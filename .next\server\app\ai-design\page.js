/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/ai-design/page";
exports.ids = ["app/ai-design/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fai-design%2Fpage&page=%2Fai-design%2Fpage&appPaths=%2Fai-design%2Fpage&pagePath=private-next-app-dir%2Fai-design%2Fpage.tsx&appDir=C%3A%5CFlame%20workspace%5Cmorderm-haven%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CFlame%20workspace%5Cmorderm-haven&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fai-design%2Fpage&page=%2Fai-design%2Fpage&appPaths=%2Fai-design%2Fpage&pagePath=private-next-app-dir%2Fai-design%2Fpage.tsx&appDir=C%3A%5CFlame%20workspace%5Cmorderm-haven%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CFlame%20workspace%5Cmorderm-haven&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'ai-design',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/ai-design/page.tsx */ \"(rsc)/./src/app/ai-design/page.tsx\")), \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/ai-design/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/ai-design/page\",\n        pathname: \"/ai-design\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fai-design%2Fpage&page=%2Fai-design%2Fpage&appPaths=%2Fai-design%2Fpage&pagePath=private-next-app-dir%2Fai-design%2Fpage.tsx&appDir=C%3A%5CFlame%20workspace%5Cmorderm-haven%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CFlame%20workspace%5Cmorderm-haven&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Csrc%5C%5Capp%5C%5Cai-design%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Csrc%5C%5Capp%5C%5Cai-design%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/ai-design/page.tsx */ \"(ssr)/./src/app/ai-design/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNGbGFtZSUyMHdvcmtzcGFjZSU1QyU1Q21vcmRlcm0taGF2ZW4lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNhaS1kZXNpZ24lNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0tBQXNHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbW9kZXJuLWhhdmVuLz81YjI2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcRmxhbWUgd29ya3NwYWNlXFxcXG1vcmRlcm0taGF2ZW5cXFxcc3JjXFxcXGFwcFxcXFxhaS1kZXNpZ25cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Csrc%5C%5Capp%5C%5Cai-design%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/ai-design/page.tsx":
/*!************************************!*\
  !*** ./src/app/ai-design/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AIDesign)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction AIDesign() {\n    const [prompt, setPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [generatedImages, setGeneratedImages] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    // Interior design keywords to validate prompts\n    const interiorKeywords = [\n        \"kitchen\",\n        \"bedroom\",\n        \"living room\",\n        \"bathroom\",\n        \"dining room\",\n        \"office\",\n        \"closet\",\n        \"interior\",\n        \"home\",\n        \"house\",\n        \"apartment\",\n        \"room\",\n        \"space\",\n        \"design\",\n        \"furniture\",\n        \"cabinet\",\n        \"counter\",\n        \"sofa\",\n        \"chair\",\n        \"table\",\n        \"bed\",\n        \"shelf\",\n        \"storage\",\n        \"modern\",\n        \"contemporary\",\n        \"traditional\",\n        \"minimalist\",\n        \"luxury\",\n        \"cozy\",\n        \"lighting\",\n        \"window\",\n        \"wall\",\n        \"floor\",\n        \"ceiling\",\n        \"decor\",\n        \"renovation\"\n    ];\n    const validatePrompt = (inputPrompt)=>{\n        const lowerPrompt = inputPrompt.toLowerCase();\n        return interiorKeywords.some((keyword)=>lowerPrompt.includes(keyword));\n    };\n    const enhancePrompt = (userPrompt)=>{\n        // Add interior design context to ensure relevant results\n        const baseContext = \"Interior design, home decor, architectural photography, professional lighting, high quality, detailed, \";\n        // Check if prompt already contains interior context\n        const lowerPrompt = userPrompt.toLowerCase();\n        if (lowerPrompt.includes(\"interior\") || lowerPrompt.includes(\"room\") || lowerPrompt.includes(\"kitchen\") || lowerPrompt.includes(\"bedroom\")) {\n            return `${baseContext}${userPrompt}`;\n        }\n        return `${baseContext}modern ${userPrompt} interior design`;\n    };\n    const generateImage = async ()=>{\n        if (!prompt.trim()) {\n            setError(\"Please enter a design description\");\n            return;\n        }\n        if (!validatePrompt(prompt)) {\n            setError(\"Please describe an interior design or home-related concept (e.g., 'modern kitchen', 'cozy bedroom', 'minimalist living room')\");\n            return;\n        }\n        setIsGenerating(true);\n        setError(\"\");\n        try {\n            const enhancedPrompt = enhancePrompt(prompt);\n            const response = await fetch(\"/api/generate-image\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    prompt: enhancedPrompt\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to generate image\");\n            }\n            const data = await response.json();\n            const newImage = {\n                url: data.imageUrl,\n                prompt: prompt,\n                timestamp: Date.now()\n            };\n            setGeneratedImages((prev)=>[\n                    newImage,\n                    ...prev\n                ]);\n            setPrompt(\"\");\n        } catch (err) {\n            setError(\"Failed to generate image. Please try again.\");\n            console.error(\"Error generating image:\", err);\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !isGenerating) {\n            generateImage();\n        }\n    };\n    const samplePrompts = [\n        \"Modern minimalist kitchen with white cabinets and marble countertops\",\n        \"Cozy bedroom with warm lighting and natural wood furniture\",\n        \"Contemporary living room with large windows and neutral colors\",\n        \"Luxury bathroom with marble tiles and gold fixtures\",\n        \"Scandinavian dining room with wooden table and pendant lights\",\n        \"Walk-in closet with organized storage and elegant lighting\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"bg-white shadow-sm border-b border-forest-green-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/\",\n                                    className: \"text-2xl font-bold text-forest-green-600\",\n                                    children: \"Modern Haven\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:block\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-10 flex items-baseline space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"Home\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/#about\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"About\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/#services\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"Services\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/gallery\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"Gallery\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/ai-design\",\n                                            className: \"bg-gold-500 text-white px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"AI Design\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/#contact\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"Contact\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative bg-gradient-to-br from-forest-green-50 to-white py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-5xl font-bold text-forest-green-700 mb-6\",\n                                children: [\n                                    \"AI \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gold-500\",\n                                        children: \"Design Studio\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 18\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-forest-green-600 mb-8 max-w-3xl mx-auto\",\n                                children: \"Bring your interior design ideas to life with our AI-powered design generator. Describe your vision and watch it transform into stunning visual concepts.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-forest-green-50 rounded-lg p-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold text-forest-green-700 mb-6\",\n                                                children: \"Create Your Design\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"prompt\",\n                                                                className: \"block text-sm font-medium text-forest-green-700 mb-2\",\n                                                                children: \"Describe your interior design vision\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                lineNumber: 160,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                id: \"prompt\",\n                                                                value: prompt,\n                                                                onChange: (e)=>setPrompt(e.target.value),\n                                                                onKeyPress: handleKeyPress,\n                                                                placeholder: \"e.g., Modern kitchen with white cabinets, marble countertops, and stainless steel appliances...\",\n                                                                className: \"w-full px-4 py-3 border border-forest-green-200 rounded-lg focus:ring-2 focus:ring-gold-500 focus:border-transparent resize-none\",\n                                                                rows: 6,\n                                                                disabled: isGenerating\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                lineNumber: 163,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 159,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-red-600 text-sm bg-red-50 p-3 rounded-lg\",\n                                                        children: error\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: generateImage,\n                                                        disabled: isGenerating || !prompt.trim(),\n                                                        className: \"w-full bg-gold-500 text-white py-3 px-6 rounded-lg font-semibold hover:bg-gold-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition duration-300\",\n                                                        children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                    lineNumber: 188,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Generating Design...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                            lineNumber: 187,\n                                                            columnNumber: 23\n                                                        }, this) : \"Generate Design\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white border border-forest-green-200 rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-4\",\n                                                children: \"\\uD83D\\uDCA1 Try these sample prompts:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: samplePrompts.map((samplePrompt, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setPrompt(samplePrompt),\n                                                        className: \"w-full text-left p-3 bg-forest-green-50 border border-forest-green-200 rounded-lg hover:border-gold-500 hover:bg-gold-50 transition duration-200\",\n                                                        disabled: isGenerating,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-forest-green-600\",\n                                                            children: samplePrompt\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                            lineNumber: 211,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, index, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 205,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-forest-green-600 rounded-lg p-6 text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold mb-4\",\n                                                children: \"\\uD83C\\uDFAF Tips for Better Results\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-2 text-forest-green-100 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Be specific about room types (kitchen, bedroom, living room, etc.)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Include style preferences (modern, traditional, minimalist, luxury)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Mention colors, materials, and lighting preferences\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Describe the mood you want to create (cozy, elegant, bright, etc.)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Include furniture and decor elements you envision\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-forest-green-50 rounded-lg p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-forest-green-700 mb-6\",\n                                            children: generatedImages.length > 0 ? \"Your Generated Designs\" : \"Generated Designs Will Appear Here\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 17\n                                        }, this),\n                                        generatedImages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-6xl mb-4\",\n                                                    children: \"\\uD83C\\uDFA8\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-forest-green-600 mb-4\",\n                                                    children: \"Start by describing your interior design vision in the left panel.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-forest-green-500\",\n                                                    children: 'Your AI-generated designs will appear here once you click \"Generate Design\".'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6 max-h-[800px] overflow-y-auto\",\n                                            children: generatedImages.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white rounded-lg shadow-lg overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative h-64\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                                src: image.url,\n                                                                alt: `Generated design: ${image.prompt}`,\n                                                                fill: true,\n                                                                className: \"object-cover\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                            lineNumber: 251,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-forest-green-600 text-sm mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"Prompt:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                            lineNumber: 261,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \" \",\n                                                                        image.prompt\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                    lineNumber: 260,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-500 text-xs\",\n                                                                    children: [\n                                                                        \"Generated on \",\n                                                                        new Date(image.timestamp).toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                    lineNumber: 263,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-3 flex space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>{\n                                                                                const link = document.createElement(\"a\");\n                                                                                link.href = image.url;\n                                                                                link.download = `modern-haven-design-${image.timestamp}.png`;\n                                                                                link.click();\n                                                                            },\n                                                                            className: \"text-xs bg-gold-500 text-white px-3 py-1 rounded hover:bg-gold-600 transition duration-200\",\n                                                                            children: \"Download\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                            lineNumber: 267,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>setPrompt(image.prompt),\n                                                                            className: \"text-xs bg-forest-green-600 text-white px-3 py-1 rounded hover:bg-forest-green-700 transition duration-200\",\n                                                                            children: \"Use Prompt\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                            lineNumber: 278,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                    lineNumber: 266,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                lineNumber: 146,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-forest-green-800 text-white py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-1 md:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-gold-400 mb-4\",\n                                            children: \"Modern Haven\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-forest-green-200 mb-4\",\n                                            children: \"Creating beautiful, functional spaces that reflect your unique style and enhance your lifestyle. Your dream home is just a consultation away.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gold-400 hover:text-gold-300\",\n                                                    children: \"\\uD83D\\uDCD8\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gold-400 hover:text-gold-300\",\n                                                    children: \"\\uD83D\\uDCF7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gold-400 hover:text-gold-300\",\n                                                    children: \"\\uD83D\\uDC26\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gold-400 hover:text-gold-300\",\n                                                    children: \"\\uD83D\\uDCBC\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold text-gold-400 mb-4\",\n                                            children: \"Quick Links\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-forest-green-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"Home\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/#about\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"About\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/#services\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"Services\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/gallery\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"Gallery\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/ai-design\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"AI Design\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/#contact\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"Contact\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold text-gold-400 mb-4\",\n                                            children: \"AI Features\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-forest-green-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Interior Design Generation\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Room Visualization\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Style Exploration\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Design Inspiration\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Custom Concepts\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                            lineNumber: 299,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-forest-green-700 mt-8 pt-8 text-center text-forest-green-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"\\xa9 2024 Modern Haven Interior Design. All rights reserved.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                            lineNumber: 335,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                    lineNumber: 298,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                lineNumber: 297,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/ai-design/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"9974e75019d9\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbW9kZXJuLWhhdmVuLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz80ZmNjIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiOTk3NGU3NTAxOWQ5XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/ai-design/page.tsx":
/*!************************************!*\
  !*** ./src/app/ai-design/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Flame workspace\morderm-haven\src\app\ai-design\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Flame workspace\morderm-haven\src\app\ai-design\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Modern Haven - Interior Design\",\n    description: \"Transform your space with Modern Haven's expert interior design services. Creating beautiful, functional spaces with forest green, gold, and white elegance.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTUE7QUFGaUI7QUFJaEIsTUFBTUMsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1I7SUFDQSxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBV1QsK0pBQWU7c0JBQUdLOzs7Ozs7Ozs7OztBQUd6QyIsInNvdXJjZXMiOlsid2VicGFjazovL21vZGVybi1oYXZlbi8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tIFwibmV4dC9mb250L2dvb2dsZVwiO1xuaW1wb3J0IFwiLi9nbG9iYWxzLmNzc1wiO1xuXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogW1wibGF0aW5cIl0gfSk7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiBcIk1vZGVybiBIYXZlbiAtIEludGVyaW9yIERlc2lnblwiLFxuICBkZXNjcmlwdGlvbjogXCJUcmFuc2Zvcm0geW91ciBzcGFjZSB3aXRoIE1vZGVybiBIYXZlbidzIGV4cGVydCBpbnRlcmlvciBkZXNpZ24gc2VydmljZXMuIENyZWF0aW5nIGJlYXV0aWZ1bCwgZnVuY3Rpb25hbCBzcGFjZXMgd2l0aCBmb3Jlc3QgZ3JlZW4sIGdvbGQsIGFuZCB3aGl0ZSBlbGVnYW5jZS5cIixcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IFJlYWRvbmx5PHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0+KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+e2NoaWxkcmVufTwvYm9keT5cbiAgICA8L2h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiaW50ZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fai-design%2Fpage&page=%2Fai-design%2Fpage&appPaths=%2Fai-design%2Fpage&pagePath=private-next-app-dir%2Fai-design%2Fpage.tsx&appDir=C%3A%5CFlame%20workspace%5Cmorderm-haven%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CFlame%20workspace%5Cmorderm-haven&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();