/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/ai-design/page";
exports.ids = ["app/ai-design/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fai-design%2Fpage&page=%2Fai-design%2Fpage&appPaths=%2Fai-design%2Fpage&pagePath=private-next-app-dir%2Fai-design%2Fpage.tsx&appDir=C%3A%5CFlame%20workspace%5Cmorderm-haven%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CFlame%20workspace%5Cmorderm-haven&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fai-design%2Fpage&page=%2Fai-design%2Fpage&appPaths=%2Fai-design%2Fpage&pagePath=private-next-app-dir%2Fai-design%2Fpage.tsx&appDir=C%3A%5CFlame%20workspace%5Cmorderm-haven%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CFlame%20workspace%5Cmorderm-haven&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'ai-design',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/ai-design/page.tsx */ \"(rsc)/./src/app/ai-design/page.tsx\")), \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/ai-design/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/ai-design/page\",\n        pathname: \"/ai-design\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fai-design%2Fpage&page=%2Fai-design%2Fpage&appPaths=%2Fai-design%2Fpage&pagePath=private-next-app-dir%2Fai-design%2Fpage.tsx&appDir=C%3A%5CFlame%20workspace%5Cmorderm-haven%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CFlame%20workspace%5Cmorderm-haven&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Csrc%5C%5Capp%5C%5Cai-design%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Csrc%5C%5Capp%5C%5Cai-design%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/ai-design/page.tsx */ \"(ssr)/./src/app/ai-design/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNGbGFtZSUyMHdvcmtzcGFjZSU1QyU1Q21vcmRlcm0taGF2ZW4lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNhaS1kZXNpZ24lNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0tBQXNHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbW9kZXJuLWhhdmVuLz81YjI2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcRmxhbWUgd29ya3NwYWNlXFxcXG1vcmRlcm0taGF2ZW5cXFxcc3JjXFxcXGFwcFxcXFxhaS1kZXNpZ25cXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Csrc%5C%5Capp%5C%5Cai-design%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/ai-design/page.tsx":
/*!************************************!*\
  !*** ./src/app/ai-design/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AIDesign)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction AIDesign() {\n    const [prompt, setPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [generatedContent, setGeneratedContent] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [suggestion, setSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [uploadedImage, setUploadedImage] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    // Interior design keywords to validate prompts\n    const interiorKeywords = [\n        \"kitchen\",\n        \"bedroom\",\n        \"living room\",\n        \"bathroom\",\n        \"dining room\",\n        \"office\",\n        \"closet\",\n        \"interior\",\n        \"home\",\n        \"house\",\n        \"apartment\",\n        \"room\",\n        \"space\",\n        \"design\",\n        \"furniture\",\n        \"cabinet\",\n        \"counter\",\n        \"sofa\",\n        \"chair\",\n        \"table\",\n        \"bed\",\n        \"shelf\",\n        \"storage\",\n        \"modern\",\n        \"contemporary\",\n        \"traditional\",\n        \"minimalist\",\n        \"luxury\",\n        \"cozy\",\n        \"lighting\",\n        \"window\",\n        \"wall\",\n        \"floor\",\n        \"ceiling\",\n        \"decor\",\n        \"renovation\"\n    ];\n    const validatePrompt = (inputPrompt)=>{\n        const lowerPrompt = inputPrompt.toLowerCase();\n        return interiorKeywords.some((keyword)=>lowerPrompt.includes(keyword));\n    };\n    const enhancePrompt = (userPrompt)=>{\n        // Add interior design context to ensure relevant results\n        const baseContext = \"Interior design, home decor, architectural photography, professional lighting, high quality, detailed, \";\n        // Check if prompt already contains interior context\n        const lowerPrompt = userPrompt.toLowerCase();\n        if (lowerPrompt.includes(\"interior\") || lowerPrompt.includes(\"room\") || lowerPrompt.includes(\"kitchen\") || lowerPrompt.includes(\"bedroom\")) {\n            return `${baseContext}${userPrompt}`;\n        }\n        return `${baseContext}modern ${userPrompt} interior design`;\n    };\n    const handleImageUpload = (event)=>{\n        const file = event.target.files?.[0];\n        if (file) {\n            // Validate file type\n            if (!file.type.startsWith(\"image/\")) {\n                setError(\"Please upload a valid image file\");\n                return;\n            }\n            // Validate file size (max 5MB)\n            if (file.size > 5 * 1024 * 1024) {\n                setError(\"Image size should be less than 5MB\");\n                return;\n            }\n            const reader = new FileReader();\n            reader.onload = (e)=>{\n                const preview = e.target?.result;\n                setUploadedImage({\n                    file,\n                    preview\n                });\n                setError(\"\");\n            };\n            reader.readAsDataURL(file);\n        }\n    };\n    const removeUploadedImage = ()=>{\n        setUploadedImage(null);\n    };\n    const generateImage = async ()=>{\n        if (!prompt.trim()) {\n            setError(\"Please enter a design description\");\n            return;\n        }\n        if (!validatePrompt(prompt)) {\n            setError(\"Please describe an interior design or home-related concept (e.g., 'modern kitchen', 'cozy bedroom', 'minimalist living room')\");\n            return;\n        }\n        setIsGenerating(true);\n        setError(\"\");\n        setSuggestion(\"\");\n        try {\n            const enhancedPrompt = enhancePrompt(prompt);\n            const response = await fetch(\"/api/generate-image\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    prompt: enhancedPrompt,\n                    uploadedImage: uploadedImage?.preview\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to generate image\");\n            }\n            const data = await response.json();\n            const newContent = {\n                url: data.imageUrl,\n                textDescription: data.textDescription,\n                prompt: prompt,\n                timestamp: Date.now(),\n                message: data.message\n            };\n            setGeneratedContent((prev)=>[\n                    newContent,\n                    ...prev\n                ]);\n            setPrompt(\"\");\n        } catch (err) {\n            // Parse error response\n            let errorMessage = \"Failed to generate image. Please try again.\";\n            let suggestionMessage = \"\";\n            if (err.response?.data) {\n                errorMessage = err.response.data.error || errorMessage;\n                suggestionMessage = err.response.data.suggestion || \"\";\n            } else if (err.message) {\n                errorMessage = err.message;\n            }\n            setError(errorMessage);\n            setSuggestion(suggestionMessage);\n            console.error(\"Error generating image:\", err);\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !isGenerating) {\n            generateImage();\n        }\n    };\n    const samplePrompts = [\n        \"Modern minimalist kitchen with white cabinets and marble countertops\",\n        \"Cozy bedroom with warm lighting and natural wood furniture\",\n        \"Contemporary living room with large windows and neutral colors\",\n        \"Luxury bathroom with marble tiles and gold fixtures\",\n        \"Scandinavian dining room with wooden table and pendant lights\",\n        \"Walk-in closet with organized storage and elegant lighting\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"bg-white shadow-sm border-b border-forest-green-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        src: \"/images/modern-haven-logo.png\",\n                                        alt: \"Modern Haven Logo\",\n                                        width: 120,\n                                        height: 40,\n                                        className: \"h-10 w-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:block\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-10 flex items-baseline space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"Home\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 182,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/#about\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"About\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/#services\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"Services\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/gallery\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"Gallery\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/ai-design\",\n                                            className: \"bg-gold-500 text-white px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"AI Design\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/#contact\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"Contact\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative bg-gradient-to-br from-forest-green-50 to-white py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-5xl font-bold text-forest-green-700 mb-6\",\n                                children: [\n                                    \"AI \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gold-500\",\n                                        children: \"Design Studio\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 18\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-forest-green-600 mb-8 max-w-3xl mx-auto\",\n                                children: \"Bring your interior design ideas to life with our advanced AI technology. Describe your vision and upload reference images to get stunning design concepts.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                lineNumber: 195,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-forest-green-50 rounded-lg p-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold text-forest-green-700 mb-6\",\n                                                children: \"Create Your Design\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"prompt\",\n                                                                className: \"block text-sm font-medium text-forest-green-700 mb-2\",\n                                                                children: \"Describe your interior design vision\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                id: \"prompt\",\n                                                                value: prompt,\n                                                                onChange: (e)=>setPrompt(e.target.value),\n                                                                onKeyPress: handleKeyPress,\n                                                                placeholder: \"e.g., Modern kitchen with white cabinets, marble countertops, and stainless steel appliances...\",\n                                                                className: \"w-full px-4 py-3 border border-forest-green-200 rounded-lg focus:ring-2 focus:ring-gold-500 focus:border-transparent resize-none text-forest-green-800 placeholder-forest-green-400 bg-white\",\n                                                                rows: 6,\n                                                                disabled: isGenerating\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                lineNumber: 227,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-red-600 text-sm bg-red-50 p-3 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: error\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                lineNumber: 241,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            suggestion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"mt-2 text-blue-600 bg-blue-50 p-2 rounded border-l-4 border-blue-400\",\n                                                                children: [\n                                                                    \"\\uD83D\\uDCA1 \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Suggestion:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                        lineNumber: 244,\n                                                                        columnNumber: 30\n                                                                    }, this),\n                                                                    \" \",\n                                                                    suggestion\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                lineNumber: 243,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: generateImage,\n                                                        disabled: isGenerating || !prompt.trim(),\n                                                        className: \"w-full bg-gold-500 text-white py-3 px-6 rounded-lg font-semibold hover:bg-gold-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition duration-300\",\n                                                        children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                    lineNumber: 257,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Generating Design...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                            lineNumber: 256,\n                                                            columnNumber: 23\n                                                        }, this) : \"Generate Design\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white border border-forest-green-200 rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-4\",\n                                                children: \"\\uD83D\\uDDBC️ Upload Reference Image (Optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 17\n                                            }, this),\n                                            !uploadedImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-2 border-dashed border-forest-green-300 rounded-lg p-6 text-center hover:border-gold-500 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"file\",\n                                                        accept: \"image/*\",\n                                                        onChange: handleImageUpload,\n                                                        className: \"hidden\",\n                                                        id: \"image-upload\",\n                                                        disabled: isGenerating\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"image-upload\",\n                                                        className: \"cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-4xl mb-2\",\n                                                                children: \"\\uD83D\\uDCF7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                lineNumber: 284,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-forest-green-600 mb-2\",\n                                                                children: \"Click to upload an image\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                lineNumber: 285,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-forest-green-500\",\n                                                                children: \"Upload a room photo for AI to redesign or use as inspiration\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                lineNumber: 286,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400 mt-2\",\n                                                                children: \"Supports JPG, PNG (max 5MB)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                lineNumber: 289,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative h-48 rounded-lg overflow-hidden\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                            src: uploadedImage.preview,\n                                                            alt: \"Uploaded reference\",\n                                                            fill: true,\n                                                            className: \"object-cover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                            lineNumber: 297,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: removeUploadedImage,\n                                                        className: \"absolute top-2 right-2 bg-red-500 text-white rounded-full w-8 h-8 flex items-center justify-center hover:bg-red-600 transition-colors\",\n                                                        disabled: isGenerating,\n                                                        children: \"✕\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-forest-green-600 mt-2\",\n                                                        children: \"Reference image uploaded. The AI will use this for inspiration.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 311,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white border border-forest-green-200 rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-4\",\n                                                children: \"\\uD83D\\uDCA1 Try these sample prompts:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: samplePrompts.map((samplePrompt, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setPrompt(samplePrompt),\n                                                        className: \"w-full text-left p-3 bg-forest-green-50 border border-forest-green-200 rounded-lg hover:border-gold-500 hover:bg-gold-50 transition duration-200\",\n                                                        disabled: isGenerating,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-forest-green-600\",\n                                                            children: samplePrompt\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                            lineNumber: 331,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, index, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-forest-green-600 rounded-lg p-6 text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold mb-4\",\n                                                children: \"\\uD83C\\uDFAF Tips for Better Results\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-2 text-forest-green-100 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Be specific about room types (kitchen, bedroom, living room, etc.)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Include style preferences (modern, traditional, minimalist, luxury)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Mention colors, materials, and lighting preferences\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Describe the mood you want to create (cozy, elegant, bright, etc.)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Include furniture and decor elements you envision\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Upload reference images to help the AI understand your vision\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 340,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-forest-green-50 rounded-lg p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-forest-green-700 mb-6\",\n                                            children: generatedContent.length > 0 ? \"Your Generated Designs\" : \"Generated Designs Will Appear Here\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 17\n                                        }, this),\n                                        generatedContent.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-6xl mb-4\",\n                                                    children: \"\\uD83C\\uDFA8\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-forest-green-600 mb-4\",\n                                                    children: \"Start by describing your interior design vision in the left panel.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-forest-green-500\",\n                                                    children: 'Your AI-generated designs will appear here once you click \"Generate Design\".'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6 h-[500px] overflow-y-auto\",\n                                            children: generatedContent.map((content, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white rounded-lg shadow-lg overflow-hidden\",\n                                                    children: [\n                                                        content.url ? // Display image if available\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative h-64\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                                src: content.url,\n                                                                alt: `Generated design: ${content.prompt}`,\n                                                                fill: true,\n                                                                className: \"object-cover\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 27\n                                                        }, this) : // Display text description from Gemini\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-6 bg-gradient-to-br from-gold-50 to-forest-green-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-4xl mb-4 text-center\",\n                                                                    children: \"\\uD83C\\uDFE0✨\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                    lineNumber: 385,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-lg font-semibold text-forest-green-700 mb-3\",\n                                                                    children: \"AI Design Concept\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                    lineNumber: 386,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-forest-green-600 text-sm leading-relaxed\",\n                                                                    children: content.textDescription\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                    lineNumber: 389,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                content.message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-3 p-3 bg-blue-50 border-l-4 border-blue-400 rounded\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-blue-700 text-sm\",\n                                                                        children: [\n                                                                            \"\\uD83D\\uDCA1 \",\n                                                                            content.message\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                        lineNumber: 394,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                    lineNumber: 393,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                            lineNumber: 384,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-forest-green-600 text-sm mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"Prompt:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                            lineNumber: 403,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \" \",\n                                                                        content.prompt\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                    lineNumber: 402,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-500 text-xs mb-3\",\n                                                                    children: [\n                                                                        \"Generated on \",\n                                                                        new Date(content.timestamp).toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                    lineNumber: 405,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex space-x-2\",\n                                                                    children: [\n                                                                        content.url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>{\n                                                                                const link = document.createElement(\"a\");\n                                                                                link.href = content.url;\n                                                                                link.download = `modern-haven-design-${content.timestamp}.png`;\n                                                                                link.click();\n                                                                            },\n                                                                            className: \"text-xs bg-gold-500 text-white px-3 py-1 rounded hover:bg-gold-600 transition duration-200\",\n                                                                            children: \"Download Image\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                            lineNumber: 410,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        content.textDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>{\n                                                                                navigator.clipboard.writeText(content.textDescription);\n                                                                                alert(\"Description copied to clipboard!\");\n                                                                            },\n                                                                            className: \"text-xs bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600 transition duration-200\",\n                                                                            children: \"Copy Description\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                            lineNumber: 423,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>setPrompt(content.prompt),\n                                                                            className: \"text-xs bg-forest-green-600 text-white px-3 py-1 rounded hover:bg-forest-green-700 transition duration-200\",\n                                                                            children: \"Use Prompt\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                            lineNumber: 433,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                    lineNumber: 408,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                            lineNumber: 401,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                    lineNumber: 211,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                lineNumber: 210,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-forest-green-800 text-white py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-1 md:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-gold-400 mb-4\",\n                                            children: \"Modern Haven\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 456,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-forest-green-200 mb-4\",\n                                            children: \"Creating beautiful, functional spaces that reflect your unique style and enhance your lifestyle. Your dream home is just a consultation away.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gold-400 hover:text-gold-300\",\n                                                    children: \"\\uD83D\\uDCD8\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 462,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gold-400 hover:text-gold-300\",\n                                                    children: \"\\uD83D\\uDCF7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gold-400 hover:text-gold-300\",\n                                                    children: \"\\uD83D\\uDC26\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gold-400 hover:text-gold-300\",\n                                                    children: \"\\uD83D\\uDCBC\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                    lineNumber: 455,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold text-gold-400 mb-4\",\n                                            children: \"Quick Links\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-forest-green-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"Home\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 471,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/#about\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"About\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 472,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/#services\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"Services\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 473,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/gallery\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"Gallery\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/ai-design\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"AI Design\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 475,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/#contact\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"Contact\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 476,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold text-gold-400 mb-4\",\n                                            children: \"AI Features\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-forest-green-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Interior Design Generation\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 482,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Room Visualization\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Style Exploration\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Design Inspiration\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Custom Concepts\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                    lineNumber: 479,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                            lineNumber: 454,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-forest-green-700 mt-8 pt-8 text-center text-forest-green-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"\\xa9 2024 Modern Haven Interior Design. All rights reserved.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                lineNumber: 491,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                            lineNumber: 490,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                    lineNumber: 453,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                lineNumber: 452,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n        lineNumber: 164,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/ai-design/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"9974e75019d9\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbW9kZXJuLWhhdmVuLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz80ZmNjIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiOTk3NGU3NTAxOWQ5XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/ai-design/page.tsx":
/*!************************************!*\
  !*** ./src/app/ai-design/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Flame workspace\morderm-haven\src\app\ai-design\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Flame workspace\morderm-haven\src\app\ai-design\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Modern Haven - Interior Design\",\n    description: \"Transform your space with Modern Haven's expert interior design services. Creating beautiful, functional spaces with forest green, gold, and white elegance.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTUE7QUFGaUI7QUFJaEIsTUFBTUMsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1I7SUFDQSxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBV1QsK0pBQWU7c0JBQUdLOzs7Ozs7Ozs7OztBQUd6QyIsInNvdXJjZXMiOlsid2VicGFjazovL21vZGVybi1oYXZlbi8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tIFwibmV4dC9mb250L2dvb2dsZVwiO1xuaW1wb3J0IFwiLi9nbG9iYWxzLmNzc1wiO1xuXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogW1wibGF0aW5cIl0gfSk7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiBcIk1vZGVybiBIYXZlbiAtIEludGVyaW9yIERlc2lnblwiLFxuICBkZXNjcmlwdGlvbjogXCJUcmFuc2Zvcm0geW91ciBzcGFjZSB3aXRoIE1vZGVybiBIYXZlbidzIGV4cGVydCBpbnRlcmlvciBkZXNpZ24gc2VydmljZXMuIENyZWF0aW5nIGJlYXV0aWZ1bCwgZnVuY3Rpb25hbCBzcGFjZXMgd2l0aCBmb3Jlc3QgZ3JlZW4sIGdvbGQsIGFuZCB3aGl0ZSBlbGVnYW5jZS5cIixcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IFJlYWRvbmx5PHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0+KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+e2NoaWxkcmVufTwvYm9keT5cbiAgICA8L2h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiaW50ZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fai-design%2Fpage&page=%2Fai-design%2Fpage&appPaths=%2Fai-design%2Fpage&pagePath=private-next-app-dir%2Fai-design%2Fpage.tsx&appDir=C%3A%5CFlame%20workspace%5Cmorderm-haven%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CFlame%20workspace%5Cmorderm-haven&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();