"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-image/route";
exports.ids = ["app/api/generate-image/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=C%3A%5CFlame%20workspace%5Cmorderm-haven%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CFlame%20workspace%5Cmorderm-haven&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=C%3A%5CFlame%20workspace%5Cmorderm-haven%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CFlame%20workspace%5Cmorderm-haven&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Flame_workspace_morderm_haven_src_app_api_generate_image_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-image/route.ts */ \"(rsc)/./src/app/api/generate-image/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-image/route\",\n        pathname: \"/api/generate-image\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-image/route\"\n    },\n    resolvedPagePath: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\api\\\\generate-image\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Flame_workspace_morderm_haven_src_app_api_generate_image_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/generate-image/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhcGklMkZnZW5lcmF0ZS1pbWFnZSUyRnJvdXRlJnBhZ2U9JTJGYXBpJTJGZ2VuZXJhdGUtaW1hZ2UlMkZyb3V0ZSZhcHBQYXRocz0mcGFnZVBhdGg9cHJpdmF0ZS1uZXh0LWFwcC1kaXIlMkZhcGklMkZnZW5lcmF0ZS1pbWFnZSUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDRmxhbWUlMjB3b3Jrc3BhY2UlNUNtb3JkZXJtLWhhdmVuJTVDc3JjJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1DJTNBJTVDRmxhbWUlMjB3b3Jrc3BhY2UlNUNtb3JkZXJtLWhhdmVuJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFBc0c7QUFDdkM7QUFDYztBQUMyQjtBQUN4RztBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsZ0hBQW1CO0FBQzNDO0FBQ0EsY0FBYyx5RUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsWUFBWTtBQUNaLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxRQUFRLGlFQUFpRTtBQUN6RTtBQUNBO0FBQ0EsV0FBVyw0RUFBVztBQUN0QjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ3VIOztBQUV2SCIsInNvdXJjZXMiOlsid2VicGFjazovL21vZGVybi1oYXZlbi8/MzA2ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBSb3V0ZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvYXBwLXJvdXRlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IHBhdGNoRmV0Y2ggYXMgX3BhdGNoRmV0Y2ggfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9saWIvcGF0Y2gtZmV0Y2hcIjtcbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCJDOlxcXFxGbGFtZSB3b3Jrc3BhY2VcXFxcbW9yZGVybS1oYXZlblxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxnZW5lcmF0ZS1pbWFnZVxcXFxyb3V0ZS50c1wiO1xuLy8gV2UgaW5qZWN0IHRoZSBuZXh0Q29uZmlnT3V0cHV0IGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCBuZXh0Q29uZmlnT3V0cHV0ID0gXCJcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvZ2VuZXJhdGUtaW1hZ2Uvcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9nZW5lcmF0ZS1pbWFnZVwiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvZ2VuZXJhdGUtaW1hZ2Uvcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCJDOlxcXFxGbGFtZSB3b3Jrc3BhY2VcXFxcbW9yZGVybS1oYXZlblxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxnZW5lcmF0ZS1pbWFnZVxcXFxyb3V0ZS50c1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHJlcXVlc3RBc3luY1N0b3JhZ2UsIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzIH0gPSByb3V0ZU1vZHVsZTtcbmNvbnN0IG9yaWdpbmFsUGF0aG5hbWUgPSBcIi9hcGkvZ2VuZXJhdGUtaW1hZ2Uvcm91dGVcIjtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgc2VydmVySG9va3MsXG4gICAgICAgIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCByZXF1ZXN0QXN5bmNTdG9yYWdlLCBzdGF0aWNHZW5lcmF0aW9uQXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgb3JpZ2luYWxQYXRobmFtZSwgcGF0Y2hGZXRjaCwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=C%3A%5CFlame%20workspace%5Cmorderm-haven%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CFlame%20workspace%5Cmorderm-haven&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/generate-image/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/generate-image/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @google/generative-ai */ \"(rsc)/./node_modules/@google/generative-ai/dist/index.mjs\");\n\n\nasync function POST(request) {\n    try {\n        const { prompt, uploadedImage } = await request.json();\n        if (!prompt) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Prompt is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Get the Google AI API key from environment variables\n        const apiKey = process.env.GOOGLE_AI_API_KEY;\n        if (!apiKey) {\n            console.error(\"GOOGLE_AI_API_KEY not found in environment variables\");\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"API configuration error\"\n            }, {\n                status: 500\n            });\n        }\n        // Interior design validation - ensure prompt is related to interior design\n        const interiorKeywords = [\n            \"kitchen\",\n            \"bedroom\",\n            \"living room\",\n            \"bathroom\",\n            \"dining room\",\n            \"office\",\n            \"closet\",\n            \"interior\",\n            \"home\",\n            \"house\",\n            \"apartment\",\n            \"room\",\n            \"space\",\n            \"design\",\n            \"furniture\",\n            \"cabinet\",\n            \"counter\",\n            \"sofa\",\n            \"chair\",\n            \"table\",\n            \"bed\",\n            \"shelf\",\n            \"storage\",\n            \"modern\",\n            \"contemporary\",\n            \"traditional\",\n            \"minimalist\",\n            \"luxury\",\n            \"cozy\",\n            \"lighting\",\n            \"window\",\n            \"wall\",\n            \"floor\",\n            \"ceiling\",\n            \"decor\",\n            \"renovation\"\n        ];\n        const lowerPrompt = prompt.toLowerCase();\n        const isInteriorRelated = interiorKeywords.some((keyword)=>lowerPrompt.includes(keyword));\n        if (!isInteriorRelated) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Please provide an interior design or home-related prompt\"\n            }, {\n                status: 400\n            });\n        }\n        // Initialize Google AI client\n        const genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__.GoogleGenerativeAI(apiKey);\n        try {\n            // Use Gemini 2.0 Flash Preview Image Generation\n            const model = genAI.getGenerativeModel({\n                model: \"gemini-2.0-flash-preview-image-generation\"\n            });\n            // Enhanced prompt for better interior design results\n            const enhancedPrompt = `Create a high-quality, professional interior design image: ${prompt}. Style: photorealistic, architectural photography, professional lighting, high resolution, detailed textures, modern design aesthetic.`;\n            console.log(\"Generating image with Gemini 2.0 Flash Preview Image Generation...\");\n            // Prepare the content array\n            const contentParts = [\n                {\n                    text: enhancedPrompt\n                }\n            ];\n            // Add uploaded image if provided\n            if (uploadedImage) {\n                contentParts.push({\n                    inlineData: {\n                        mimeType: \"image/jpeg\",\n                        data: uploadedImage.split(\",\")[1] // Remove data:image/jpeg;base64, prefix\n                    }\n                });\n            }\n            // For image generation models, we need to specify that we want both image and text\n            const result = await model.generateContent({\n                contents: [\n                    {\n                        role: \"user\",\n                        parts: contentParts\n                    }\n                ],\n                generationConfig: {\n                    responseModalities: [\n                        \"IMAGE\",\n                        \"TEXT\"\n                    ]\n                }\n            });\n            const response = await result.response;\n            // Check if response has candidates with parts\n            if (response.candidates && response.candidates[0] && response.candidates[0].content) {\n                const parts = response.candidates[0].content.parts;\n                let imageData = null;\n                let textDescription = \"\";\n                // Look through all parts for image and text\n                for (const part of parts){\n                    if (part.inlineData && part.inlineData.mimeType?.startsWith(\"image/\")) {\n                        // Found image data\n                        imageData = part.inlineData.data;\n                    } else if (part.text) {\n                        // Found text description\n                        textDescription += part.text;\n                    }\n                }\n                if (imageData) {\n                    // Successfully generated image\n                    const imageUrl = `data:image/png;base64,${imageData}`;\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        imageUrl,\n                        textDescription: textDescription || \"Generated interior design image\",\n                        prompt,\n                        timestamp: Date.now(),\n                        message: \"Successfully generated interior design image\"\n                    });\n                } else if (textDescription) {\n                    // Got text description only\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        textDescription,\n                        prompt,\n                        timestamp: Date.now(),\n                        message: \"Generated detailed interior design description\"\n                    });\n                }\n            }\n            // Fallback: try to get text response\n            const text = response.text();\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                textDescription: text,\n                prompt,\n                timestamp: Date.now(),\n                message: \"Generated interior design concept\"\n            });\n        } catch (geminiError) {\n            console.error(\"Google AI API error:\", geminiError);\n            if (geminiError.message?.includes(\"quota\") || geminiError.message?.includes(\"exceeded\")) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Google AI API quota exceeded. Please check your API usage or upgrade your plan.\",\n                    suggestion: \"Visit Google AI Studio to check your API quota and usage limits.\"\n                }, {\n                    status: 402\n                });\n            }\n            if (geminiError.message?.includes(\"invalid\") || geminiError.message?.includes(\"key\")) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Invalid Google AI API key. Please check your configuration.\"\n                }, {\n                    status: 401\n                });\n            }\n            if (geminiError.message?.includes(\"rate limit\")) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Rate limit exceeded. Please wait a moment before trying again.\"\n                }, {\n                    status: 429\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Failed to generate content: ${geminiError.message || \"Unknown error\"}`\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error(\"Error in generate-image API:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error. Please try again.\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-image/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@google"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=C%3A%5CFlame%20workspace%5Cmorderm-haven%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CFlame%20workspace%5Cmorderm-haven&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();