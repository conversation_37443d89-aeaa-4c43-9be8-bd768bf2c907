"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-image/route";
exports.ids = ["app/api/generate-image/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=C%3A%5CFlame%20workspace%5Cmorderm-haven%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CFlame%20workspace%5Cmorderm-haven&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=C%3A%5CFlame%20workspace%5Cmorderm-haven%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CFlame%20workspace%5Cmorderm-haven&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Flame_workspace_morderm_haven_src_app_api_generate_image_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-image/route.ts */ \"(rsc)/./src/app/api/generate-image/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-image/route\",\n        pathname: \"/api/generate-image\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-image/route\"\n    },\n    resolvedPagePath: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\api\\\\generate-image\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Flame_workspace_morderm_haven_src_app_api_generate_image_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/generate-image/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhcGklMkZnZW5lcmF0ZS1pbWFnZSUyRnJvdXRlJnBhZ2U9JTJGYXBpJTJGZ2VuZXJhdGUtaW1hZ2UlMkZyb3V0ZSZhcHBQYXRocz0mcGFnZVBhdGg9cHJpdmF0ZS1uZXh0LWFwcC1kaXIlMkZhcGklMkZnZW5lcmF0ZS1pbWFnZSUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDRmxhbWUlMjB3b3Jrc3BhY2UlNUNtb3JkZXJtLWhhdmVuJTVDc3JjJTVDYXBwJnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMmcm9vdERpcj1DJTNBJTVDRmxhbWUlMjB3b3Jrc3BhY2UlNUNtb3JkZXJtLWhhdmVuJmlzRGV2PXRydWUmdHNjb25maWdQYXRoPXRzY29uZmlnLmpzb24mYmFzZVBhdGg9JmFzc2V0UHJlZml4PSZuZXh0Q29uZmlnT3V0cHV0PSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFBc0c7QUFDdkM7QUFDYztBQUMyQjtBQUN4RztBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsZ0hBQW1CO0FBQzNDO0FBQ0EsY0FBYyx5RUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsWUFBWTtBQUNaLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxRQUFRLGlFQUFpRTtBQUN6RTtBQUNBO0FBQ0EsV0FBVyw0RUFBVztBQUN0QjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ3VIOztBQUV2SCIsInNvdXJjZXMiOlsid2VicGFjazovL21vZGVybi1oYXZlbi8/MzA2ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBBcHBSb3V0ZVJvdXRlTW9kdWxlIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLW1vZHVsZXMvYXBwLXJvdXRlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IHBhdGNoRmV0Y2ggYXMgX3BhdGNoRmV0Y2ggfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9saWIvcGF0Y2gtZmV0Y2hcIjtcbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCJDOlxcXFxGbGFtZSB3b3Jrc3BhY2VcXFxcbW9yZGVybS1oYXZlblxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxnZW5lcmF0ZS1pbWFnZVxcXFxyb3V0ZS50c1wiO1xuLy8gV2UgaW5qZWN0IHRoZSBuZXh0Q29uZmlnT3V0cHV0IGhlcmUgc28gdGhhdCB3ZSBjYW4gdXNlIHRoZW0gaW4gdGhlIHJvdXRlXG4vLyBtb2R1bGUuXG5jb25zdCBuZXh0Q29uZmlnT3V0cHV0ID0gXCJcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvZ2VuZXJhdGUtaW1hZ2Uvcm91dGVcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL2FwaS9nZW5lcmF0ZS1pbWFnZVwiLFxuICAgICAgICBmaWxlbmFtZTogXCJyb3V0ZVwiLFxuICAgICAgICBidW5kbGVQYXRoOiBcImFwcC9hcGkvZ2VuZXJhdGUtaW1hZ2Uvcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCJDOlxcXFxGbGFtZSB3b3Jrc3BhY2VcXFxcbW9yZGVybS1oYXZlblxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxnZW5lcmF0ZS1pbWFnZVxcXFxyb3V0ZS50c1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHJlcXVlc3RBc3luY1N0b3JhZ2UsIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzIH0gPSByb3V0ZU1vZHVsZTtcbmNvbnN0IG9yaWdpbmFsUGF0aG5hbWUgPSBcIi9hcGkvZ2VuZXJhdGUtaW1hZ2Uvcm91dGVcIjtcbmZ1bmN0aW9uIHBhdGNoRmV0Y2goKSB7XG4gICAgcmV0dXJuIF9wYXRjaEZldGNoKHtcbiAgICAgICAgc2VydmVySG9va3MsXG4gICAgICAgIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2VcbiAgICB9KTtcbn1cbmV4cG9ydCB7IHJvdXRlTW9kdWxlLCByZXF1ZXN0QXN5bmNTdG9yYWdlLCBzdGF0aWNHZW5lcmF0aW9uQXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcywgb3JpZ2luYWxQYXRobmFtZSwgcGF0Y2hGZXRjaCwgIH07XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWFwcC1yb3V0ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=C%3A%5CFlame%20workspace%5Cmorderm-haven%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CFlame%20workspace%5Cmorderm-haven&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/generate-image/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/generate-image/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _huggingface_inference__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @huggingface/inference */ \"(rsc)/./node_modules/@huggingface/inference/dist/esm/index.js\");\n\n\nasync function POST(request) {\n    try {\n        const { prompt } = await request.json();\n        if (!prompt) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Prompt is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Get the API key from environment variables\n        const apiKey = process.env.HUGGINGFACE_API_KEY;\n        if (!apiKey) {\n            console.error(\"HUGGINGFACE_API_KEY not found in environment variables\");\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"API configuration error\"\n            }, {\n                status: 500\n            });\n        }\n        // Interior design validation - ensure prompt is related to interior design\n        const interiorKeywords = [\n            \"kitchen\",\n            \"bedroom\",\n            \"living room\",\n            \"bathroom\",\n            \"dining room\",\n            \"office\",\n            \"closet\",\n            \"interior\",\n            \"home\",\n            \"house\",\n            \"apartment\",\n            \"room\",\n            \"space\",\n            \"design\",\n            \"furniture\",\n            \"cabinet\",\n            \"counter\",\n            \"sofa\",\n            \"chair\",\n            \"table\",\n            \"bed\",\n            \"shelf\",\n            \"storage\",\n            \"modern\",\n            \"contemporary\",\n            \"traditional\",\n            \"minimalist\",\n            \"luxury\",\n            \"cozy\",\n            \"lighting\",\n            \"window\",\n            \"wall\",\n            \"floor\",\n            \"ceiling\",\n            \"decor\",\n            \"renovation\"\n        ];\n        const lowerPrompt = prompt.toLowerCase();\n        const isInteriorRelated = interiorKeywords.some((keyword)=>lowerPrompt.includes(keyword));\n        if (!isInteriorRelated) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Please provide an interior design or home-related prompt\"\n            }, {\n                status: 400\n            });\n        }\n        // Initialize Hugging Face Inference Client\n        const hf = new _huggingface_inference__WEBPACK_IMPORTED_MODULE_1__.HfInference(apiKey);\n        try {\n            // Use Hugging Face InferenceClient with FLUX.1-dev model\n            const imageBlob = await hf.textToImage({\n                inputs: prompt,\n                model: \"black-forest-labs/FLUX.1-dev\",\n                parameters: {\n                    guidance_scale: 7.5,\n                    num_inference_steps: 28,\n                    width: 1024,\n                    height: 1024\n                }\n            });\n            // Convert blob to base64 for sending to client\n            const arrayBuffer = await imageBlob.arrayBuffer();\n            const base64 = Buffer.from(arrayBuffer).toString(\"base64\");\n            const imageUrl = `data:image/png;base64,${base64}`;\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                imageUrl,\n                prompt,\n                timestamp: Date.now()\n            });\n        } catch (hfError) {\n            console.error(\"Hugging Face API error:\", hfError);\n            if (hfError.message?.includes(\"exceeded\")) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Monthly API credits exceeded. Please upgrade your Hugging Face plan or try again next month.\"\n                }, {\n                    status: 402\n                });\n            }\n            if (hfError.message?.includes(\"loading\")) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Model is currently loading. Please try again in a few moments.\"\n                }, {\n                    status: 503\n                });\n            }\n            if (hfError.message?.includes(\"rate limit\")) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Rate limit exceeded. Please wait a moment before trying again.\"\n                }, {\n                    status: 429\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Failed to generate image: ${hfError.message || \"Unknown error\"}`\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error(\"Error in generate-image API:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error. Please try again.\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9nZW5lcmF0ZS1pbWFnZS9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBd0Q7QUFDSDtBQUU5QyxlQUFlRSxLQUFLQyxPQUFvQjtJQUM3QyxJQUFJO1FBQ0YsTUFBTSxFQUFFQyxNQUFNLEVBQUUsR0FBRyxNQUFNRCxRQUFRRSxJQUFJO1FBRXJDLElBQUksQ0FBQ0QsUUFBUTtZQUNYLE9BQU9KLHFEQUFZQSxDQUFDSyxJQUFJLENBQ3RCO2dCQUFFQyxPQUFPO1lBQXFCLEdBQzlCO2dCQUFFQyxRQUFRO1lBQUk7UUFFbEI7UUFFQSw2Q0FBNkM7UUFDN0MsTUFBTUMsU0FBU0MsUUFBUUMsR0FBRyxDQUFDQyxtQkFBbUI7UUFFOUMsSUFBSSxDQUFDSCxRQUFRO1lBQ1hJLFFBQVFOLEtBQUssQ0FBQztZQUNkLE9BQU9OLHFEQUFZQSxDQUFDSyxJQUFJLENBQ3RCO2dCQUFFQyxPQUFPO1lBQTBCLEdBQ25DO2dCQUFFQyxRQUFRO1lBQUk7UUFFbEI7UUFFQSwyRUFBMkU7UUFDM0UsTUFBTU0sbUJBQW1CO1lBQ3ZCO1lBQVc7WUFBVztZQUFlO1lBQVk7WUFBZTtZQUFVO1lBQzFFO1lBQVk7WUFBUTtZQUFTO1lBQWE7WUFBUTtZQUFTO1lBQVU7WUFDckU7WUFBVztZQUFXO1lBQVE7WUFBUztZQUFTO1lBQU87WUFBUztZQUNoRTtZQUFVO1lBQWdCO1lBQWU7WUFBYztZQUFVO1lBQ2pFO1lBQVk7WUFBVTtZQUFRO1lBQVM7WUFBVztZQUFTO1NBQzVEO1FBRUQsTUFBTUMsY0FBY1YsT0FBT1csV0FBVztRQUN0QyxNQUFNQyxvQkFBb0JILGlCQUFpQkksSUFBSSxDQUFDQyxDQUFBQSxVQUFXSixZQUFZSyxRQUFRLENBQUNEO1FBRWhGLElBQUksQ0FBQ0YsbUJBQW1CO1lBQ3RCLE9BQU9oQixxREFBWUEsQ0FBQ0ssSUFBSSxDQUN0QjtnQkFBRUMsT0FBTztZQUEyRCxHQUNwRTtnQkFBRUMsUUFBUTtZQUFJO1FBRWxCO1FBRUEsMkNBQTJDO1FBQzNDLE1BQU1hLEtBQUssSUFBSW5CLCtEQUFXQSxDQUFDTztRQUUzQixJQUFJO1lBQ0YseURBQXlEO1lBQ3pELE1BQU1hLFlBQVksTUFBTUQsR0FBR0UsV0FBVyxDQUFDO2dCQUNyQ0MsUUFBUW5CO2dCQUNSb0IsT0FBTztnQkFDUEMsWUFBWTtvQkFDVkMsZ0JBQWdCO29CQUNoQkMscUJBQXFCO29CQUNyQkMsT0FBTztvQkFDUEMsUUFBUTtnQkFDVjtZQUNGO1lBRUEsK0NBQStDO1lBQy9DLE1BQU1DLGNBQWMsTUFBTVQsVUFBVVMsV0FBVztZQUMvQyxNQUFNQyxTQUFTQyxPQUFPQyxJQUFJLENBQUNILGFBQWFJLFFBQVEsQ0FBQztZQUNqRCxNQUFNQyxXQUFXLENBQUMsc0JBQXNCLEVBQUVKLE9BQU8sQ0FBQztZQUVsRCxPQUFPL0IscURBQVlBLENBQUNLLElBQUksQ0FBQztnQkFDdkI4QjtnQkFDQS9CO2dCQUNBZ0MsV0FBV0MsS0FBS0MsR0FBRztZQUNyQjtRQUVGLEVBQUUsT0FBT0MsU0FBYztZQUNyQjNCLFFBQVFOLEtBQUssQ0FBQywyQkFBMkJpQztZQUV6QyxJQUFJQSxRQUFRQyxPQUFPLEVBQUVyQixTQUFTLGFBQWE7Z0JBQ3pDLE9BQU9uQixxREFBWUEsQ0FBQ0ssSUFBSSxDQUN0QjtvQkFBRUMsT0FBTztnQkFBK0YsR0FDeEc7b0JBQUVDLFFBQVE7Z0JBQUk7WUFFbEI7WUFFQSxJQUFJZ0MsUUFBUUMsT0FBTyxFQUFFckIsU0FBUyxZQUFZO2dCQUN4QyxPQUFPbkIscURBQVlBLENBQUNLLElBQUksQ0FDdEI7b0JBQUVDLE9BQU87Z0JBQWlFLEdBQzFFO29CQUFFQyxRQUFRO2dCQUFJO1lBRWxCO1lBRUEsSUFBSWdDLFFBQVFDLE9BQU8sRUFBRXJCLFNBQVMsZUFBZTtnQkFDM0MsT0FBT25CLHFEQUFZQSxDQUFDSyxJQUFJLENBQ3RCO29CQUFFQyxPQUFPO2dCQUFpRSxHQUMxRTtvQkFBRUMsUUFBUTtnQkFBSTtZQUVsQjtZQUVBLE9BQU9QLHFEQUFZQSxDQUFDSyxJQUFJLENBQ3RCO2dCQUFFQyxPQUFPLENBQUMsMEJBQTBCLEVBQUVpQyxRQUFRQyxPQUFPLElBQUksZ0JBQWdCLENBQUM7WUFBQyxHQUMzRTtnQkFBRWpDLFFBQVE7WUFBSTtRQUVsQjtJQUVGLEVBQUUsT0FBT0QsT0FBTztRQUNkTSxRQUFRTixLQUFLLENBQUMsZ0NBQWdDQTtRQUM5QyxPQUFPTixxREFBWUEsQ0FBQ0ssSUFBSSxDQUN0QjtZQUFFQyxPQUFPO1FBQTJDLEdBQ3BEO1lBQUVDLFFBQVE7UUFBSTtJQUVsQjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbW9kZXJuLWhhdmVuLy4vc3JjL2FwcC9hcGkvZ2VuZXJhdGUtaW1hZ2Uvcm91dGUudHM/Y2IxYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInO1xuaW1wb3J0IHsgSGZJbmZlcmVuY2UgfSBmcm9tICdAaHVnZ2luZ2ZhY2UvaW5mZXJlbmNlJztcblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIFBPU1QocmVxdWVzdDogTmV4dFJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCB7IHByb21wdCB9ID0gYXdhaXQgcmVxdWVzdC5qc29uKCk7XG5cbiAgICBpZiAoIXByb21wdCkge1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IGVycm9yOiAnUHJvbXB0IGlzIHJlcXVpcmVkJyB9LFxuICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgICk7XG4gICAgfVxuXG4gICAgLy8gR2V0IHRoZSBBUEkga2V5IGZyb20gZW52aXJvbm1lbnQgdmFyaWFibGVzXG4gICAgY29uc3QgYXBpS2V5ID0gcHJvY2Vzcy5lbnYuSFVHR0lOR0ZBQ0VfQVBJX0tFWTtcblxuICAgIGlmICghYXBpS2V5KSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdIVUdHSU5HRkFDRV9BUElfS0VZIG5vdCBmb3VuZCBpbiBlbnZpcm9ubWVudCB2YXJpYWJsZXMnKTtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBlcnJvcjogJ0FQSSBjb25maWd1cmF0aW9uIGVycm9yJyB9LFxuICAgICAgICB7IHN0YXR1czogNTAwIH1cbiAgICAgICk7XG4gICAgfVxuXG4gICAgLy8gSW50ZXJpb3IgZGVzaWduIHZhbGlkYXRpb24gLSBlbnN1cmUgcHJvbXB0IGlzIHJlbGF0ZWQgdG8gaW50ZXJpb3IgZGVzaWduXG4gICAgY29uc3QgaW50ZXJpb3JLZXl3b3JkcyA9IFtcbiAgICAgICdraXRjaGVuJywgJ2JlZHJvb20nLCAnbGl2aW5nIHJvb20nLCAnYmF0aHJvb20nLCAnZGluaW5nIHJvb20nLCAnb2ZmaWNlJywgJ2Nsb3NldCcsXG4gICAgICAnaW50ZXJpb3InLCAnaG9tZScsICdob3VzZScsICdhcGFydG1lbnQnLCAncm9vbScsICdzcGFjZScsICdkZXNpZ24nLCAnZnVybml0dXJlJyxcbiAgICAgICdjYWJpbmV0JywgJ2NvdW50ZXInLCAnc29mYScsICdjaGFpcicsICd0YWJsZScsICdiZWQnLCAnc2hlbGYnLCAnc3RvcmFnZScsXG4gICAgICAnbW9kZXJuJywgJ2NvbnRlbXBvcmFyeScsICd0cmFkaXRpb25hbCcsICdtaW5pbWFsaXN0JywgJ2x1eHVyeScsICdjb3p5JyxcbiAgICAgICdsaWdodGluZycsICd3aW5kb3cnLCAnd2FsbCcsICdmbG9vcicsICdjZWlsaW5nJywgJ2RlY29yJywgJ3Jlbm92YXRpb24nXG4gICAgXTtcblxuICAgIGNvbnN0IGxvd2VyUHJvbXB0ID0gcHJvbXB0LnRvTG93ZXJDYXNlKCk7XG4gICAgY29uc3QgaXNJbnRlcmlvclJlbGF0ZWQgPSBpbnRlcmlvcktleXdvcmRzLnNvbWUoa2V5d29yZCA9PiBsb3dlclByb21wdC5pbmNsdWRlcyhrZXl3b3JkKSk7XG5cbiAgICBpZiAoIWlzSW50ZXJpb3JSZWxhdGVkKSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHsgZXJyb3I6ICdQbGVhc2UgcHJvdmlkZSBhbiBpbnRlcmlvciBkZXNpZ24gb3IgaG9tZS1yZWxhdGVkIHByb21wdCcgfSxcbiAgICAgICAgeyBzdGF0dXM6IDQwMCB9XG4gICAgICApO1xuICAgIH1cblxuICAgIC8vIEluaXRpYWxpemUgSHVnZ2luZyBGYWNlIEluZmVyZW5jZSBDbGllbnRcbiAgICBjb25zdCBoZiA9IG5ldyBIZkluZmVyZW5jZShhcGlLZXkpO1xuXG4gICAgdHJ5IHtcbiAgICAgIC8vIFVzZSBIdWdnaW5nIEZhY2UgSW5mZXJlbmNlQ2xpZW50IHdpdGggRkxVWC4xLWRldiBtb2RlbFxuICAgICAgY29uc3QgaW1hZ2VCbG9iID0gYXdhaXQgaGYudGV4dFRvSW1hZ2Uoe1xuICAgICAgICBpbnB1dHM6IHByb21wdCxcbiAgICAgICAgbW9kZWw6ICdibGFjay1mb3Jlc3QtbGFicy9GTFVYLjEtZGV2JyxcbiAgICAgICAgcGFyYW1ldGVyczoge1xuICAgICAgICAgIGd1aWRhbmNlX3NjYWxlOiA3LjUsXG4gICAgICAgICAgbnVtX2luZmVyZW5jZV9zdGVwczogMjgsXG4gICAgICAgICAgd2lkdGg6IDEwMjQsXG4gICAgICAgICAgaGVpZ2h0OiAxMDI0LFxuICAgICAgICB9LFxuICAgICAgfSk7XG5cbiAgICAgIC8vIENvbnZlcnQgYmxvYiB0byBiYXNlNjQgZm9yIHNlbmRpbmcgdG8gY2xpZW50XG4gICAgICBjb25zdCBhcnJheUJ1ZmZlciA9IGF3YWl0IGltYWdlQmxvYi5hcnJheUJ1ZmZlcigpO1xuICAgICAgY29uc3QgYmFzZTY0ID0gQnVmZmVyLmZyb20oYXJyYXlCdWZmZXIpLnRvU3RyaW5nKCdiYXNlNjQnKTtcbiAgICAgIGNvbnN0IGltYWdlVXJsID0gYGRhdGE6aW1hZ2UvcG5nO2Jhc2U2NCwke2Jhc2U2NH1gO1xuXG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oe1xuICAgICAgICBpbWFnZVVybCxcbiAgICAgICAgcHJvbXB0LFxuICAgICAgICB0aW1lc3RhbXA6IERhdGUubm93KCksXG4gICAgICB9KTtcblxuICAgIH0gY2F0Y2ggKGhmRXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcignSHVnZ2luZyBGYWNlIEFQSSBlcnJvcjonLCBoZkVycm9yKTtcblxuICAgICAgaWYgKGhmRXJyb3IubWVzc2FnZT8uaW5jbHVkZXMoJ2V4Y2VlZGVkJykpIHtcbiAgICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICAgIHsgZXJyb3I6ICdNb250aGx5IEFQSSBjcmVkaXRzIGV4Y2VlZGVkLiBQbGVhc2UgdXBncmFkZSB5b3VyIEh1Z2dpbmcgRmFjZSBwbGFuIG9yIHRyeSBhZ2FpbiBuZXh0IG1vbnRoLicgfSxcbiAgICAgICAgICB7IHN0YXR1czogNDAyIH1cbiAgICAgICAgKTtcbiAgICAgIH1cblxuICAgICAgaWYgKGhmRXJyb3IubWVzc2FnZT8uaW5jbHVkZXMoJ2xvYWRpbmcnKSkge1xuICAgICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgICAgeyBlcnJvcjogJ01vZGVsIGlzIGN1cnJlbnRseSBsb2FkaW5nLiBQbGVhc2UgdHJ5IGFnYWluIGluIGEgZmV3IG1vbWVudHMuJyB9LFxuICAgICAgICAgIHsgc3RhdHVzOiA1MDMgfVxuICAgICAgICApO1xuICAgICAgfVxuXG4gICAgICBpZiAoaGZFcnJvci5tZXNzYWdlPy5pbmNsdWRlcygncmF0ZSBsaW1pdCcpKSB7XG4gICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgICB7IGVycm9yOiAnUmF0ZSBsaW1pdCBleGNlZWRlZC4gUGxlYXNlIHdhaXQgYSBtb21lbnQgYmVmb3JlIHRyeWluZyBhZ2Fpbi4nIH0sXG4gICAgICAgICAgeyBzdGF0dXM6IDQyOSB9XG4gICAgICAgICk7XG4gICAgICB9XG5cbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBlcnJvcjogYEZhaWxlZCB0byBnZW5lcmF0ZSBpbWFnZTogJHtoZkVycm9yLm1lc3NhZ2UgfHwgJ1Vua25vd24gZXJyb3InfWAgfSxcbiAgICAgICAgeyBzdGF0dXM6IDUwMCB9XG4gICAgICApO1xuICAgIH1cblxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGluIGdlbmVyYXRlLWltYWdlIEFQSTonLCBlcnJvcik7XG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgeyBlcnJvcjogJ0ludGVybmFsIHNlcnZlciBlcnJvci4gUGxlYXNlIHRyeSBhZ2Fpbi4nIH0sXG4gICAgICB7IHN0YXR1czogNTAwIH1cbiAgICApO1xuICB9XG59XG4iXSwibmFtZXMiOlsiTmV4dFJlc3BvbnNlIiwiSGZJbmZlcmVuY2UiLCJQT1NUIiwicmVxdWVzdCIsInByb21wdCIsImpzb24iLCJlcnJvciIsInN0YXR1cyIsImFwaUtleSIsInByb2Nlc3MiLCJlbnYiLCJIVUdHSU5HRkFDRV9BUElfS0VZIiwiY29uc29sZSIsImludGVyaW9yS2V5d29yZHMiLCJsb3dlclByb21wdCIsInRvTG93ZXJDYXNlIiwiaXNJbnRlcmlvclJlbGF0ZWQiLCJzb21lIiwia2V5d29yZCIsImluY2x1ZGVzIiwiaGYiLCJpbWFnZUJsb2IiLCJ0ZXh0VG9JbWFnZSIsImlucHV0cyIsIm1vZGVsIiwicGFyYW1ldGVycyIsImd1aWRhbmNlX3NjYWxlIiwibnVtX2luZmVyZW5jZV9zdGVwcyIsIndpZHRoIiwiaGVpZ2h0IiwiYXJyYXlCdWZmZXIiLCJiYXNlNjQiLCJCdWZmZXIiLCJmcm9tIiwidG9TdHJpbmciLCJpbWFnZVVybCIsInRpbWVzdGFtcCIsIkRhdGUiLCJub3ciLCJoZkVycm9yIiwibWVzc2FnZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-image/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@huggingface"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=C%3A%5CFlame%20workspace%5Cmorderm-haven%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CFlame%20workspace%5Cmorderm-haven&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();