"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-image/route";
exports.ids = ["app/api/generate-image/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=C%3A%5CFlame%20workspace%5Cmorderm-haven%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CFlame%20workspace%5Cmorderm-haven&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=C%3A%5CFlame%20workspace%5Cmorderm-haven%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CFlame%20workspace%5Cmorderm-haven&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Flame_workspace_morderm_haven_src_app_api_generate_image_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-image/route.ts */ \"(rsc)/./src/app/api/generate-image/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-image/route\",\n        pathname: \"/api/generate-image\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-image/route\"\n    },\n    resolvedPagePath: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\api\\\\generate-image\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Flame_workspace_morderm_haven_src_app_api_generate_image_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/generate-image/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=C%3A%5CFlame%20workspace%5Cmorderm-haven%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CFlame%20workspace%5Cmorderm-haven&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/generate-image/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/generate-image/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @google/generative-ai */ \"(rsc)/./node_modules/@google/generative-ai/dist/index.mjs\");\n\n\nasync function POST(request) {\n    try {\n        const { prompt, uploadedImage } = await request.json();\n        if (!prompt) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Prompt is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Get the Google AI API key from environment variables\n        const apiKey = process.env.GOOGLE_AI_API_KEY;\n        if (!apiKey) {\n            console.error(\"GOOGLE_AI_API_KEY not found in environment variables\");\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"API configuration error\"\n            }, {\n                status: 500\n            });\n        }\n        // Interior design validation - ensure prompt is related to interior design\n        const interiorKeywords = [\n            \"kitchen\",\n            \"bedroom\",\n            \"living room\",\n            \"bathroom\",\n            \"dining room\",\n            \"office\",\n            \"closet\",\n            \"interior\",\n            \"home\",\n            \"house\",\n            \"apartment\",\n            \"room\",\n            \"space\",\n            \"design\",\n            \"furniture\",\n            \"cabinet\",\n            \"counter\",\n            \"sofa\",\n            \"chair\",\n            \"table\",\n            \"bed\",\n            \"shelf\",\n            \"storage\",\n            \"modern\",\n            \"contemporary\",\n            \"traditional\",\n            \"minimalist\",\n            \"luxury\",\n            \"cozy\",\n            \"lighting\",\n            \"window\",\n            \"wall\",\n            \"floor\",\n            \"ceiling\",\n            \"decor\",\n            \"renovation\"\n        ];\n        const lowerPrompt = prompt.toLowerCase();\n        const isInteriorRelated = interiorKeywords.some((keyword)=>lowerPrompt.includes(keyword));\n        if (!isInteriorRelated) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Please provide an interior design or home-related prompt\"\n            }, {\n                status: 400\n            });\n        }\n        // Initialize Google AI client\n        const genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__.GoogleGenerativeAI(apiKey);\n        try {\n            // Use Gemini 2.0 Flash Preview Image Generation\n            const model = genAI.getGenerativeModel({\n                model: \"gemini-2.0-flash-preview-image-generation\"\n            });\n            // Enhanced prompt for better interior design results\n            const enhancedPrompt = `Create a high-quality, professional interior design image: ${prompt}. Style: photorealistic, architectural photography, professional lighting, high resolution, detailed textures, modern design aesthetic.`;\n            console.log(\"Generating image with Gemini 2.0 Flash Preview Image Generation...\");\n            // Prepare the content array\n            const contentParts = [\n                {\n                    text: enhancedPrompt\n                }\n            ];\n            // Add uploaded image if provided\n            if (uploadedImage) {\n                contentParts.push({\n                    inlineData: {\n                        mimeType: \"image/jpeg\",\n                        data: uploadedImage.split(\",\")[1] // Remove data:image/jpeg;base64, prefix\n                    }\n                });\n            }\n            const result = await model.generateContent(contentParts);\n            const response = await result.response;\n            // Check if the response contains an image\n            if (response.candidates && response.candidates[0]) {\n                const candidate = response.candidates[0];\n                // Look for image data in the response\n                if (candidate.content && candidate.content.parts) {\n                    for (const part of candidate.content.parts){\n                        if (part.inlineData && part.inlineData.data) {\n                            // Found generated image\n                            const imageData = part.inlineData.data;\n                            const mimeType = part.inlineData.mimeType || \"image/png\";\n                            const imageUrl = `data:${mimeType};base64,${imageData}`;\n                            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                                imageUrl,\n                                prompt,\n                                timestamp: Date.now(),\n                                message: \"Successfully generated interior design image\"\n                            });\n                        }\n                    }\n                }\n            }\n            // If no image was generated, return text description\n            const text = response.text();\n            // Try to use Gemini's image generation capabilities\n            // Note: Gemini 2.0 Flash Preview may have limited image generation\n            // Let's implement a proper image generation request\n            try {\n                // For Gemini image generation, we need to use a different approach\n                // Since Gemini 2.0 Flash Preview might not support direct image generation yet,\n                // let's create a comprehensive text description and use that for now\n                const imageDescription = text;\n                // Create a placeholder image with the description\n                // In a real implementation, you would use Gemini's actual image generation API\n                // For now, we'll return the description and suggest using it with other tools\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    textDescription: imageDescription,\n                    prompt: enhancedPrompt,\n                    timestamp: Date.now(),\n                    message: \"Gemini 2.0 Flash has generated a detailed description of your interior design concept.\",\n                    suggestion: \"Use this description with image generation tools or as inspiration for your design project.\"\n                });\n            } catch (imageError) {\n                console.log(\"Image generation not available, returning text description\");\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    textDescription: text,\n                    prompt: enhancedPrompt,\n                    timestamp: Date.now(),\n                    message: \"Generated detailed interior design description using Gemini 2.0 Flash\"\n                });\n            }\n        } catch (geminiError) {\n            console.error(\"Google AI API error:\", geminiError);\n            if (geminiError.message?.includes(\"quota\") || geminiError.message?.includes(\"exceeded\")) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Google AI API quota exceeded. Please check your API usage or upgrade your plan.\",\n                    suggestion: \"Visit Google AI Studio to check your API quota and usage limits.\"\n                }, {\n                    status: 402\n                });\n            }\n            if (geminiError.message?.includes(\"invalid\") || geminiError.message?.includes(\"key\")) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Invalid Google AI API key. Please check your configuration.\"\n                }, {\n                    status: 401\n                });\n            }\n            if (geminiError.message?.includes(\"rate limit\")) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Rate limit exceeded. Please wait a moment before trying again.\"\n                }, {\n                    status: 429\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Failed to generate content: ${geminiError.message || \"Unknown error\"}`\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error(\"Error in generate-image API:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error. Please try again.\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9nZW5lcmF0ZS1pbWFnZS9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBd0Q7QUFDRztBQUVwRCxlQUFlRSxLQUFLQyxPQUFvQjtJQUM3QyxJQUFJO1FBQ0YsTUFBTSxFQUFFQyxNQUFNLEVBQUVDLGFBQWEsRUFBRSxHQUFHLE1BQU1GLFFBQVFHLElBQUk7UUFFcEQsSUFBSSxDQUFDRixRQUFRO1lBQ1gsT0FBT0oscURBQVlBLENBQUNNLElBQUksQ0FDdEI7Z0JBQUVDLE9BQU87WUFBcUIsR0FDOUI7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLHVEQUF1RDtRQUN2RCxNQUFNQyxTQUFTQyxRQUFRQyxHQUFHLENBQUNDLGlCQUFpQjtRQUU1QyxJQUFJLENBQUNILFFBQVE7WUFDWEksUUFBUU4sS0FBSyxDQUFDO1lBQ2QsT0FBT1AscURBQVlBLENBQUNNLElBQUksQ0FDdEI7Z0JBQUVDLE9BQU87WUFBMEIsR0FDbkM7Z0JBQUVDLFFBQVE7WUFBSTtRQUVsQjtRQUVBLDJFQUEyRTtRQUMzRSxNQUFNTSxtQkFBbUI7WUFDdkI7WUFBVztZQUFXO1lBQWU7WUFBWTtZQUFlO1lBQVU7WUFDMUU7WUFBWTtZQUFRO1lBQVM7WUFBYTtZQUFRO1lBQVM7WUFBVTtZQUNyRTtZQUFXO1lBQVc7WUFBUTtZQUFTO1lBQVM7WUFBTztZQUFTO1lBQ2hFO1lBQVU7WUFBZ0I7WUFBZTtZQUFjO1lBQVU7WUFDakU7WUFBWTtZQUFVO1lBQVE7WUFBUztZQUFXO1lBQVM7U0FDNUQ7UUFFRCxNQUFNQyxjQUFjWCxPQUFPWSxXQUFXO1FBQ3RDLE1BQU1DLG9CQUFvQkgsaUJBQWlCSSxJQUFJLENBQUNDLENBQUFBLFVBQVdKLFlBQVlLLFFBQVEsQ0FBQ0Q7UUFFaEYsSUFBSSxDQUFDRixtQkFBbUI7WUFDdEIsT0FBT2pCLHFEQUFZQSxDQUFDTSxJQUFJLENBQ3RCO2dCQUFFQyxPQUFPO1lBQTJELEdBQ3BFO2dCQUFFQyxRQUFRO1lBQUk7UUFFbEI7UUFFQSw4QkFBOEI7UUFDOUIsTUFBTWEsUUFBUSxJQUFJcEIscUVBQWtCQSxDQUFDUTtRQUVyQyxJQUFJO1lBQ0YsZ0RBQWdEO1lBQ2hELE1BQU1hLFFBQVFELE1BQU1FLGtCQUFrQixDQUFDO2dCQUFFRCxPQUFPO1lBQTRDO1lBRTVGLHFEQUFxRDtZQUNyRCxNQUFNRSxpQkFBaUIsQ0FBQywyREFBMkQsRUFBRXBCLE9BQU8sdUlBQXVJLENBQUM7WUFFcE9TLFFBQVFZLEdBQUcsQ0FBQztZQUVaLDRCQUE0QjtZQUM1QixNQUFNQyxlQUFlO2dCQUFDO29CQUFFQyxNQUFNSDtnQkFBZTthQUFFO1lBRS9DLGlDQUFpQztZQUNqQyxJQUFJbkIsZUFBZTtnQkFDakJxQixhQUFhRSxJQUFJLENBQUM7b0JBQ2hCQyxZQUFZO3dCQUNWQyxVQUFVO3dCQUNWQyxNQUFNMUIsY0FBYzJCLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRSxDQUFDLHdDQUF3QztvQkFDNUU7Z0JBQ0Y7WUFDRjtZQUVBLE1BQU1DLFNBQVMsTUFBTVgsTUFBTVksZUFBZSxDQUFDUjtZQUMzQyxNQUFNUyxXQUFXLE1BQU1GLE9BQU9FLFFBQVE7WUFFdEMsMENBQTBDO1lBQzFDLElBQUlBLFNBQVNDLFVBQVUsSUFBSUQsU0FBU0MsVUFBVSxDQUFDLEVBQUUsRUFBRTtnQkFDakQsTUFBTUMsWUFBWUYsU0FBU0MsVUFBVSxDQUFDLEVBQUU7Z0JBRXhDLHNDQUFzQztnQkFDdEMsSUFBSUMsVUFBVUMsT0FBTyxJQUFJRCxVQUFVQyxPQUFPLENBQUNDLEtBQUssRUFBRTtvQkFDaEQsS0FBSyxNQUFNQyxRQUFRSCxVQUFVQyxPQUFPLENBQUNDLEtBQUssQ0FBRTt3QkFDMUMsSUFBSUMsS0FBS1gsVUFBVSxJQUFJVyxLQUFLWCxVQUFVLENBQUNFLElBQUksRUFBRTs0QkFDM0Msd0JBQXdCOzRCQUN4QixNQUFNVSxZQUFZRCxLQUFLWCxVQUFVLENBQUNFLElBQUk7NEJBQ3RDLE1BQU1ELFdBQVdVLEtBQUtYLFVBQVUsQ0FBQ0MsUUFBUSxJQUFJOzRCQUM3QyxNQUFNWSxXQUFXLENBQUMsS0FBSyxFQUFFWixTQUFTLFFBQVEsRUFBRVcsVUFBVSxDQUFDOzRCQUV2RCxPQUFPekMscURBQVlBLENBQUNNLElBQUksQ0FBQztnQ0FDdkJvQztnQ0FDQXRDO2dDQUNBdUMsV0FBV0MsS0FBS0MsR0FBRztnQ0FDbkJDLFNBQVM7NEJBQ1g7d0JBQ0Y7b0JBQ0Y7Z0JBQ0Y7WUFDRjtZQUVBLHFEQUFxRDtZQUNyRCxNQUFNbkIsT0FBT1EsU0FBU1IsSUFBSTtZQUUxQixvREFBb0Q7WUFDcEQsbUVBQW1FO1lBQ25FLG9EQUFvRDtZQUVwRCxJQUFJO2dCQUNGLG1FQUFtRTtnQkFDbkUsZ0ZBQWdGO2dCQUNoRixxRUFBcUU7Z0JBRXJFLE1BQU1vQixtQkFBbUJwQjtnQkFFekIsa0RBQWtEO2dCQUNsRCwrRUFBK0U7Z0JBQy9FLDhFQUE4RTtnQkFFOUUsT0FBTzNCLHFEQUFZQSxDQUFDTSxJQUFJLENBQUM7b0JBQ3ZCMEMsaUJBQWlCRDtvQkFDakIzQyxRQUFRb0I7b0JBQ1JtQixXQUFXQyxLQUFLQyxHQUFHO29CQUNuQkMsU0FBUztvQkFDVEcsWUFBWTtnQkFDZDtZQUVGLEVBQUUsT0FBT0MsWUFBaUI7Z0JBQ3hCckMsUUFBUVksR0FBRyxDQUFDO2dCQUVaLE9BQU96QixxREFBWUEsQ0FBQ00sSUFBSSxDQUFDO29CQUN2QjBDLGlCQUFpQnJCO29CQUNqQnZCLFFBQVFvQjtvQkFDUm1CLFdBQVdDLEtBQUtDLEdBQUc7b0JBQ25CQyxTQUFTO2dCQUNYO1lBQ0Y7UUFFRixFQUFFLE9BQU9LLGFBQWtCO1lBQ3pCdEMsUUFBUU4sS0FBSyxDQUFDLHdCQUF3QjRDO1lBRXRDLElBQUlBLFlBQVlMLE9BQU8sRUFBRTFCLFNBQVMsWUFBWStCLFlBQVlMLE9BQU8sRUFBRTFCLFNBQVMsYUFBYTtnQkFDdkYsT0FBT3BCLHFEQUFZQSxDQUFDTSxJQUFJLENBQ3RCO29CQUNFQyxPQUFPO29CQUNQMEMsWUFBWTtnQkFDZCxHQUNBO29CQUFFekMsUUFBUTtnQkFBSTtZQUVsQjtZQUVBLElBQUkyQyxZQUFZTCxPQUFPLEVBQUUxQixTQUFTLGNBQWMrQixZQUFZTCxPQUFPLEVBQUUxQixTQUFTLFFBQVE7Z0JBQ3BGLE9BQU9wQixxREFBWUEsQ0FBQ00sSUFBSSxDQUN0QjtvQkFBRUMsT0FBTztnQkFBOEQsR0FDdkU7b0JBQUVDLFFBQVE7Z0JBQUk7WUFFbEI7WUFFQSxJQUFJMkMsWUFBWUwsT0FBTyxFQUFFMUIsU0FBUyxlQUFlO2dCQUMvQyxPQUFPcEIscURBQVlBLENBQUNNLElBQUksQ0FDdEI7b0JBQUVDLE9BQU87Z0JBQWlFLEdBQzFFO29CQUFFQyxRQUFRO2dCQUFJO1lBRWxCO1lBRUEsT0FBT1IscURBQVlBLENBQUNNLElBQUksQ0FDdEI7Z0JBQUVDLE9BQU8sQ0FBQyw0QkFBNEIsRUFBRTRDLFlBQVlMLE9BQU8sSUFBSSxnQkFBZ0IsQ0FBQztZQUFDLEdBQ2pGO2dCQUFFdEMsUUFBUTtZQUFJO1FBRWxCO0lBRUYsRUFBRSxPQUFPRCxPQUFPO1FBQ2RNLFFBQVFOLEtBQUssQ0FBQyxnQ0FBZ0NBO1FBQzlDLE9BQU9QLHFEQUFZQSxDQUFDTSxJQUFJLENBQ3RCO1lBQUVDLE9BQU87UUFBMkMsR0FDcEQ7WUFBRUMsUUFBUTtRQUFJO0lBRWxCO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9tb2Rlcm4taGF2ZW4vLi9zcmMvYXBwL2FwaS9nZW5lcmF0ZS1pbWFnZS9yb3V0ZS50cz9jYjFiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRSZXF1ZXN0LCBOZXh0UmVzcG9uc2UgfSBmcm9tICduZXh0L3NlcnZlcic7XG5pbXBvcnQgeyBHb29nbGVHZW5lcmF0aXZlQUkgfSBmcm9tICdAZ29vZ2xlL2dlbmVyYXRpdmUtYWknO1xuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gUE9TVChyZXF1ZXN0OiBOZXh0UmVxdWVzdCkge1xuICB0cnkge1xuICAgIGNvbnN0IHsgcHJvbXB0LCB1cGxvYWRlZEltYWdlIH0gPSBhd2FpdCByZXF1ZXN0Lmpzb24oKTtcblxuICAgIGlmICghcHJvbXB0KSB7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICAgIHsgZXJyb3I6ICdQcm9tcHQgaXMgcmVxdWlyZWQnIH0sXG4gICAgICAgIHsgc3RhdHVzOiA0MDAgfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICAvLyBHZXQgdGhlIEdvb2dsZSBBSSBBUEkga2V5IGZyb20gZW52aXJvbm1lbnQgdmFyaWFibGVzXG4gICAgY29uc3QgYXBpS2V5ID0gcHJvY2Vzcy5lbnYuR09PR0xFX0FJX0FQSV9LRVk7XG5cbiAgICBpZiAoIWFwaUtleSkge1xuICAgICAgY29uc29sZS5lcnJvcignR09PR0xFX0FJX0FQSV9LRVkgbm90IGZvdW5kIGluIGVudmlyb25tZW50IHZhcmlhYmxlcycpO1xuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IGVycm9yOiAnQVBJIGNvbmZpZ3VyYXRpb24gZXJyb3InIH0sXG4gICAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgICAgKTtcbiAgICB9XG5cbiAgICAvLyBJbnRlcmlvciBkZXNpZ24gdmFsaWRhdGlvbiAtIGVuc3VyZSBwcm9tcHQgaXMgcmVsYXRlZCB0byBpbnRlcmlvciBkZXNpZ25cbiAgICBjb25zdCBpbnRlcmlvcktleXdvcmRzID0gW1xuICAgICAgJ2tpdGNoZW4nLCAnYmVkcm9vbScsICdsaXZpbmcgcm9vbScsICdiYXRocm9vbScsICdkaW5pbmcgcm9vbScsICdvZmZpY2UnLCAnY2xvc2V0JyxcbiAgICAgICdpbnRlcmlvcicsICdob21lJywgJ2hvdXNlJywgJ2FwYXJ0bWVudCcsICdyb29tJywgJ3NwYWNlJywgJ2Rlc2lnbicsICdmdXJuaXR1cmUnLFxuICAgICAgJ2NhYmluZXQnLCAnY291bnRlcicsICdzb2ZhJywgJ2NoYWlyJywgJ3RhYmxlJywgJ2JlZCcsICdzaGVsZicsICdzdG9yYWdlJyxcbiAgICAgICdtb2Rlcm4nLCAnY29udGVtcG9yYXJ5JywgJ3RyYWRpdGlvbmFsJywgJ21pbmltYWxpc3QnLCAnbHV4dXJ5JywgJ2NvenknLFxuICAgICAgJ2xpZ2h0aW5nJywgJ3dpbmRvdycsICd3YWxsJywgJ2Zsb29yJywgJ2NlaWxpbmcnLCAnZGVjb3InLCAncmVub3ZhdGlvbidcbiAgICBdO1xuXG4gICAgY29uc3QgbG93ZXJQcm9tcHQgPSBwcm9tcHQudG9Mb3dlckNhc2UoKTtcbiAgICBjb25zdCBpc0ludGVyaW9yUmVsYXRlZCA9IGludGVyaW9yS2V5d29yZHMuc29tZShrZXl3b3JkID0+IGxvd2VyUHJvbXB0LmluY2x1ZGVzKGtleXdvcmQpKTtcblxuICAgIGlmICghaXNJbnRlcmlvclJlbGF0ZWQpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBlcnJvcjogJ1BsZWFzZSBwcm92aWRlIGFuIGludGVyaW9yIGRlc2lnbiBvciBob21lLXJlbGF0ZWQgcHJvbXB0JyB9LFxuICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgICk7XG4gICAgfVxuXG4gICAgLy8gSW5pdGlhbGl6ZSBHb29nbGUgQUkgY2xpZW50XG4gICAgY29uc3QgZ2VuQUkgPSBuZXcgR29vZ2xlR2VuZXJhdGl2ZUFJKGFwaUtleSk7XG5cbiAgICB0cnkge1xuICAgICAgLy8gVXNlIEdlbWluaSAyLjAgRmxhc2ggUHJldmlldyBJbWFnZSBHZW5lcmF0aW9uXG4gICAgICBjb25zdCBtb2RlbCA9IGdlbkFJLmdldEdlbmVyYXRpdmVNb2RlbCh7IG1vZGVsOiAnZ2VtaW5pLTIuMC1mbGFzaC1wcmV2aWV3LWltYWdlLWdlbmVyYXRpb24nIH0pO1xuXG4gICAgICAvLyBFbmhhbmNlZCBwcm9tcHQgZm9yIGJldHRlciBpbnRlcmlvciBkZXNpZ24gcmVzdWx0c1xuICAgICAgY29uc3QgZW5oYW5jZWRQcm9tcHQgPSBgQ3JlYXRlIGEgaGlnaC1xdWFsaXR5LCBwcm9mZXNzaW9uYWwgaW50ZXJpb3IgZGVzaWduIGltYWdlOiAke3Byb21wdH0uIFN0eWxlOiBwaG90b3JlYWxpc3RpYywgYXJjaGl0ZWN0dXJhbCBwaG90b2dyYXBoeSwgcHJvZmVzc2lvbmFsIGxpZ2h0aW5nLCBoaWdoIHJlc29sdXRpb24sIGRldGFpbGVkIHRleHR1cmVzLCBtb2Rlcm4gZGVzaWduIGFlc3RoZXRpYy5gO1xuXG4gICAgICBjb25zb2xlLmxvZygnR2VuZXJhdGluZyBpbWFnZSB3aXRoIEdlbWluaSAyLjAgRmxhc2ggUHJldmlldyBJbWFnZSBHZW5lcmF0aW9uLi4uJyk7XG5cbiAgICAgIC8vIFByZXBhcmUgdGhlIGNvbnRlbnQgYXJyYXlcbiAgICAgIGNvbnN0IGNvbnRlbnRQYXJ0cyA9IFt7IHRleHQ6IGVuaGFuY2VkUHJvbXB0IH1dO1xuXG4gICAgICAvLyBBZGQgdXBsb2FkZWQgaW1hZ2UgaWYgcHJvdmlkZWRcbiAgICAgIGlmICh1cGxvYWRlZEltYWdlKSB7XG4gICAgICAgIGNvbnRlbnRQYXJ0cy5wdXNoKHtcbiAgICAgICAgICBpbmxpbmVEYXRhOiB7XG4gICAgICAgICAgICBtaW1lVHlwZTogJ2ltYWdlL2pwZWcnLCAvLyBvciBkZXRlY3QgZnJvbSB1cGxvYWRlZEltYWdlXG4gICAgICAgICAgICBkYXRhOiB1cGxvYWRlZEltYWdlLnNwbGl0KCcsJylbMV0gLy8gUmVtb3ZlIGRhdGE6aW1hZ2UvanBlZztiYXNlNjQsIHByZWZpeFxuICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IG1vZGVsLmdlbmVyYXRlQ29udGVudChjb250ZW50UGFydHMpO1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCByZXN1bHQucmVzcG9uc2U7XG5cbiAgICAgIC8vIENoZWNrIGlmIHRoZSByZXNwb25zZSBjb250YWlucyBhbiBpbWFnZVxuICAgICAgaWYgKHJlc3BvbnNlLmNhbmRpZGF0ZXMgJiYgcmVzcG9uc2UuY2FuZGlkYXRlc1swXSkge1xuICAgICAgICBjb25zdCBjYW5kaWRhdGUgPSByZXNwb25zZS5jYW5kaWRhdGVzWzBdO1xuXG4gICAgICAgIC8vIExvb2sgZm9yIGltYWdlIGRhdGEgaW4gdGhlIHJlc3BvbnNlXG4gICAgICAgIGlmIChjYW5kaWRhdGUuY29udGVudCAmJiBjYW5kaWRhdGUuY29udGVudC5wYXJ0cykge1xuICAgICAgICAgIGZvciAoY29uc3QgcGFydCBvZiBjYW5kaWRhdGUuY29udGVudC5wYXJ0cykge1xuICAgICAgICAgICAgaWYgKHBhcnQuaW5saW5lRGF0YSAmJiBwYXJ0LmlubGluZURhdGEuZGF0YSkge1xuICAgICAgICAgICAgICAvLyBGb3VuZCBnZW5lcmF0ZWQgaW1hZ2VcbiAgICAgICAgICAgICAgY29uc3QgaW1hZ2VEYXRhID0gcGFydC5pbmxpbmVEYXRhLmRhdGE7XG4gICAgICAgICAgICAgIGNvbnN0IG1pbWVUeXBlID0gcGFydC5pbmxpbmVEYXRhLm1pbWVUeXBlIHx8ICdpbWFnZS9wbmcnO1xuICAgICAgICAgICAgICBjb25zdCBpbWFnZVVybCA9IGBkYXRhOiR7bWltZVR5cGV9O2Jhc2U2NCwke2ltYWdlRGF0YX1gO1xuXG4gICAgICAgICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICAgICAgICAgICAgaW1hZ2VVcmwsXG4gICAgICAgICAgICAgICAgcHJvbXB0LFxuICAgICAgICAgICAgICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKSxcbiAgICAgICAgICAgICAgICBtZXNzYWdlOiAnU3VjY2Vzc2Z1bGx5IGdlbmVyYXRlZCBpbnRlcmlvciBkZXNpZ24gaW1hZ2UnXG4gICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyBJZiBubyBpbWFnZSB3YXMgZ2VuZXJhdGVkLCByZXR1cm4gdGV4dCBkZXNjcmlwdGlvblxuICAgICAgY29uc3QgdGV4dCA9IHJlc3BvbnNlLnRleHQoKTtcblxuICAgICAgLy8gVHJ5IHRvIHVzZSBHZW1pbmkncyBpbWFnZSBnZW5lcmF0aW9uIGNhcGFiaWxpdGllc1xuICAgICAgLy8gTm90ZTogR2VtaW5pIDIuMCBGbGFzaCBQcmV2aWV3IG1heSBoYXZlIGxpbWl0ZWQgaW1hZ2UgZ2VuZXJhdGlvblxuICAgICAgLy8gTGV0J3MgaW1wbGVtZW50IGEgcHJvcGVyIGltYWdlIGdlbmVyYXRpb24gcmVxdWVzdFxuXG4gICAgICB0cnkge1xuICAgICAgICAvLyBGb3IgR2VtaW5pIGltYWdlIGdlbmVyYXRpb24sIHdlIG5lZWQgdG8gdXNlIGEgZGlmZmVyZW50IGFwcHJvYWNoXG4gICAgICAgIC8vIFNpbmNlIEdlbWluaSAyLjAgRmxhc2ggUHJldmlldyBtaWdodCBub3Qgc3VwcG9ydCBkaXJlY3QgaW1hZ2UgZ2VuZXJhdGlvbiB5ZXQsXG4gICAgICAgIC8vIGxldCdzIGNyZWF0ZSBhIGNvbXByZWhlbnNpdmUgdGV4dCBkZXNjcmlwdGlvbiBhbmQgdXNlIHRoYXQgZm9yIG5vd1xuXG4gICAgICAgIGNvbnN0IGltYWdlRGVzY3JpcHRpb24gPSB0ZXh0O1xuXG4gICAgICAgIC8vIENyZWF0ZSBhIHBsYWNlaG9sZGVyIGltYWdlIHdpdGggdGhlIGRlc2NyaXB0aW9uXG4gICAgICAgIC8vIEluIGEgcmVhbCBpbXBsZW1lbnRhdGlvbiwgeW91IHdvdWxkIHVzZSBHZW1pbmkncyBhY3R1YWwgaW1hZ2UgZ2VuZXJhdGlvbiBBUElcbiAgICAgICAgLy8gRm9yIG5vdywgd2UnbGwgcmV0dXJuIHRoZSBkZXNjcmlwdGlvbiBhbmQgc3VnZ2VzdCB1c2luZyBpdCB3aXRoIG90aGVyIHRvb2xzXG5cbiAgICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgICAgICB0ZXh0RGVzY3JpcHRpb246IGltYWdlRGVzY3JpcHRpb24sXG4gICAgICAgICAgcHJvbXB0OiBlbmhhbmNlZFByb21wdCxcbiAgICAgICAgICB0aW1lc3RhbXA6IERhdGUubm93KCksXG4gICAgICAgICAgbWVzc2FnZTogJ0dlbWluaSAyLjAgRmxhc2ggaGFzIGdlbmVyYXRlZCBhIGRldGFpbGVkIGRlc2NyaXB0aW9uIG9mIHlvdXIgaW50ZXJpb3IgZGVzaWduIGNvbmNlcHQuJyxcbiAgICAgICAgICBzdWdnZXN0aW9uOiAnVXNlIHRoaXMgZGVzY3JpcHRpb24gd2l0aCBpbWFnZSBnZW5lcmF0aW9uIHRvb2xzIG9yIGFzIGluc3BpcmF0aW9uIGZvciB5b3VyIGRlc2lnbiBwcm9qZWN0LidcbiAgICAgICAgfSk7XG5cbiAgICAgIH0gY2F0Y2ggKGltYWdlRXJyb3I6IGFueSkge1xuICAgICAgICBjb25zb2xlLmxvZygnSW1hZ2UgZ2VuZXJhdGlvbiBub3QgYXZhaWxhYmxlLCByZXR1cm5pbmcgdGV4dCBkZXNjcmlwdGlvbicpO1xuXG4gICAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7XG4gICAgICAgICAgdGV4dERlc2NyaXB0aW9uOiB0ZXh0LFxuICAgICAgICAgIHByb21wdDogZW5oYW5jZWRQcm9tcHQsXG4gICAgICAgICAgdGltZXN0YW1wOiBEYXRlLm5vdygpLFxuICAgICAgICAgIG1lc3NhZ2U6ICdHZW5lcmF0ZWQgZGV0YWlsZWQgaW50ZXJpb3IgZGVzaWduIGRlc2NyaXB0aW9uIHVzaW5nIEdlbWluaSAyLjAgRmxhc2gnLFxuICAgICAgICB9KTtcbiAgICAgIH1cblxuICAgIH0gY2F0Y2ggKGdlbWluaUVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0dvb2dsZSBBSSBBUEkgZXJyb3I6JywgZ2VtaW5pRXJyb3IpO1xuXG4gICAgICBpZiAoZ2VtaW5pRXJyb3IubWVzc2FnZT8uaW5jbHVkZXMoJ3F1b3RhJykgfHwgZ2VtaW5pRXJyb3IubWVzc2FnZT8uaW5jbHVkZXMoJ2V4Y2VlZGVkJykpIHtcbiAgICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICAgIHtcbiAgICAgICAgICAgIGVycm9yOiAnR29vZ2xlIEFJIEFQSSBxdW90YSBleGNlZWRlZC4gUGxlYXNlIGNoZWNrIHlvdXIgQVBJIHVzYWdlIG9yIHVwZ3JhZGUgeW91ciBwbGFuLicsXG4gICAgICAgICAgICBzdWdnZXN0aW9uOiAnVmlzaXQgR29vZ2xlIEFJIFN0dWRpbyB0byBjaGVjayB5b3VyIEFQSSBxdW90YSBhbmQgdXNhZ2UgbGltaXRzLidcbiAgICAgICAgICB9LFxuICAgICAgICAgIHsgc3RhdHVzOiA0MDIgfVxuICAgICAgICApO1xuICAgICAgfVxuXG4gICAgICBpZiAoZ2VtaW5pRXJyb3IubWVzc2FnZT8uaW5jbHVkZXMoJ2ludmFsaWQnKSB8fCBnZW1pbmlFcnJvci5tZXNzYWdlPy5pbmNsdWRlcygna2V5JykpIHtcbiAgICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICAgIHsgZXJyb3I6ICdJbnZhbGlkIEdvb2dsZSBBSSBBUEkga2V5LiBQbGVhc2UgY2hlY2sgeW91ciBjb25maWd1cmF0aW9uLicgfSxcbiAgICAgICAgICB7IHN0YXR1czogNDAxIH1cbiAgICAgICAgKTtcbiAgICAgIH1cblxuICAgICAgaWYgKGdlbWluaUVycm9yLm1lc3NhZ2U/LmluY2x1ZGVzKCdyYXRlIGxpbWl0JykpIHtcbiAgICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICAgIHsgZXJyb3I6ICdSYXRlIGxpbWl0IGV4Y2VlZGVkLiBQbGVhc2Ugd2FpdCBhIG1vbWVudCBiZWZvcmUgdHJ5aW5nIGFnYWluLicgfSxcbiAgICAgICAgICB7IHN0YXR1czogNDI5IH1cbiAgICAgICAgKTtcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKFxuICAgICAgICB7IGVycm9yOiBgRmFpbGVkIHRvIGdlbmVyYXRlIGNvbnRlbnQ6ICR7Z2VtaW5pRXJyb3IubWVzc2FnZSB8fCAnVW5rbm93biBlcnJvcid9YCB9LFxuICAgICAgICB7IHN0YXR1czogNTAwIH1cbiAgICAgICk7XG4gICAgfVxuXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcignRXJyb3IgaW4gZ2VuZXJhdGUtaW1hZ2UgQVBJOicsIGVycm9yKTtcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oXG4gICAgICB7IGVycm9yOiAnSW50ZXJuYWwgc2VydmVyIGVycm9yLiBQbGVhc2UgdHJ5IGFnYWluLicgfSxcbiAgICAgIHsgc3RhdHVzOiA1MDAgfVxuICAgICk7XG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJOZXh0UmVzcG9uc2UiLCJHb29nbGVHZW5lcmF0aXZlQUkiLCJQT1NUIiwicmVxdWVzdCIsInByb21wdCIsInVwbG9hZGVkSW1hZ2UiLCJqc29uIiwiZXJyb3IiLCJzdGF0dXMiLCJhcGlLZXkiLCJwcm9jZXNzIiwiZW52IiwiR09PR0xFX0FJX0FQSV9LRVkiLCJjb25zb2xlIiwiaW50ZXJpb3JLZXl3b3JkcyIsImxvd2VyUHJvbXB0IiwidG9Mb3dlckNhc2UiLCJpc0ludGVyaW9yUmVsYXRlZCIsInNvbWUiLCJrZXl3b3JkIiwiaW5jbHVkZXMiLCJnZW5BSSIsIm1vZGVsIiwiZ2V0R2VuZXJhdGl2ZU1vZGVsIiwiZW5oYW5jZWRQcm9tcHQiLCJsb2ciLCJjb250ZW50UGFydHMiLCJ0ZXh0IiwicHVzaCIsImlubGluZURhdGEiLCJtaW1lVHlwZSIsImRhdGEiLCJzcGxpdCIsInJlc3VsdCIsImdlbmVyYXRlQ29udGVudCIsInJlc3BvbnNlIiwiY2FuZGlkYXRlcyIsImNhbmRpZGF0ZSIsImNvbnRlbnQiLCJwYXJ0cyIsInBhcnQiLCJpbWFnZURhdGEiLCJpbWFnZVVybCIsInRpbWVzdGFtcCIsIkRhdGUiLCJub3ciLCJtZXNzYWdlIiwiaW1hZ2VEZXNjcmlwdGlvbiIsInRleHREZXNjcmlwdGlvbiIsInN1Z2dlc3Rpb24iLCJpbWFnZUVycm9yIiwiZ2VtaW5pRXJyb3IiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-image/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@google"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=C%3A%5CFlame%20workspace%5Cmorderm-haven%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CFlame%20workspace%5Cmorderm-haven&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();