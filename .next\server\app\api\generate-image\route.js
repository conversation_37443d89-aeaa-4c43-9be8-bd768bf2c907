"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-image/route";
exports.ids = ["app/api/generate-image/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=C%3A%5CFlame%20workspace%5Cmorderm-haven%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CFlame%20workspace%5Cmorderm-haven&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=C%3A%5CFlame%20workspace%5Cmorderm-haven%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CFlame%20workspace%5Cmorderm-haven&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Flame_workspace_morderm_haven_src_app_api_generate_image_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-image/route.ts */ \"(rsc)/./src/app/api/generate-image/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-image/route\",\n        pathname: \"/api/generate-image\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-image/route\"\n    },\n    resolvedPagePath: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\api\\\\generate-image\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Flame_workspace_morderm_haven_src_app_api_generate_image_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/generate-image/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=C%3A%5CFlame%20workspace%5Cmorderm-haven%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CFlame%20workspace%5Cmorderm-haven&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/generate-image/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/generate-image/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @google/generative-ai */ \"(rsc)/./node_modules/@google/generative-ai/dist/index.mjs\");\n\n\nasync function POST(request) {\n    try {\n        const { prompt, uploadedImage } = await request.json();\n        if (!prompt) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Prompt is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Get the Google AI API key from environment variables\n        const apiKey = process.env.GOOGLE_AI_API_KEY;\n        if (!apiKey) {\n            console.error(\"GOOGLE_AI_API_KEY not found in environment variables\");\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"API configuration error\"\n            }, {\n                status: 500\n            });\n        }\n        // Interior design validation - ensure prompt is related to interior design\n        const interiorKeywords = [\n            \"kitchen\",\n            \"bedroom\",\n            \"living room\",\n            \"bathroom\",\n            \"dining room\",\n            \"office\",\n            \"closet\",\n            \"interior\",\n            \"home\",\n            \"house\",\n            \"apartment\",\n            \"room\",\n            \"space\",\n            \"design\",\n            \"furniture\",\n            \"cabinet\",\n            \"counter\",\n            \"sofa\",\n            \"chair\",\n            \"table\",\n            \"bed\",\n            \"shelf\",\n            \"storage\",\n            \"modern\",\n            \"contemporary\",\n            \"traditional\",\n            \"minimalist\",\n            \"luxury\",\n            \"cozy\",\n            \"lighting\",\n            \"window\",\n            \"wall\",\n            \"floor\",\n            \"ceiling\",\n            \"decor\",\n            \"renovation\"\n        ];\n        const lowerPrompt = prompt.toLowerCase();\n        const isInteriorRelated = interiorKeywords.some((keyword)=>lowerPrompt.includes(keyword));\n        if (!isInteriorRelated) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Please provide an interior design or home-related prompt\"\n            }, {\n                status: 400\n            });\n        }\n        // Initialize Google AI client\n        const genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_1__.GoogleGenerativeAI(apiKey);\n        try {\n            // Use Gemini 2.0 Flash Preview Image Generation\n            const model = genAI.getGenerativeModel({\n                model: \"gemini-2.0-flash-preview-image-generation\"\n            });\n            // Enhanced prompt for better interior design results\n            const enhancedPrompt = `Create a high-quality, professional interior design image: ${prompt}. Style: photorealistic, architectural photography, professional lighting, high resolution, detailed textures, modern design aesthetic.`;\n            console.log(\"Generating image with Gemini 2.0 Flash Preview Image Generation...\");\n            // Prepare the content array\n            const contentParts = [\n                {\n                    text: enhancedPrompt\n                }\n            ];\n            // Add uploaded image if provided\n            if (uploadedImage) {\n                contentParts.push({\n                    inlineData: {\n                        mimeType: \"image/jpeg\",\n                        data: uploadedImage.split(\",\")[1] // Remove data:image/jpeg;base64, prefix\n                    }\n                });\n            }\n            // For image generation models, we need to specify that we want both image and text\n            const result = await model.generateContent({\n                contents: [\n                    {\n                        role: \"user\",\n                        parts: contentParts\n                    }\n                ]\n            });\n            const response = await result.response;\n            // Check if response has candidates with parts\n            if (response.candidates && response.candidates[0] && response.candidates[0].content) {\n                const parts = response.candidates[0].content.parts;\n                let imageData = null;\n                let textDescription = \"\";\n                // Look through all parts for image and text\n                for (const part of parts){\n                    if (part.inlineData && part.inlineData.mimeType?.startsWith(\"image/\")) {\n                        // Found image data\n                        imageData = part.inlineData.data;\n                    } else if (part.text) {\n                        // Found text description\n                        textDescription += part.text;\n                    }\n                }\n                if (imageData) {\n                    // Successfully generated image\n                    const imageUrl = `data:image/png;base64,${imageData}`;\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        imageUrl,\n                        textDescription: textDescription || \"Generated interior design image\",\n                        prompt,\n                        timestamp: Date.now(),\n                        message: \"Successfully generated interior design image\"\n                    });\n                } else if (textDescription) {\n                    // Got text description only\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        textDescription,\n                        prompt,\n                        timestamp: Date.now(),\n                        message: \"Generated detailed interior design description\"\n                    });\n                }\n            }\n            // Fallback: try to get text response\n            const text = response.text();\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                textDescription: text,\n                prompt,\n                timestamp: Date.now(),\n                message: \"Generated interior design concept\"\n            });\n        } catch (geminiError) {\n            console.error(\"Google AI API error:\", geminiError);\n            if (geminiError.message?.includes(\"quota\") || geminiError.message?.includes(\"exceeded\")) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Google AI API quota exceeded. Please check your API usage or upgrade your plan.\",\n                    suggestion: \"Visit Google AI Studio to check your API quota and usage limits.\"\n                }, {\n                    status: 402\n                });\n            }\n            if (geminiError.message?.includes(\"invalid\") || geminiError.message?.includes(\"key\")) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Invalid Google AI API key. Please check your configuration.\"\n                }, {\n                    status: 401\n                });\n            }\n            if (geminiError.message?.includes(\"rate limit\")) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Rate limit exceeded. Please wait a moment before trying again.\"\n                }, {\n                    status: 429\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Failed to generate content: ${geminiError.message || \"Unknown error\"}`\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        console.error(\"Error in generate-image API:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error. Please try again.\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-image/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@google"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=C%3A%5CFlame%20workspace%5Cmorderm-haven%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CFlame%20workspace%5Cmorderm-haven&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();