"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/generate-image/route";
exports.ids = ["app/api/generate-image/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=C%3A%5CFlame%20workspace%5Cmorderm-haven%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CFlame%20workspace%5Cmorderm-haven&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=C%3A%5CFlame%20workspace%5Cmorderm-haven%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CFlame%20workspace%5Cmorderm-haven&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Flame_workspace_morderm_haven_src_app_api_generate_image_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/generate-image/route.ts */ \"(rsc)/./src/app/api/generate-image/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/generate-image/route\",\n        pathname: \"/api/generate-image\",\n        filename: \"route\",\n        bundlePath: \"app/api/generate-image/route\"\n    },\n    resolvedPagePath: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\api\\\\generate-image\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Flame_workspace_morderm_haven_src_app_api_generate_image_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/generate-image/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=C%3A%5CFlame%20workspace%5Cmorderm-haven%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CFlame%20workspace%5Cmorderm-haven&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/generate-image/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/generate-image/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nasync function POST(request) {\n    try {\n        const { prompt } = await request.json();\n        if (!prompt) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Prompt is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Get the API key from environment variables\n        const apiKey = process.env.HUGGINGFACE_API_KEY;\n        if (!apiKey) {\n            console.error(\"HUGGINGFACE_API_KEY not found in environment variables\");\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"API configuration error\"\n            }, {\n                status: 500\n            });\n        }\n        // Interior design validation - ensure prompt is related to interior design\n        const interiorKeywords = [\n            \"kitchen\",\n            \"bedroom\",\n            \"living room\",\n            \"bathroom\",\n            \"dining room\",\n            \"office\",\n            \"closet\",\n            \"interior\",\n            \"home\",\n            \"house\",\n            \"apartment\",\n            \"room\",\n            \"space\",\n            \"design\",\n            \"furniture\",\n            \"cabinet\",\n            \"counter\",\n            \"sofa\",\n            \"chair\",\n            \"table\",\n            \"bed\",\n            \"shelf\",\n            \"storage\",\n            \"modern\",\n            \"contemporary\",\n            \"traditional\",\n            \"minimalist\",\n            \"luxury\",\n            \"cozy\",\n            \"lighting\",\n            \"window\",\n            \"wall\",\n            \"floor\",\n            \"ceiling\",\n            \"decor\",\n            \"renovation\"\n        ];\n        const lowerPrompt = prompt.toLowerCase();\n        const isInteriorRelated = interiorKeywords.some((keyword)=>lowerPrompt.includes(keyword));\n        if (!isInteriorRelated) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Please provide an interior design or home-related prompt\"\n            }, {\n                status: 400\n            });\n        }\n        // Use Hugging Face Inference API with FLUX.1-dev model\n        const response = await fetch(\"https://api-inference.huggingface.co/models/black-forest-labs/FLUX.1-dev\", {\n            method: \"POST\",\n            headers: {\n                \"Authorization\": `Bearer ${apiKey}`,\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                inputs: prompt,\n                parameters: {\n                    guidance_scale: 7.5,\n                    num_inference_steps: 28,\n                    width: 1024,\n                    height: 1024,\n                    seed: Math.floor(Math.random() * 1000000)\n                }\n            })\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error(\"Hugging Face API error:\", response.status, errorText);\n            if (response.status === 503) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Model is currently loading. Please try again in a few moments.\"\n                }, {\n                    status: 503\n                });\n            }\n            if (response.status === 429) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Rate limit exceeded. Please wait a moment before trying again.\"\n                }, {\n                    status: 429\n                });\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to generate image. Please try again.\"\n            }, {\n                status: response.status\n            });\n        }\n        // Get the image as a blob\n        const imageBlob = await response.blob();\n        // Convert blob to base64 for sending to client\n        const arrayBuffer = await imageBlob.arrayBuffer();\n        const base64 = Buffer.from(arrayBuffer).toString(\"base64\");\n        const imageUrl = `data:image/png;base64,${base64}`;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            imageUrl,\n            prompt,\n            timestamp: Date.now()\n        });\n    } catch (error) {\n        console.error(\"Error in generate-image API:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error. Please try again.\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/generate-image/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fgenerate-image%2Froute&page=%2Fapi%2Fgenerate-image%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fgenerate-image%2Froute.ts&appDir=C%3A%5CFlame%20workspace%5Cmorderm-haven%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CFlame%20workspace%5Cmorderm-haven&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();