/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/gallery/page";
exports.ids = ["app/gallery/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fgallery%2Fpage&page=%2Fgallery%2Fpage&appPaths=%2Fgallery%2Fpage&pagePath=private-next-app-dir%2Fgallery%2Fpage.tsx&appDir=C%3A%5CFlame%20workspace%5Cmorderm-haven%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CFlame%20workspace%5Cmorderm-haven&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fgallery%2Fpage&page=%2Fgallery%2Fpage&appPaths=%2Fgallery%2Fpage&pagePath=private-next-app-dir%2Fgallery%2Fpage.tsx&appDir=C%3A%5CFlame%20workspace%5Cmorderm-haven%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CFlame%20workspace%5Cmorderm-haven&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'gallery',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/gallery/page.tsx */ \"(rsc)/./src/app/gallery/page.tsx\")), \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/gallery/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/gallery/page\",\n        pathname: \"/gallery\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fgallery%2Fpage&page=%2Fgallery%2Fpage&appPaths=%2Fgallery%2Fpage&pagePath=private-next-app-dir%2Fgallery%2Fpage.tsx&appDir=C%3A%5CFlame%20workspace%5Cmorderm-haven%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CFlame%20workspace%5Cmorderm-haven&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Csrc%5C%5Capp%5C%5Cgallery%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Csrc%5C%5Capp%5C%5Cgallery%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/gallery/page.tsx */ \"(ssr)/./src/app/gallery/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNGbGFtZSUyMHdvcmtzcGFjZSU1QyU1Q21vcmRlcm0taGF2ZW4lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnYWxsZXJ5JTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdLQUFvRyIsInNvdXJjZXMiOlsid2VicGFjazovL21vZGVybi1oYXZlbi8/NWRiMCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXEZsYW1lIHdvcmtzcGFjZVxcXFxtb3JkZXJtLWhhdmVuXFxcXHNyY1xcXFxhcHBcXFxcZ2FsbGVyeVxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CFlame%20workspace%5C%5Cmorderm-haven%5C%5Csrc%5C%5Capp%5C%5Cgallery%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/gallery/page.tsx":
/*!**********************************!*\
  !*** ./src/app/gallery/page.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Gallery)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(ssr)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Gallery() {\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const openModal = (image)=>{\n        setSelectedImage(image);\n    };\n    const closeModal = ()=>{\n        setSelectedImage(null);\n    };\n    // Handle keyboard events\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        const handleKeyDown = (event)=>{\n            if (event.key === \"Escape\" && selectedImage) {\n                closeModal();\n            }\n        };\n        if (selectedImage) {\n            document.addEventListener(\"keydown\", handleKeyDown);\n            document.body.style.overflow = \"hidden\"; // Prevent background scrolling\n        } else {\n            document.body.style.overflow = \"unset\";\n        }\n        return ()=>{\n            document.removeEventListener(\"keydown\", handleKeyDown);\n            document.body.style.overflow = \"unset\";\n        };\n    }, [\n        selectedImage\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"bg-white shadow-sm border-b border-forest-green-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/\",\n                                    className: \"text-2xl font-bold text-forest-green-600\",\n                                    children: \"Modern Haven\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:block\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-10 flex items-baseline space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"Home\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"#about\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"About\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"#services\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"Services\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/gallery\",\n                                            className: \"bg-gold-500 text-white px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"Gallery\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/ai-design\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"AI Design\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"#contact\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"Contact\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative bg-gradient-to-br from-forest-green-50 to-white py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-5xl font-bold text-forest-green-700 mb-6\",\n                                children: [\n                                    \"Our \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gold-500\",\n                                        children: \"Gallery\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-forest-green-600 mb-8 max-w-3xl mx-auto\",\n                                children: \"Explore our diverse collections of interior design projects, from modern kitchens to organized storage solutions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl md:text-4xl font-bold text-forest-green-700 mb-6 text-center\",\n                                children: [\n                                    \"Kitchen \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gold-500\",\n                                        children: \"Reconstruction\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 23\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-forest-green-600 text-center max-w-4xl mx-auto mb-12\",\n                                children: \"Witness the complete transformation of a kitchen space from start to finish. This collection showcases our expertise in full-scale kitchen renovations, featuring modern design principles, functional layouts, and premium finishes that create the heart of the home.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/kitchen-reconstruction/IMG_7906.jpg\",\n                                                alt: \"Kitchen Reconstruction - Initial Phase\",\n                                                title: \"Initial Phase\",\n                                                description: \"The beginning of our comprehensive kitchen renovation project. This phase involves planning, measurements, and preparing the space for transformation.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/kitchen-reconstruction/IMG_7906.jpg\",\n                                                    alt: \"Kitchen Reconstruction - Initial Phase\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Initial Phase\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"The beginning of our comprehensive kitchen renovation project.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/kitchen-reconstruction/IMG_7908.jpg\",\n                                                alt: \"Kitchen Reconstruction - Demolition\",\n                                                title: \"Demolition Phase\",\n                                                description: \"Careful removal of existing structures to prepare for the new design. This phase requires precision to preserve structural integrity while clearing space for improvements.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/kitchen-reconstruction/IMG_7908.jpg\",\n                                                    alt: \"Kitchen Reconstruction - Demolition\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Demolition Phase\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Careful removal of existing structures to prepare for the new design.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/kitchen-reconstruction/IMG_7910.jpg\",\n                                                alt: \"Kitchen Reconstruction - Structural Work\",\n                                                title: \"Structural Work\",\n                                                description: \"Foundation and structural improvements for the new kitchen layout. This phase includes electrical, plumbing, and framework modifications to support the new design.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/kitchen-reconstruction/IMG_7910.jpg\",\n                                                    alt: \"Kitchen Reconstruction - Structural Work\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Structural Work\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Foundation and structural improvements for the new kitchen layout.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/kitchen-reconstruction/IMG_7911.jpg\",\n                                                alt: \"Kitchen Reconstruction - Installation\",\n                                                title: \"Installation Phase\",\n                                                description: \"Installing cabinets, countertops, and essential kitchen elements. This phase brings the design to life with custom cabinetry and premium materials.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/kitchen-reconstruction/IMG_7911.jpg\",\n                                                    alt: \"Kitchen Reconstruction - Installation\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Installation Phase\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Installing cabinets, countertops, and essential kitchen elements.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/kitchen-reconstruction/IMG_7913.jpg\",\n                                                alt: \"Kitchen Reconstruction - Finishing Touches\",\n                                                title: \"Finishing Touches\",\n                                                description: \"Adding the final details that bring the design vision to life. This includes hardware installation, lighting fixtures, and decorative elements.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/kitchen-reconstruction/IMG_7913.jpg\",\n                                                    alt: \"Kitchen Reconstruction - Finishing Touches\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Finishing Touches\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Adding the final details that bring the design vision to life.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/kitchen-reconstruction/IMG_7915.jpg\",\n                                                alt: \"Kitchen Reconstruction - Completed\",\n                                                title: \"Final Result\",\n                                                description: \"The stunning completed kitchen transformation. A perfect blend of functionality and style, showcasing modern design principles and premium craftsmanship.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/kitchen-reconstruction/IMG_7915.jpg\",\n                                                    alt: \"Kitchen Reconstruction - Completed\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Final Result\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"The stunning completed kitchen transformation.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-forest-green-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl md:text-4xl font-bold text-forest-green-700 mb-6 text-center\",\n                                children: [\n                                    \"Modern \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gold-500\",\n                                        children: \"Kitchen Designs\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 22\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-forest-green-600 text-center max-w-4xl mx-auto mb-12\",\n                                children: \"Our collection of contemporary kitchen designs featuring sleek lines, innovative storage solutions, and cutting-edge appliances. Each design balances functionality with aesthetic appeal to create the perfect culinary workspace.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/Kitchen/IMG_6009.PNG\",\n                                                alt: \"Modern Kitchen Design 1\",\n                                                title: \"Minimalist Excellence\",\n                                                description: \"Clean lines and neutral tones create a serene cooking environment. This design emphasizes simplicity and functionality with hidden storage solutions and streamlined appliances.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg overflow-hidden h-80 mb-4 shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Kitchen/IMG_6009.PNG\",\n                                                    alt: \"Modern Kitchen Design 1\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Minimalist Excellence\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Clean lines and neutral tones create a serene cooking environment.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/Kitchen/IMG_6010.PNG\",\n                                                alt: \"Modern Kitchen Design 2\",\n                                                title: \"Contemporary Elegance\",\n                                                description: \"Sophisticated design with premium materials and finishes. Features high-end appliances, custom cabinetry, and elegant lighting that creates a luxurious cooking experience.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg overflow-hidden h-80 mb-4 shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Kitchen/IMG_6010.PNG\",\n                                                    alt: \"Modern Kitchen Design 2\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Contemporary Elegance\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Sophisticated design with premium materials and finishes.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/Kitchen/IMG_6711.JPG\",\n                                                alt: \"Modern Kitchen Design 3\",\n                                                title: \"Functional Beauty\",\n                                                description: \"Perfect blend of style and practicality for everyday living. Smart storage solutions and ergonomic design make cooking and entertaining effortless and enjoyable.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg overflow-hidden h-80 mb-4 shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Kitchen/IMG_6711.JPG\",\n                                                    alt: \"Modern Kitchen Design 3\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Functional Beauty\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Perfect blend of style and practicality for everyday living.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/Kitchen/IMG_6712.JPG\",\n                                                alt: \"Modern Kitchen Design 4\",\n                                                title: \"Luxury Living\",\n                                                description: \"High-end appliances and custom cabinetry for the discerning homeowner. Premium materials and sophisticated finishes create an upscale culinary environment.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg overflow-hidden h-80 mb-4 shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Kitchen/IMG_6712.JPG\",\n                                                    alt: \"Modern Kitchen Design 4\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Luxury Living\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"High-end appliances and custom cabinetry for the discerning homeowner.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/Kitchen/IMG_6713.JPG\",\n                                                alt: \"Modern Kitchen Design 5\",\n                                                title: \"Smart Design\",\n                                                description: \"Innovative storage solutions maximize space and efficiency. Clever organization systems and multi-functional elements make the most of every square inch.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg overflow-hidden h-80 mb-4 shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Kitchen/IMG_6713.JPG\",\n                                                    alt: \"Modern Kitchen Design 5\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Smart Design\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Innovative storage solutions maximize space and efficiency.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/Kitchen/IMG_6714.JPG\",\n                                                alt: \"Modern Kitchen Design 6\",\n                                                title: \"Timeless Appeal\",\n                                                description: \"Classic design elements with modern functionality. This kitchen combines traditional aesthetics with contemporary conveniences for enduring style and performance.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg overflow-hidden h-80 mb-4 shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Kitchen/IMG_6714.JPG\",\n                                                    alt: \"Modern Kitchen Design 6\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Timeless Appeal\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Classic design elements with modern functionality.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl md:text-4xl font-bold text-forest-green-700 mb-6 text-center\",\n                                children: [\n                                    \"Closet \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gold-500\",\n                                        children: \"Organization\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 22\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-forest-green-600 text-center max-w-4xl mx-auto mb-12\",\n                                children: \"Transform your storage spaces with our custom closet organization solutions. From walk-in wardrobes to compact storage areas, we create systems that maximize space while maintaining style and accessibility.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/Closet/IMG_6003.PNG\",\n                                                alt: \"Closet Organization 1\",\n                                                title: \"Walk-in Wardrobe\",\n                                                description: \"Luxurious walk-in closet with custom shelving and hanging solutions. Features dedicated spaces for different clothing types, shoes, and accessories with elegant lighting.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Closet/IMG_6003.PNG\",\n                                                    alt: \"Closet Organization 1\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Walk-in Wardrobe\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 415,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Luxurious walk-in closet with custom shelving and hanging solutions.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/Closet/IMG_6004.PNG\",\n                                                alt: \"Closet Organization 2\",\n                                                title: \"Organized Storage\",\n                                                description: \"Efficient storage system with designated spaces for every item. Smart organization solutions that make finding and storing belongings effortless and intuitive.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Closet/IMG_6004.PNG\",\n                                                    alt: \"Closet Organization 2\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Organized Storage\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Efficient storage system with designated spaces for every item.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/Closet/IMG_6005.PNG\",\n                                                alt: \"Closet Organization 3\",\n                                                title: \"Custom Solutions\",\n                                                description: \"Tailored organization systems designed for your specific needs. Every element is customized to fit your lifestyle, space constraints, and storage requirements.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Closet/IMG_6005.PNG\",\n                                                    alt: \"Closet Organization 3\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 451,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Custom Solutions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Tailored organization systems designed for your specific needs.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/Closet/IMG_6006.PNG\",\n                                                alt: \"Closet Organization 4\",\n                                                title: \"Space Optimization\",\n                                                description: \"Maximizing every inch of available space with smart design. Vertical storage solutions and multi-level organization create maximum capacity in minimal footprint.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Closet/IMG_6006.PNG\",\n                                                    alt: \"Closet Organization 4\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 473,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Space Optimization\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Maximizing every inch of available space with smart design.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 463,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/Closet/IMG_6007.PNG\",\n                                                alt: \"Closet Organization 5\",\n                                                title: \"Elegant Design\",\n                                                description: \"Beautiful and functional closet design that complements your home. Sophisticated finishes and thoughtful details create a luxurious storage experience.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Closet/IMG_6007.PNG\",\n                                                    alt: \"Closet Organization 5\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Elegant Design\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 503,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Beautiful and functional closet design that complements your home.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/Closet/IMG_6008.PNG\",\n                                                alt: \"Closet Organization 6\",\n                                                title: \"Complete Organization\",\n                                                description: \"Comprehensive storage solution for all your belongings. Every item has its place in this thoughtfully designed organization system that maintains order and accessibility.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Closet/IMG_6008.PNG\",\n                                                    alt: \"Closet Organization 6\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Complete Organization\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 525,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Comprehensive storage solution for all your belongings.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 526,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                        lineNumber: 386,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                    lineNumber: 385,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                lineNumber: 384,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-forest-green-600\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-white mb-6\",\n                            children: [\n                                \"Ready to Start Your \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gold-400\",\n                                    children: \"Project?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 537,\n                                    columnNumber: 33\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                            lineNumber: 536,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-forest-green-100 mb-8 max-w-2xl mx-auto\",\n                            children: \"Let's bring your vision to life. Contact us today to discuss your interior design needs.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                            lineNumber: 539,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/#contact\",\n                                    className: \"bg-gold-500 text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-gold-600 transition duration-300\",\n                                    children: \"Get Started\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 543,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/\",\n                                    className: \"border-2 border-white text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-white hover:text-forest-green-600 transition duration-300\",\n                                    children: \"Back to Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 546,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                            lineNumber: 542,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                    lineNumber: 535,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                lineNumber: 534,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-forest-green-800 text-white py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-1 md:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-gold-400 mb-4\",\n                                            children: \"Modern Haven\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 558,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-forest-green-200 mb-4\",\n                                            children: \"Creating beautiful, functional spaces that reflect your unique style and enhance your lifestyle. Your dream home is just a consultation away.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 559,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gold-400 hover:text-gold-300\",\n                                                    children: \"\\uD83D\\uDCD8\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 564,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gold-400 hover:text-gold-300\",\n                                                    children: \"\\uD83D\\uDCF7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 565,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gold-400 hover:text-gold-300\",\n                                                    children: \"\\uD83D\\uDC26\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 566,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gold-400 hover:text-gold-300\",\n                                                    children: \"\\uD83D\\uDCBC\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 567,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 563,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 557,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold text-gold-400 mb-4\",\n                                            children: \"Quick Links\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 571,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-forest-green-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"Home\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                        lineNumber: 573,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 573,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/#about\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"About\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                        lineNumber: 574,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 574,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/#services\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"Services\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                        lineNumber: 575,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 575,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/gallery\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"Gallery\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                        lineNumber: 576,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 576,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/#contact\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"Contact\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                        lineNumber: 577,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 577,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 572,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 570,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold text-gold-400 mb-4\",\n                                            children: \"Collections\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 581,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-forest-green-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Kitchen Reconstruction\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 583,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Modern Kitchen Designs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 584,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Closet Organization\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 585,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Custom Storage Solutions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 586,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Interior Styling\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 587,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 582,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 580,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                            lineNumber: 556,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-forest-green-700 mt-8 pt-8 text-center text-forest-green-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"\\xa9 2024 Modern Haven Interior Design. All rights reserved.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 592,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                            lineNumber: 591,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                    lineNumber: 555,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                lineNumber: 554,\n                columnNumber: 7\n            }, this),\n            selectedImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4\",\n                onClick: closeModal,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative max-w-4xl max-h-full bg-white rounded-lg overflow-hidden\",\n                    onClick: (e)=>e.stopPropagation(),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: closeModal,\n                            className: \"absolute top-4 right-4 z-10 bg-forest-green-600 text-white rounded-full w-10 h-10 flex items-center justify-center hover:bg-forest-green-700 transition-colors\",\n                            children: \"✕\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                            lineNumber: 608,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                src: selectedImage.src,\n                                alt: selectedImage.alt,\n                                width: 1200,\n                                height: 800,\n                                className: \"w-full h-auto max-h-[80vh] object-contain\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 617,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                            lineNumber: 616,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 bg-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-forest-green-700 mb-2\",\n                                    children: selectedImage.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 628,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-forest-green-600\",\n                                    children: selectedImage.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 629,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                            lineNumber: 627,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                    lineNumber: 603,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                lineNumber: 599,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2dhbGxlcnkvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFDK0I7QUFDRjtBQUNlO0FBUzdCLFNBQVNJO0lBQ3RCLE1BQU0sQ0FBQ0MsZUFBZUMsaUJBQWlCLEdBQUdKLCtDQUFRQSxDQUFvQjtJQUV0RSxNQUFNSyxZQUFZLENBQUNDO1FBQ2pCRixpQkFBaUJFO0lBQ25CO0lBRUEsTUFBTUMsYUFBYTtRQUNqQkgsaUJBQWlCO0lBQ25CO0lBRUEseUJBQXlCO0lBQ3pCSCxnREFBU0EsQ0FBQztRQUNSLE1BQU1PLGdCQUFnQixDQUFDQztZQUNyQixJQUFJQSxNQUFNQyxHQUFHLEtBQUssWUFBWVAsZUFBZTtnQkFDM0NJO1lBQ0Y7UUFDRjtRQUVBLElBQUlKLGVBQWU7WUFDakJRLFNBQVNDLGdCQUFnQixDQUFDLFdBQVdKO1lBQ3JDRyxTQUFTRSxJQUFJLENBQUNDLEtBQUssQ0FBQ0MsUUFBUSxHQUFHLFVBQVUsK0JBQStCO1FBQzFFLE9BQU87WUFDTEosU0FBU0UsSUFBSSxDQUFDQyxLQUFLLENBQUNDLFFBQVEsR0FBRztRQUNqQztRQUVBLE9BQU87WUFDTEosU0FBU0ssbUJBQW1CLENBQUMsV0FBV1I7WUFDeENHLFNBQVNFLElBQUksQ0FBQ0MsS0FBSyxDQUFDQyxRQUFRLEdBQUc7UUFDakM7SUFDRixHQUFHO1FBQUNaO0tBQWM7SUFDbEIscUJBQ0UsOERBQUNjO1FBQUtDLFdBQVU7OzBCQUVkLDhEQUFDQztnQkFBSUQsV0FBVTswQkFDYiw0RUFBQ0U7b0JBQUlGLFdBQVU7OEJBQ2IsNEVBQUNFO3dCQUFJRixXQUFVOzswQ0FDYiw4REFBQ0U7Z0NBQUlGLFdBQVU7MENBQ2IsNEVBQUNuQixpREFBSUE7b0NBQUNzQixNQUFLO29DQUFJSCxXQUFVOzhDQUEyQzs7Ozs7Ozs7Ozs7MENBRXRFLDhEQUFDRTtnQ0FBSUYsV0FBVTswQ0FDYiw0RUFBQ0U7b0NBQUlGLFdBQVU7O3NEQUNiLDhEQUFDbkIsaURBQUlBOzRDQUFDc0IsTUFBSzs0Q0FBSUgsV0FBVTtzREFBcUY7Ozs7OztzREFDOUcsOERBQUNuQixpREFBSUE7NENBQUNzQixNQUFLOzRDQUFTSCxXQUFVO3NEQUFxRjs7Ozs7O3NEQUNuSCw4REFBQ25CLGlEQUFJQTs0Q0FBQ3NCLE1BQUs7NENBQVlILFdBQVU7c0RBQXFGOzs7Ozs7c0RBQ3RILDhEQUFDbkIsaURBQUlBOzRDQUFDc0IsTUFBSzs0Q0FBV0gsV0FBVTtzREFBa0U7Ozs7OztzREFDbEcsOERBQUNuQixpREFBSUE7NENBQUNzQixNQUFLOzRDQUFhSCxXQUFVO3NEQUFxRjs7Ozs7O3NEQUN2SCw4REFBQ25CLGlEQUFJQTs0Q0FBQ3NCLE1BQUs7NENBQVdILFdBQVU7c0RBQXFGOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUS9ILDhEQUFDSTtnQkFBUUosV0FBVTswQkFDakIsNEVBQUNFO29CQUFJRixXQUFVOzhCQUNiLDRFQUFDRTt3QkFBSUYsV0FBVTs7MENBQ2IsOERBQUNLO2dDQUFHTCxXQUFVOztvQ0FBNEQ7a0RBQ3BFLDhEQUFDTTt3Q0FBS04sV0FBVTtrREFBZ0I7Ozs7Ozs7Ozs7OzswQ0FFdEMsOERBQUNPO2dDQUFFUCxXQUFVOzBDQUF1RDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFRMUUsOERBQUNJO2dCQUFRSixXQUFVOzBCQUNqQiw0RUFBQ0U7b0JBQUlGLFdBQVU7OEJBQ2IsNEVBQUNFO3dCQUFJRixXQUFVOzswQ0FDYiw4REFBQ1E7Z0NBQUdSLFdBQVU7O29DQUF3RTtrREFDNUUsOERBQUNNO3dDQUFLTixXQUFVO2tEQUFnQjs7Ozs7Ozs7Ozs7OzBDQUUxQyw4REFBQ087Z0NBQUVQLFdBQVU7MENBQW9FOzs7Ozs7MENBTWpGLDhEQUFDRTtnQ0FBSUYsV0FBVTs7a0RBRWIsOERBQUNFO3dDQUNDRixXQUFVO3dDQUNWUyxTQUFTLElBQU10QixVQUFVO2dEQUN2QnVCLEtBQUs7Z0RBQ0xDLEtBQUs7Z0RBQ0xDLE9BQU87Z0RBQ1BDLGFBQWE7NENBQ2Y7OzBEQUVBLDhEQUFDWDtnREFBSUYsV0FBVTswREFDYiw0RUFBQ3BCLGtEQUFLQTtvREFDSjhCLEtBQUk7b0RBQ0pDLEtBQUk7b0RBQ0pHLE9BQU87b0RBQ1BDLFFBQVE7b0RBQ1JmLFdBQVU7Ozs7Ozs7Ozs7OzBEQUdkLDhEQUFDZ0I7Z0RBQUdoQixXQUFVOzBEQUFtRDs7Ozs7OzBEQUNqRSw4REFBQ087Z0RBQUVQLFdBQVU7MERBQXdCOzs7Ozs7Ozs7Ozs7a0RBR3ZDLDhEQUFDRTt3Q0FDQ0YsV0FBVTt3Q0FDVlMsU0FBUyxJQUFNdEIsVUFBVTtnREFDdkJ1QixLQUFLO2dEQUNMQyxLQUFLO2dEQUNMQyxPQUFPO2dEQUNQQyxhQUFhOzRDQUNmOzswREFFQSw4REFBQ1g7Z0RBQUlGLFdBQVU7MERBQ2IsNEVBQUNwQixrREFBS0E7b0RBQ0o4QixLQUFJO29EQUNKQyxLQUFJO29EQUNKRyxPQUFPO29EQUNQQyxRQUFRO29EQUNSZixXQUFVOzs7Ozs7Ozs7OzswREFHZCw4REFBQ2dCO2dEQUFHaEIsV0FBVTswREFBbUQ7Ozs7OzswREFDakUsOERBQUNPO2dEQUFFUCxXQUFVOzBEQUF3Qjs7Ozs7Ozs7Ozs7O2tEQUd2Qyw4REFBQ0U7d0NBQ0NGLFdBQVU7d0NBQ1ZTLFNBQVMsSUFBTXRCLFVBQVU7Z0RBQ3ZCdUIsS0FBSztnREFDTEMsS0FBSztnREFDTEMsT0FBTztnREFDUEMsYUFBYTs0Q0FDZjs7MERBRUEsOERBQUNYO2dEQUFJRixXQUFVOzBEQUNiLDRFQUFDcEIsa0RBQUtBO29EQUNKOEIsS0FBSTtvREFDSkMsS0FBSTtvREFDSkcsT0FBTztvREFDUEMsUUFBUTtvREFDUmYsV0FBVTs7Ozs7Ozs7Ozs7MERBR2QsOERBQUNnQjtnREFBR2hCLFdBQVU7MERBQW1EOzs7Ozs7MERBQ2pFLDhEQUFDTztnREFBRVAsV0FBVTswREFBd0I7Ozs7Ozs7Ozs7OztrREFHdkMsOERBQUNFO3dDQUNDRixXQUFVO3dDQUNWUyxTQUFTLElBQU10QixVQUFVO2dEQUN2QnVCLEtBQUs7Z0RBQ0xDLEtBQUs7Z0RBQ0xDLE9BQU87Z0RBQ1BDLGFBQWE7NENBQ2Y7OzBEQUVBLDhEQUFDWDtnREFBSUYsV0FBVTswREFDYiw0RUFBQ3BCLGtEQUFLQTtvREFDSjhCLEtBQUk7b0RBQ0pDLEtBQUk7b0RBQ0pHLE9BQU87b0RBQ1BDLFFBQVE7b0RBQ1JmLFdBQVU7Ozs7Ozs7Ozs7OzBEQUdkLDhEQUFDZ0I7Z0RBQUdoQixXQUFVOzBEQUFtRDs7Ozs7OzBEQUNqRSw4REFBQ087Z0RBQUVQLFdBQVU7MERBQXdCOzs7Ozs7Ozs7Ozs7a0RBR3ZDLDhEQUFDRTt3Q0FDQ0YsV0FBVTt3Q0FDVlMsU0FBUyxJQUFNdEIsVUFBVTtnREFDdkJ1QixLQUFLO2dEQUNMQyxLQUFLO2dEQUNMQyxPQUFPO2dEQUNQQyxhQUFhOzRDQUNmOzswREFFQSw4REFBQ1g7Z0RBQUlGLFdBQVU7MERBQ2IsNEVBQUNwQixrREFBS0E7b0RBQ0o4QixLQUFJO29EQUNKQyxLQUFJO29EQUNKRyxPQUFPO29EQUNQQyxRQUFRO29EQUNSZixXQUFVOzs7Ozs7Ozs7OzswREFHZCw4REFBQ2dCO2dEQUFHaEIsV0FBVTswREFBbUQ7Ozs7OzswREFDakUsOERBQUNPO2dEQUFFUCxXQUFVOzBEQUF3Qjs7Ozs7Ozs7Ozs7O2tEQUd2Qyw4REFBQ0U7d0NBQ0NGLFdBQVU7d0NBQ1ZTLFNBQVMsSUFBTXRCLFVBQVU7Z0RBQ3ZCdUIsS0FBSztnREFDTEMsS0FBSztnREFDTEMsT0FBTztnREFDUEMsYUFBYTs0Q0FDZjs7MERBRUEsOERBQUNYO2dEQUFJRixXQUFVOzBEQUNiLDRFQUFDcEIsa0RBQUtBO29EQUNKOEIsS0FBSTtvREFDSkMsS0FBSTtvREFDSkcsT0FBTztvREFDUEMsUUFBUTtvREFDUmYsV0FBVTs7Ozs7Ozs7Ozs7MERBR2QsOERBQUNnQjtnREFBR2hCLFdBQVU7MERBQW1EOzs7Ozs7MERBQ2pFLDhEQUFDTztnREFBRVAsV0FBVTswREFBd0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUS9DLDhEQUFDSTtnQkFBUUosV0FBVTswQkFDakIsNEVBQUNFO29CQUFJRixXQUFVOzhCQUNiLDRFQUFDRTt3QkFBSUYsV0FBVTs7MENBQ2IsOERBQUNRO2dDQUFHUixXQUFVOztvQ0FBd0U7a0RBQzdFLDhEQUFDTTt3Q0FBS04sV0FBVTtrREFBZ0I7Ozs7Ozs7Ozs7OzswQ0FFekMsOERBQUNPO2dDQUFFUCxXQUFVOzBDQUFvRTs7Ozs7OzBDQU1qRiw4REFBQ0U7Z0NBQUlGLFdBQVU7O2tEQUViLDhEQUFDRTt3Q0FDQ0YsV0FBVTt3Q0FDVlMsU0FBUyxJQUFNdEIsVUFBVTtnREFDdkJ1QixLQUFLO2dEQUNMQyxLQUFLO2dEQUNMQyxPQUFPO2dEQUNQQyxhQUFhOzRDQUNmOzswREFFQSw4REFBQ1g7Z0RBQUlGLFdBQVU7MERBQ2IsNEVBQUNwQixrREFBS0E7b0RBQ0o4QixLQUFJO29EQUNKQyxLQUFJO29EQUNKRyxPQUFPO29EQUNQQyxRQUFRO29EQUNSZixXQUFVOzs7Ozs7Ozs7OzswREFHZCw4REFBQ2dCO2dEQUFHaEIsV0FBVTswREFBbUQ7Ozs7OzswREFDakUsOERBQUNPO2dEQUFFUCxXQUFVOzBEQUF3Qjs7Ozs7Ozs7Ozs7O2tEQUd2Qyw4REFBQ0U7d0NBQ0NGLFdBQVU7d0NBQ1ZTLFNBQVMsSUFBTXRCLFVBQVU7Z0RBQ3ZCdUIsS0FBSztnREFDTEMsS0FBSztnREFDTEMsT0FBTztnREFDUEMsYUFBYTs0Q0FDZjs7MERBRUEsOERBQUNYO2dEQUFJRixXQUFVOzBEQUNiLDRFQUFDcEIsa0RBQUtBO29EQUNKOEIsS0FBSTtvREFDSkMsS0FBSTtvREFDSkcsT0FBTztvREFDUEMsUUFBUTtvREFDUmYsV0FBVTs7Ozs7Ozs7Ozs7MERBR2QsOERBQUNnQjtnREFBR2hCLFdBQVU7MERBQW1EOzs7Ozs7MERBQ2pFLDhEQUFDTztnREFBRVAsV0FBVTswREFBd0I7Ozs7Ozs7Ozs7OztrREFHdkMsOERBQUNFO3dDQUNDRixXQUFVO3dDQUNWUyxTQUFTLElBQU10QixVQUFVO2dEQUN2QnVCLEtBQUs7Z0RBQ0xDLEtBQUs7Z0RBQ0xDLE9BQU87Z0RBQ1BDLGFBQWE7NENBQ2Y7OzBEQUVBLDhEQUFDWDtnREFBSUYsV0FBVTswREFDYiw0RUFBQ3BCLGtEQUFLQTtvREFDSjhCLEtBQUk7b0RBQ0pDLEtBQUk7b0RBQ0pHLE9BQU87b0RBQ1BDLFFBQVE7b0RBQ1JmLFdBQVU7Ozs7Ozs7Ozs7OzBEQUdkLDhEQUFDZ0I7Z0RBQUdoQixXQUFVOzBEQUFtRDs7Ozs7OzBEQUNqRSw4REFBQ087Z0RBQUVQLFdBQVU7MERBQXdCOzs7Ozs7Ozs7Ozs7a0RBR3ZDLDhEQUFDRTt3Q0FDQ0YsV0FBVTt3Q0FDVlMsU0FBUyxJQUFNdEIsVUFBVTtnREFDdkJ1QixLQUFLO2dEQUNMQyxLQUFLO2dEQUNMQyxPQUFPO2dEQUNQQyxhQUFhOzRDQUNmOzswREFFQSw4REFBQ1g7Z0RBQUlGLFdBQVU7MERBQ2IsNEVBQUNwQixrREFBS0E7b0RBQ0o4QixLQUFJO29EQUNKQyxLQUFJO29EQUNKRyxPQUFPO29EQUNQQyxRQUFRO29EQUNSZixXQUFVOzs7Ozs7Ozs7OzswREFHZCw4REFBQ2dCO2dEQUFHaEIsV0FBVTswREFBbUQ7Ozs7OzswREFDakUsOERBQUNPO2dEQUFFUCxXQUFVOzBEQUF3Qjs7Ozs7Ozs7Ozs7O2tEQUd2Qyw4REFBQ0U7d0NBQ0NGLFdBQVU7d0NBQ1ZTLFNBQVMsSUFBTXRCLFVBQVU7Z0RBQ3ZCdUIsS0FBSztnREFDTEMsS0FBSztnREFDTEMsT0FBTztnREFDUEMsYUFBYTs0Q0FDZjs7MERBRUEsOERBQUNYO2dEQUFJRixXQUFVOzBEQUNiLDRFQUFDcEIsa0RBQUtBO29EQUNKOEIsS0FBSTtvREFDSkMsS0FBSTtvREFDSkcsT0FBTztvREFDUEMsUUFBUTtvREFDUmYsV0FBVTs7Ozs7Ozs7Ozs7MERBR2QsOERBQUNnQjtnREFBR2hCLFdBQVU7MERBQW1EOzs7Ozs7MERBQ2pFLDhEQUFDTztnREFBRVAsV0FBVTswREFBd0I7Ozs7Ozs7Ozs7OztrREFHdkMsOERBQUNFO3dDQUNDRixXQUFVO3dDQUNWUyxTQUFTLElBQU10QixVQUFVO2dEQUN2QnVCLEtBQUs7Z0RBQ0xDLEtBQUs7Z0RBQ0xDLE9BQU87Z0RBQ1BDLGFBQWE7NENBQ2Y7OzBEQUVBLDhEQUFDWDtnREFBSUYsV0FBVTswREFDYiw0RUFBQ3BCLGtEQUFLQTtvREFDSjhCLEtBQUk7b0RBQ0pDLEtBQUk7b0RBQ0pHLE9BQU87b0RBQ1BDLFFBQVE7b0RBQ1JmLFdBQVU7Ozs7Ozs7Ozs7OzBEQUdkLDhEQUFDZ0I7Z0RBQUdoQixXQUFVOzBEQUFtRDs7Ozs7OzBEQUNqRSw4REFBQ087Z0RBQUVQLFdBQVU7MERBQXdCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVEvQyw4REFBQ0k7Z0JBQVFKLFdBQVU7MEJBQ2pCLDRFQUFDRTtvQkFBSUYsV0FBVTs4QkFDYiw0RUFBQ0U7d0JBQUlGLFdBQVU7OzBDQUNiLDhEQUFDUTtnQ0FBR1IsV0FBVTs7b0NBQXdFO2tEQUM3RSw4REFBQ007d0NBQUtOLFdBQVU7a0RBQWdCOzs7Ozs7Ozs7Ozs7MENBRXpDLDhEQUFDTztnQ0FBRVAsV0FBVTswQ0FBb0U7Ozs7OzswQ0FLakYsOERBQUNFO2dDQUFJRixXQUFVOztrREFFYiw4REFBQ0U7d0NBQ0NGLFdBQVU7d0NBQ1ZTLFNBQVMsSUFBTXRCLFVBQVU7Z0RBQ3ZCdUIsS0FBSztnREFDTEMsS0FBSztnREFDTEMsT0FBTztnREFDUEMsYUFBYTs0Q0FDZjs7MERBRUEsOERBQUNYO2dEQUFJRixXQUFVOzBEQUNiLDRFQUFDcEIsa0RBQUtBO29EQUNKOEIsS0FBSTtvREFDSkMsS0FBSTtvREFDSkcsT0FBTztvREFDUEMsUUFBUTtvREFDUmYsV0FBVTs7Ozs7Ozs7Ozs7MERBR2QsOERBQUNnQjtnREFBR2hCLFdBQVU7MERBQW1EOzs7Ozs7MERBQ2pFLDhEQUFDTztnREFBRVAsV0FBVTswREFBd0I7Ozs7Ozs7Ozs7OztrREFHdkMsOERBQUNFO3dDQUNDRixXQUFVO3dDQUNWUyxTQUFTLElBQU10QixVQUFVO2dEQUN2QnVCLEtBQUs7Z0RBQ0xDLEtBQUs7Z0RBQ0xDLE9BQU87Z0RBQ1BDLGFBQWE7NENBQ2Y7OzBEQUVBLDhEQUFDWDtnREFBSUYsV0FBVTswREFDYiw0RUFBQ3BCLGtEQUFLQTtvREFDSjhCLEtBQUk7b0RBQ0pDLEtBQUk7b0RBQ0pHLE9BQU87b0RBQ1BDLFFBQVE7b0RBQ1JmLFdBQVU7Ozs7Ozs7Ozs7OzBEQUdkLDhEQUFDZ0I7Z0RBQUdoQixXQUFVOzBEQUFtRDs7Ozs7OzBEQUNqRSw4REFBQ087Z0RBQUVQLFdBQVU7MERBQXdCOzs7Ozs7Ozs7Ozs7a0RBR3ZDLDhEQUFDRTt3Q0FDQ0YsV0FBVTt3Q0FDVlMsU0FBUyxJQUFNdEIsVUFBVTtnREFDdkJ1QixLQUFLO2dEQUNMQyxLQUFLO2dEQUNMQyxPQUFPO2dEQUNQQyxhQUFhOzRDQUNmOzswREFFQSw4REFBQ1g7Z0RBQUlGLFdBQVU7MERBQ2IsNEVBQUNwQixrREFBS0E7b0RBQ0o4QixLQUFJO29EQUNKQyxLQUFJO29EQUNKRyxPQUFPO29EQUNQQyxRQUFRO29EQUNSZixXQUFVOzs7Ozs7Ozs7OzswREFHZCw4REFBQ2dCO2dEQUFHaEIsV0FBVTswREFBbUQ7Ozs7OzswREFDakUsOERBQUNPO2dEQUFFUCxXQUFVOzBEQUF3Qjs7Ozs7Ozs7Ozs7O2tEQUd2Qyw4REFBQ0U7d0NBQ0NGLFdBQVU7d0NBQ1ZTLFNBQVMsSUFBTXRCLFVBQVU7Z0RBQ3ZCdUIsS0FBSztnREFDTEMsS0FBSztnREFDTEMsT0FBTztnREFDUEMsYUFBYTs0Q0FDZjs7MERBRUEsOERBQUNYO2dEQUFJRixXQUFVOzBEQUNiLDRFQUFDcEIsa0RBQUtBO29EQUNKOEIsS0FBSTtvREFDSkMsS0FBSTtvREFDSkcsT0FBTztvREFDUEMsUUFBUTtvREFDUmYsV0FBVTs7Ozs7Ozs7Ozs7MERBR2QsOERBQUNnQjtnREFBR2hCLFdBQVU7MERBQW1EOzs7Ozs7MERBQ2pFLDhEQUFDTztnREFBRVAsV0FBVTswREFBd0I7Ozs7Ozs7Ozs7OztrREFHdkMsOERBQUNFO3dDQUNDRixXQUFVO3dDQUNWUyxTQUFTLElBQU10QixVQUFVO2dEQUN2QnVCLEtBQUs7Z0RBQ0xDLEtBQUs7Z0RBQ0xDLE9BQU87Z0RBQ1BDLGFBQWE7NENBQ2Y7OzBEQUVBLDhEQUFDWDtnREFBSUYsV0FBVTswREFDYiw0RUFBQ3BCLGtEQUFLQTtvREFDSjhCLEtBQUk7b0RBQ0pDLEtBQUk7b0RBQ0pHLE9BQU87b0RBQ1BDLFFBQVE7b0RBQ1JmLFdBQVU7Ozs7Ozs7Ozs7OzBEQUdkLDhEQUFDZ0I7Z0RBQUdoQixXQUFVOzBEQUFtRDs7Ozs7OzBEQUNqRSw4REFBQ087Z0RBQUVQLFdBQVU7MERBQXdCOzs7Ozs7Ozs7Ozs7a0RBR3ZDLDhEQUFDRTt3Q0FDQ0YsV0FBVTt3Q0FDVlMsU0FBUyxJQUFNdEIsVUFBVTtnREFDdkJ1QixLQUFLO2dEQUNMQyxLQUFLO2dEQUNMQyxPQUFPO2dEQUNQQyxhQUFhOzRDQUNmOzswREFFQSw4REFBQ1g7Z0RBQUlGLFdBQVU7MERBQ2IsNEVBQUNwQixrREFBS0E7b0RBQ0o4QixLQUFJO29EQUNKQyxLQUFJO29EQUNKRyxPQUFPO29EQUNQQyxRQUFRO29EQUNSZixXQUFVOzs7Ozs7Ozs7OzswREFHZCw4REFBQ2dCO2dEQUFHaEIsV0FBVTswREFBbUQ7Ozs7OzswREFDakUsOERBQUNPO2dEQUFFUCxXQUFVOzBEQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFRL0MsOERBQUNJO2dCQUFRSixXQUFVOzBCQUNqQiw0RUFBQ0U7b0JBQUlGLFdBQVU7O3NDQUNiLDhEQUFDUTs0QkFBR1IsV0FBVTs7Z0NBQWlEOzhDQUN6Qyw4REFBQ007b0NBQUtOLFdBQVU7OENBQWdCOzs7Ozs7Ozs7Ozs7c0NBRXRELDhEQUFDTzs0QkFBRVAsV0FBVTtzQ0FBdUQ7Ozs7OztzQ0FHcEUsOERBQUNFOzRCQUFJRixXQUFVOzs4Q0FDYiw4REFBQ25CLGlEQUFJQTtvQ0FBQ3NCLE1BQUs7b0NBQVlILFdBQVU7OENBQThHOzs7Ozs7OENBRy9JLDhEQUFDbkIsaURBQUlBO29DQUFDc0IsTUFBSztvQ0FBSUgsV0FBVTs4Q0FBaUo7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVFoTCw4REFBQ2lCO2dCQUFPakIsV0FBVTswQkFDaEIsNEVBQUNFO29CQUFJRixXQUFVOztzQ0FDYiw4REFBQ0U7NEJBQUlGLFdBQVU7OzhDQUNiLDhEQUFDRTtvQ0FBSUYsV0FBVTs7c0RBQ2IsOERBQUNnQjs0Q0FBR2hCLFdBQVU7c0RBQXdDOzs7Ozs7c0RBQ3RELDhEQUFDTzs0Q0FBRVAsV0FBVTtzREFBNkI7Ozs7OztzREFJMUMsOERBQUNFOzRDQUFJRixXQUFVOzs4REFDYiw4REFBQ2tCO29EQUFFZixNQUFLO29EQUFJSCxXQUFVOzhEQUFvQzs7Ozs7OzhEQUMxRCw4REFBQ2tCO29EQUFFZixNQUFLO29EQUFJSCxXQUFVOzhEQUFvQzs7Ozs7OzhEQUMxRCw4REFBQ2tCO29EQUFFZixNQUFLO29EQUFJSCxXQUFVOzhEQUFvQzs7Ozs7OzhEQUMxRCw4REFBQ2tCO29EQUFFZixNQUFLO29EQUFJSCxXQUFVOzhEQUFvQzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUc5RCw4REFBQ0U7O3NEQUNDLDhEQUFDaUI7NENBQUduQixXQUFVO3NEQUEyQzs7Ozs7O3NEQUN6RCw4REFBQ29COzRDQUFHcEIsV0FBVTs7OERBQ1osOERBQUNxQjs4REFBRyw0RUFBQ3hDLGlEQUFJQTt3REFBQ3NCLE1BQUs7d0RBQUlILFdBQVU7a0VBQXNCOzs7Ozs7Ozs7Ozs4REFDbkQsOERBQUNxQjs4REFBRyw0RUFBQ3hDLGlEQUFJQTt3REFBQ3NCLE1BQUs7d0RBQVVILFdBQVU7a0VBQXNCOzs7Ozs7Ozs7Ozs4REFDekQsOERBQUNxQjs4REFBRyw0RUFBQ3hDLGlEQUFJQTt3REFBQ3NCLE1BQUs7d0RBQWFILFdBQVU7a0VBQXNCOzs7Ozs7Ozs7Ozs4REFDNUQsOERBQUNxQjs4REFBRyw0RUFBQ3hDLGlEQUFJQTt3REFBQ3NCLE1BQUs7d0RBQVdILFdBQVU7a0VBQXNCOzs7Ozs7Ozs7Ozs4REFDMUQsOERBQUNxQjs4REFBRyw0RUFBQ3hDLGlEQUFJQTt3REFBQ3NCLE1BQUs7d0RBQVlILFdBQVU7a0VBQXNCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FHL0QsOERBQUNFOztzREFDQyw4REFBQ2lCOzRDQUFHbkIsV0FBVTtzREFBMkM7Ozs7OztzREFDekQsOERBQUNvQjs0Q0FBR3BCLFdBQVU7OzhEQUNaLDhEQUFDcUI7OERBQUc7Ozs7Ozs4REFDSiw4REFBQ0E7OERBQUc7Ozs7Ozs4REFDSiw4REFBQ0E7OERBQUc7Ozs7Ozs4REFDSiw4REFBQ0E7OERBQUc7Ozs7Ozs4REFDSiw4REFBQ0E7OERBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FJViw4REFBQ25COzRCQUFJRixXQUFVO3NDQUNiLDRFQUFDTzswQ0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztZQU1SdEIsK0JBQ0MsOERBQUNpQjtnQkFDQ0YsV0FBVTtnQkFDVlMsU0FBU3BCOzBCQUVULDRFQUFDYTtvQkFDQ0YsV0FBVTtvQkFDVlMsU0FBUyxDQUFDYSxJQUFNQSxFQUFFQyxlQUFlOztzQ0FHakMsOERBQUNDOzRCQUNDZixTQUFTcEI7NEJBQ1RXLFdBQVU7c0NBQ1g7Ozs7OztzQ0FLRCw4REFBQ0U7NEJBQUlGLFdBQVU7c0NBQ2IsNEVBQUNwQixrREFBS0E7Z0NBQ0o4QixLQUFLekIsY0FBY3lCLEdBQUc7Z0NBQ3RCQyxLQUFLMUIsY0FBYzBCLEdBQUc7Z0NBQ3RCRyxPQUFPO2dDQUNQQyxRQUFRO2dDQUNSZixXQUFVOzs7Ozs7Ozs7OztzQ0FLZCw4REFBQ0U7NEJBQUlGLFdBQVU7OzhDQUNiLDhEQUFDZ0I7b0NBQUdoQixXQUFVOzhDQUFpRGYsY0FBYzJCLEtBQUs7Ozs7Ozs4Q0FDbEYsOERBQUNMO29DQUFFUCxXQUFVOzhDQUF5QmYsY0FBYzRCLFdBQVc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTzdFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbW9kZXJuLWhhdmVuLy4vc3JjL2FwcC9nYWxsZXJ5L3BhZ2UudHN4PzJkMTUiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5pbXBvcnQgSW1hZ2UgZnJvbSBcIm5leHQvaW1hZ2VcIjtcbmltcG9ydCBMaW5rIGZyb20gXCJuZXh0L2xpbmtcIjtcbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tIFwicmVhY3RcIjtcblxuaW50ZXJmYWNlIE1vZGFsSW1hZ2Uge1xuICBzcmM6IHN0cmluZztcbiAgYWx0OiBzdHJpbmc7XG4gIHRpdGxlOiBzdHJpbmc7XG4gIGRlc2NyaXB0aW9uOiBzdHJpbmc7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEdhbGxlcnkoKSB7XG4gIGNvbnN0IFtzZWxlY3RlZEltYWdlLCBzZXRTZWxlY3RlZEltYWdlXSA9IHVzZVN0YXRlPE1vZGFsSW1hZ2UgfCBudWxsPihudWxsKTtcblxuICBjb25zdCBvcGVuTW9kYWwgPSAoaW1hZ2U6IE1vZGFsSW1hZ2UpID0+IHtcbiAgICBzZXRTZWxlY3RlZEltYWdlKGltYWdlKTtcbiAgfTtcblxuICBjb25zdCBjbG9zZU1vZGFsID0gKCkgPT4ge1xuICAgIHNldFNlbGVjdGVkSW1hZ2UobnVsbCk7XG4gIH07XG5cbiAgLy8gSGFuZGxlIGtleWJvYXJkIGV2ZW50c1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGhhbmRsZUtleURvd24gPSAoZXZlbnQ6IEtleWJvYXJkRXZlbnQpID0+IHtcbiAgICAgIGlmIChldmVudC5rZXkgPT09ICdFc2NhcGUnICYmIHNlbGVjdGVkSW1hZ2UpIHtcbiAgICAgICAgY2xvc2VNb2RhbCgpO1xuICAgICAgfVxuICAgIH07XG5cbiAgICBpZiAoc2VsZWN0ZWRJbWFnZSkge1xuICAgICAgZG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcigna2V5ZG93bicsIGhhbmRsZUtleURvd24pO1xuICAgICAgZG9jdW1lbnQuYm9keS5zdHlsZS5vdmVyZmxvdyA9ICdoaWRkZW4nOyAvLyBQcmV2ZW50IGJhY2tncm91bmQgc2Nyb2xsaW5nXG4gICAgfSBlbHNlIHtcbiAgICAgIGRvY3VtZW50LmJvZHkuc3R5bGUub3ZlcmZsb3cgPSAndW5zZXQnO1xuICAgIH1cblxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBkb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKCdrZXlkb3duJywgaGFuZGxlS2V5RG93bik7XG4gICAgICBkb2N1bWVudC5ib2R5LnN0eWxlLm92ZXJmbG93ID0gJ3Vuc2V0JztcbiAgICB9O1xuICB9LCBbc2VsZWN0ZWRJbWFnZV0pO1xuICByZXR1cm4gKFxuICAgIDxtYWluIGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy13aGl0ZVwiPlxuICAgICAgey8qIE5hdmlnYXRpb24gKi99XG4gICAgICA8bmF2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHNoYWRvdy1zbSBib3JkZXItYiBib3JkZXItZm9yZXN0LWdyZWVuLTEwMFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktYmV0d2VlbiBpdGVtcy1jZW50ZXIgaC0xNlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICA8TGluayBocmVmPVwiL1wiIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWZvcmVzdC1ncmVlbi02MDBcIj5Nb2Rlcm4gSGF2ZW48L0xpbms+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaGlkZGVuIG1kOmJsb2NrXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWwtMTAgZmxleCBpdGVtcy1iYXNlbGluZSBzcGFjZS14LTRcIj5cbiAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL1wiIGNsYXNzTmFtZT1cInRleHQtZm9yZXN0LWdyZWVuLTYwMCBob3Zlcjp0ZXh0LWdvbGQtNTAwIHB4LTMgcHktMiByb3VuZGVkLW1kIHRleHQtc20gZm9udC1tZWRpdW1cIj5Ib21lPC9MaW5rPlxuICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIjYWJvdXRcIiBjbGFzc05hbWU9XCJ0ZXh0LWZvcmVzdC1ncmVlbi02MDAgaG92ZXI6dGV4dC1nb2xkLTUwMCBweC0zIHB5LTIgcm91bmRlZC1tZCB0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+QWJvdXQ8L0xpbms+XG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIiNzZXJ2aWNlc1wiIGNsYXNzTmFtZT1cInRleHQtZm9yZXN0LWdyZWVuLTYwMCBob3Zlcjp0ZXh0LWdvbGQtNTAwIHB4LTMgcHktMiByb3VuZGVkLW1kIHRleHQtc20gZm9udC1tZWRpdW1cIj5TZXJ2aWNlczwvTGluaz5cbiAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiL2dhbGxlcnlcIiBjbGFzc05hbWU9XCJiZy1nb2xkLTUwMCB0ZXh0LXdoaXRlIHB4LTMgcHktMiByb3VuZGVkLW1kIHRleHQtc20gZm9udC1tZWRpdW1cIj5HYWxsZXJ5PC9MaW5rPlxuICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvYWktZGVzaWduXCIgY2xhc3NOYW1lPVwidGV4dC1mb3Jlc3QtZ3JlZW4tNjAwIGhvdmVyOnRleHQtZ29sZC01MDAgcHgtMyBweS0yIHJvdW5kZWQtbWQgdGV4dC1zbSBmb250LW1lZGl1bVwiPkFJIERlc2lnbjwvTGluaz5cbiAgICAgICAgICAgICAgICA8TGluayBocmVmPVwiI2NvbnRhY3RcIiBjbGFzc05hbWU9XCJ0ZXh0LWZvcmVzdC1ncmVlbi02MDAgaG92ZXI6dGV4dC1nb2xkLTUwMCBweC0zIHB5LTIgcm91bmRlZC1tZCB0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+Q29udGFjdDwvTGluaz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L25hdj5cblxuICAgICAgey8qIEhlcm8gU2VjdGlvbiAqL31cbiAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cInJlbGF0aXZlIGJnLWdyYWRpZW50LXRvLWJyIGZyb20tZm9yZXN0LWdyZWVuLTUwIHRvLXdoaXRlIHB5LTE2XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LThcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC00eGwgbWQ6dGV4dC01eGwgZm9udC1ib2xkIHRleHQtZm9yZXN0LWdyZWVuLTcwMCBtYi02XCI+XG4gICAgICAgICAgICAgIE91ciA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdvbGQtNTAwXCI+R2FsbGVyeTwvc3Bhbj5cbiAgICAgICAgICAgIDwvaDE+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhsIHRleHQtZm9yZXN0LWdyZWVuLTYwMCBtYi04IG1heC13LTN4bCBteC1hdXRvXCI+XG4gICAgICAgICAgICAgIEV4cGxvcmUgb3VyIGRpdmVyc2UgY29sbGVjdGlvbnMgb2YgaW50ZXJpb3IgZGVzaWduIHByb2plY3RzLCBmcm9tIG1vZGVybiBraXRjaGVucyB0byBvcmdhbml6ZWQgc3RvcmFnZSBzb2x1dGlvbnNcbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L3NlY3Rpb24+XG5cbiAgICAgIHsvKiBLaXRjaGVuIFJlY29uc3RydWN0aW9uIENvbGxlY3Rpb24gKi99XG4gICAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJweS0yMCBiZy13aGl0ZVwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi0xNlwiPlxuICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtM3hsIG1kOnRleHQtNHhsIGZvbnQtYm9sZCB0ZXh0LWZvcmVzdC1ncmVlbi03MDAgbWItNiB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICBLaXRjaGVuIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtZ29sZC01MDBcIj5SZWNvbnN0cnVjdGlvbjwvc3Bhbj5cbiAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWxnIHRleHQtZm9yZXN0LWdyZWVuLTYwMCB0ZXh0LWNlbnRlciBtYXgtdy00eGwgbXgtYXV0byBtYi0xMlwiPlxuICAgICAgICAgICAgICBXaXRuZXNzIHRoZSBjb21wbGV0ZSB0cmFuc2Zvcm1hdGlvbiBvZiBhIGtpdGNoZW4gc3BhY2UgZnJvbSBzdGFydCB0byBmaW5pc2guIFRoaXMgY29sbGVjdGlvbiBzaG93Y2FzZXNcbiAgICAgICAgICAgICAgb3VyIGV4cGVydGlzZSBpbiBmdWxsLXNjYWxlIGtpdGNoZW4gcmVub3ZhdGlvbnMsIGZlYXR1cmluZyBtb2Rlcm4gZGVzaWduIHByaW5jaXBsZXMsIGZ1bmN0aW9uYWwgbGF5b3V0cyxcbiAgICAgICAgICAgICAgYW5kIHByZW1pdW0gZmluaXNoZXMgdGhhdCBjcmVhdGUgdGhlIGhlYXJ0IG9mIHRoZSBob21lLlxuICAgICAgICAgICAgPC9wPlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTMgZ2FwLThcIj5cbiAgICAgICAgICAgICAgey8qIEtpdGNoZW4gUmVjb25zdHJ1Y3Rpb24gSW1hZ2VzICovfVxuICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZ3JvdXAgY3Vyc29yLXBvaW50ZXJcIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IG9wZW5Nb2RhbCh7XG4gICAgICAgICAgICAgICAgICBzcmM6IFwiL2ltYWdlcy9raXRjaGVuLXJlY29uc3RydWN0aW9uL0lNR183OTA2LmpwZ1wiLFxuICAgICAgICAgICAgICAgICAgYWx0OiBcIktpdGNoZW4gUmVjb25zdHJ1Y3Rpb24gLSBJbml0aWFsIFBoYXNlXCIsXG4gICAgICAgICAgICAgICAgICB0aXRsZTogXCJJbml0aWFsIFBoYXNlXCIsXG4gICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogXCJUaGUgYmVnaW5uaW5nIG9mIG91ciBjb21wcmVoZW5zaXZlIGtpdGNoZW4gcmVub3ZhdGlvbiBwcm9qZWN0LiBUaGlzIHBoYXNlIGludm9sdmVzIHBsYW5uaW5nLCBtZWFzdXJlbWVudHMsIGFuZCBwcmVwYXJpbmcgdGhlIHNwYWNlIGZvciB0cmFuc2Zvcm1hdGlvbi5cIlxuICAgICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1mb3Jlc3QtZ3JlZW4tMTAwIHJvdW5kZWQtbGcgb3ZlcmZsb3ctaGlkZGVuIGgtODAgbWItNFwiPlxuICAgICAgICAgICAgICAgICAgPEltYWdlXG4gICAgICAgICAgICAgICAgICAgIHNyYz1cIi9pbWFnZXMva2l0Y2hlbi1yZWNvbnN0cnVjdGlvbi9JTUdfNzkwNi5qcGdcIlxuICAgICAgICAgICAgICAgICAgICBhbHQ9XCJLaXRjaGVuIFJlY29uc3RydWN0aW9uIC0gSW5pdGlhbCBQaGFzZVwiXG4gICAgICAgICAgICAgICAgICAgIHdpZHRoPXs0MDB9XG4gICAgICAgICAgICAgICAgICAgIGhlaWdodD17MzIwfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1mdWxsIG9iamVjdC1jb3ZlciBncm91cC1ob3ZlcjpzY2FsZS0xMDUgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tMzAwXCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWZvcmVzdC1ncmVlbi03MDAgbWItMlwiPkluaXRpYWwgUGhhc2U8L2gzPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZm9yZXN0LWdyZWVuLTYwMFwiPlRoZSBiZWdpbm5pbmcgb2Ygb3VyIGNvbXByZWhlbnNpdmUga2l0Y2hlbiByZW5vdmF0aW9uIHByb2plY3QuPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZ3JvdXAgY3Vyc29yLXBvaW50ZXJcIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IG9wZW5Nb2RhbCh7XG4gICAgICAgICAgICAgICAgICBzcmM6IFwiL2ltYWdlcy9raXRjaGVuLXJlY29uc3RydWN0aW9uL0lNR183OTA4LmpwZ1wiLFxuICAgICAgICAgICAgICAgICAgYWx0OiBcIktpdGNoZW4gUmVjb25zdHJ1Y3Rpb24gLSBEZW1vbGl0aW9uXCIsXG4gICAgICAgICAgICAgICAgICB0aXRsZTogXCJEZW1vbGl0aW9uIFBoYXNlXCIsXG4gICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogXCJDYXJlZnVsIHJlbW92YWwgb2YgZXhpc3Rpbmcgc3RydWN0dXJlcyB0byBwcmVwYXJlIGZvciB0aGUgbmV3IGRlc2lnbi4gVGhpcyBwaGFzZSByZXF1aXJlcyBwcmVjaXNpb24gdG8gcHJlc2VydmUgc3RydWN0dXJhbCBpbnRlZ3JpdHkgd2hpbGUgY2xlYXJpbmcgc3BhY2UgZm9yIGltcHJvdmVtZW50cy5cIlxuICAgICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1mb3Jlc3QtZ3JlZW4tMTAwIHJvdW5kZWQtbGcgb3ZlcmZsb3ctaGlkZGVuIGgtODAgbWItNFwiPlxuICAgICAgICAgICAgICAgICAgPEltYWdlXG4gICAgICAgICAgICAgICAgICAgIHNyYz1cIi9pbWFnZXMva2l0Y2hlbi1yZWNvbnN0cnVjdGlvbi9JTUdfNzkwOC5qcGdcIlxuICAgICAgICAgICAgICAgICAgICBhbHQ9XCJLaXRjaGVuIFJlY29uc3RydWN0aW9uIC0gRGVtb2xpdGlvblwiXG4gICAgICAgICAgICAgICAgICAgIHdpZHRoPXs0MDB9XG4gICAgICAgICAgICAgICAgICAgIGhlaWdodD17MzIwfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1mdWxsIG9iamVjdC1jb3ZlciBncm91cC1ob3ZlcjpzY2FsZS0xMDUgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tMzAwXCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWZvcmVzdC1ncmVlbi03MDAgbWItMlwiPkRlbW9saXRpb24gUGhhc2U8L2gzPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZm9yZXN0LWdyZWVuLTYwMFwiPkNhcmVmdWwgcmVtb3ZhbCBvZiBleGlzdGluZyBzdHJ1Y3R1cmVzIHRvIHByZXBhcmUgZm9yIHRoZSBuZXcgZGVzaWduLjwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImdyb3VwIGN1cnNvci1wb2ludGVyXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBvcGVuTW9kYWwoe1xuICAgICAgICAgICAgICAgICAgc3JjOiBcIi9pbWFnZXMva2l0Y2hlbi1yZWNvbnN0cnVjdGlvbi9JTUdfNzkxMC5qcGdcIixcbiAgICAgICAgICAgICAgICAgIGFsdDogXCJLaXRjaGVuIFJlY29uc3RydWN0aW9uIC0gU3RydWN0dXJhbCBXb3JrXCIsXG4gICAgICAgICAgICAgICAgICB0aXRsZTogXCJTdHJ1Y3R1cmFsIFdvcmtcIixcbiAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBcIkZvdW5kYXRpb24gYW5kIHN0cnVjdHVyYWwgaW1wcm92ZW1lbnRzIGZvciB0aGUgbmV3IGtpdGNoZW4gbGF5b3V0LiBUaGlzIHBoYXNlIGluY2x1ZGVzIGVsZWN0cmljYWwsIHBsdW1iaW5nLCBhbmQgZnJhbWV3b3JrIG1vZGlmaWNhdGlvbnMgdG8gc3VwcG9ydCB0aGUgbmV3IGRlc2lnbi5cIlxuICAgICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1mb3Jlc3QtZ3JlZW4tMTAwIHJvdW5kZWQtbGcgb3ZlcmZsb3ctaGlkZGVuIGgtODAgbWItNFwiPlxuICAgICAgICAgICAgICAgICAgPEltYWdlXG4gICAgICAgICAgICAgICAgICAgIHNyYz1cIi9pbWFnZXMva2l0Y2hlbi1yZWNvbnN0cnVjdGlvbi9JTUdfNzkxMC5qcGdcIlxuICAgICAgICAgICAgICAgICAgICBhbHQ9XCJLaXRjaGVuIFJlY29uc3RydWN0aW9uIC0gU3RydWN0dXJhbCBXb3JrXCJcbiAgICAgICAgICAgICAgICAgICAgd2lkdGg9ezQwMH1cbiAgICAgICAgICAgICAgICAgICAgaGVpZ2h0PXszMjB9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGwgb2JqZWN0LWNvdmVyIGdyb3VwLWhvdmVyOnNjYWxlLTEwNSB0cmFuc2l0aW9uLXRyYW5zZm9ybSBkdXJhdGlvbi0zMDBcIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZm9yZXN0LWdyZWVuLTcwMCBtYi0yXCI+U3RydWN0dXJhbCBXb3JrPC9oMz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWZvcmVzdC1ncmVlbi02MDBcIj5Gb3VuZGF0aW9uIGFuZCBzdHJ1Y3R1cmFsIGltcHJvdmVtZW50cyBmb3IgdGhlIG5ldyBraXRjaGVuIGxheW91dC48L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJncm91cCBjdXJzb3ItcG9pbnRlclwiXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gb3Blbk1vZGFsKHtcbiAgICAgICAgICAgICAgICAgIHNyYzogXCIvaW1hZ2VzL2tpdGNoZW4tcmVjb25zdHJ1Y3Rpb24vSU1HXzc5MTEuanBnXCIsXG4gICAgICAgICAgICAgICAgICBhbHQ6IFwiS2l0Y2hlbiBSZWNvbnN0cnVjdGlvbiAtIEluc3RhbGxhdGlvblwiLFxuICAgICAgICAgICAgICAgICAgdGl0bGU6IFwiSW5zdGFsbGF0aW9uIFBoYXNlXCIsXG4gICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogXCJJbnN0YWxsaW5nIGNhYmluZXRzLCBjb3VudGVydG9wcywgYW5kIGVzc2VudGlhbCBraXRjaGVuIGVsZW1lbnRzLiBUaGlzIHBoYXNlIGJyaW5ncyB0aGUgZGVzaWduIHRvIGxpZmUgd2l0aCBjdXN0b20gY2FiaW5ldHJ5IGFuZCBwcmVtaXVtIG1hdGVyaWFscy5cIlxuICAgICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1mb3Jlc3QtZ3JlZW4tMTAwIHJvdW5kZWQtbGcgb3ZlcmZsb3ctaGlkZGVuIGgtODAgbWItNFwiPlxuICAgICAgICAgICAgICAgICAgPEltYWdlXG4gICAgICAgICAgICAgICAgICAgIHNyYz1cIi9pbWFnZXMva2l0Y2hlbi1yZWNvbnN0cnVjdGlvbi9JTUdfNzkxMS5qcGdcIlxuICAgICAgICAgICAgICAgICAgICBhbHQ9XCJLaXRjaGVuIFJlY29uc3RydWN0aW9uIC0gSW5zdGFsbGF0aW9uXCJcbiAgICAgICAgICAgICAgICAgICAgd2lkdGg9ezQwMH1cbiAgICAgICAgICAgICAgICAgICAgaGVpZ2h0PXszMjB9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGwgb2JqZWN0LWNvdmVyIGdyb3VwLWhvdmVyOnNjYWxlLTEwNSB0cmFuc2l0aW9uLXRyYW5zZm9ybSBkdXJhdGlvbi0zMDBcIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZm9yZXN0LWdyZWVuLTcwMCBtYi0yXCI+SW5zdGFsbGF0aW9uIFBoYXNlPC9oMz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWZvcmVzdC1ncmVlbi02MDBcIj5JbnN0YWxsaW5nIGNhYmluZXRzLCBjb3VudGVydG9wcywgYW5kIGVzc2VudGlhbCBraXRjaGVuIGVsZW1lbnRzLjwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImdyb3VwIGN1cnNvci1wb2ludGVyXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBvcGVuTW9kYWwoe1xuICAgICAgICAgICAgICAgICAgc3JjOiBcIi9pbWFnZXMva2l0Y2hlbi1yZWNvbnN0cnVjdGlvbi9JTUdfNzkxMy5qcGdcIixcbiAgICAgICAgICAgICAgICAgIGFsdDogXCJLaXRjaGVuIFJlY29uc3RydWN0aW9uIC0gRmluaXNoaW5nIFRvdWNoZXNcIixcbiAgICAgICAgICAgICAgICAgIHRpdGxlOiBcIkZpbmlzaGluZyBUb3VjaGVzXCIsXG4gICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogXCJBZGRpbmcgdGhlIGZpbmFsIGRldGFpbHMgdGhhdCBicmluZyB0aGUgZGVzaWduIHZpc2lvbiB0byBsaWZlLiBUaGlzIGluY2x1ZGVzIGhhcmR3YXJlIGluc3RhbGxhdGlvbiwgbGlnaHRpbmcgZml4dHVyZXMsIGFuZCBkZWNvcmF0aXZlIGVsZW1lbnRzLlwiXG4gICAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWZvcmVzdC1ncmVlbi0xMDAgcm91bmRlZC1sZyBvdmVyZmxvdy1oaWRkZW4gaC04MCBtYi00XCI+XG4gICAgICAgICAgICAgICAgICA8SW1hZ2VcbiAgICAgICAgICAgICAgICAgICAgc3JjPVwiL2ltYWdlcy9raXRjaGVuLXJlY29uc3RydWN0aW9uL0lNR183OTEzLmpwZ1wiXG4gICAgICAgICAgICAgICAgICAgIGFsdD1cIktpdGNoZW4gUmVjb25zdHJ1Y3Rpb24gLSBGaW5pc2hpbmcgVG91Y2hlc1wiXG4gICAgICAgICAgICAgICAgICAgIHdpZHRoPXs0MDB9XG4gICAgICAgICAgICAgICAgICAgIGhlaWdodD17MzIwfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1mdWxsIG9iamVjdC1jb3ZlciBncm91cC1ob3ZlcjpzY2FsZS0xMDUgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tMzAwXCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWZvcmVzdC1ncmVlbi03MDAgbWItMlwiPkZpbmlzaGluZyBUb3VjaGVzPC9oMz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWZvcmVzdC1ncmVlbi02MDBcIj5BZGRpbmcgdGhlIGZpbmFsIGRldGFpbHMgdGhhdCBicmluZyB0aGUgZGVzaWduIHZpc2lvbiB0byBsaWZlLjwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImdyb3VwIGN1cnNvci1wb2ludGVyXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBvcGVuTW9kYWwoe1xuICAgICAgICAgICAgICAgICAgc3JjOiBcIi9pbWFnZXMva2l0Y2hlbi1yZWNvbnN0cnVjdGlvbi9JTUdfNzkxNS5qcGdcIixcbiAgICAgICAgICAgICAgICAgIGFsdDogXCJLaXRjaGVuIFJlY29uc3RydWN0aW9uIC0gQ29tcGxldGVkXCIsXG4gICAgICAgICAgICAgICAgICB0aXRsZTogXCJGaW5hbCBSZXN1bHRcIixcbiAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBcIlRoZSBzdHVubmluZyBjb21wbGV0ZWQga2l0Y2hlbiB0cmFuc2Zvcm1hdGlvbi4gQSBwZXJmZWN0IGJsZW5kIG9mIGZ1bmN0aW9uYWxpdHkgYW5kIHN0eWxlLCBzaG93Y2FzaW5nIG1vZGVybiBkZXNpZ24gcHJpbmNpcGxlcyBhbmQgcHJlbWl1bSBjcmFmdHNtYW5zaGlwLlwiXG4gICAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWZvcmVzdC1ncmVlbi0xMDAgcm91bmRlZC1sZyBvdmVyZmxvdy1oaWRkZW4gaC04MCBtYi00XCI+XG4gICAgICAgICAgICAgICAgICA8SW1hZ2VcbiAgICAgICAgICAgICAgICAgICAgc3JjPVwiL2ltYWdlcy9raXRjaGVuLXJlY29uc3RydWN0aW9uL0lNR183OTE1LmpwZ1wiXG4gICAgICAgICAgICAgICAgICAgIGFsdD1cIktpdGNoZW4gUmVjb25zdHJ1Y3Rpb24gLSBDb21wbGV0ZWRcIlxuICAgICAgICAgICAgICAgICAgICB3aWR0aD17NDAwfVxuICAgICAgICAgICAgICAgICAgICBoZWlnaHQ9ezMyMH1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBvYmplY3QtY292ZXIgZ3JvdXAtaG92ZXI6c2NhbGUtMTA1IHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTMwMFwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1mb3Jlc3QtZ3JlZW4tNzAwIG1iLTJcIj5GaW5hbCBSZXN1bHQ8L2gzPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZm9yZXN0LWdyZWVuLTYwMFwiPlRoZSBzdHVubmluZyBjb21wbGV0ZWQga2l0Y2hlbiB0cmFuc2Zvcm1hdGlvbi48L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9zZWN0aW9uPlxuXG4gICAgICB7LyogTW9kZXJuIEtpdGNoZW4gQ29sbGVjdGlvbiAqL31cbiAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cInB5LTIwIGJnLWZvcmVzdC1ncmVlbi01MFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi0xNlwiPlxuICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtM3hsIG1kOnRleHQtNHhsIGZvbnQtYm9sZCB0ZXh0LWZvcmVzdC1ncmVlbi03MDAgbWItNiB0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICBNb2Rlcm4gPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1nb2xkLTUwMFwiPktpdGNoZW4gRGVzaWduczwvc3Bhbj5cbiAgICAgICAgICAgIDwvaDI+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWxnIHRleHQtZm9yZXN0LWdyZWVuLTYwMCB0ZXh0LWNlbnRlciBtYXgtdy00eGwgbXgtYXV0byBtYi0xMlwiPlxuICAgICAgICAgICAgICBPdXIgY29sbGVjdGlvbiBvZiBjb250ZW1wb3Jhcnkga2l0Y2hlbiBkZXNpZ25zIGZlYXR1cmluZyBzbGVlayBsaW5lcywgaW5ub3ZhdGl2ZSBzdG9yYWdlIHNvbHV0aW9ucyxcbiAgICAgICAgICAgICAgYW5kIGN1dHRpbmctZWRnZSBhcHBsaWFuY2VzLiBFYWNoIGRlc2lnbiBiYWxhbmNlcyBmdW5jdGlvbmFsaXR5IHdpdGggYWVzdGhldGljIGFwcGVhbCB0byBjcmVhdGVcbiAgICAgICAgICAgICAgdGhlIHBlcmZlY3QgY3VsaW5hcnkgd29ya3NwYWNlLlxuICAgICAgICAgICAgPC9wPlxuXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTMgZ2FwLThcIj5cbiAgICAgICAgICAgICAgey8qIE1vZGVybiBLaXRjaGVuIEltYWdlcyAqL31cbiAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImdyb3VwIGN1cnNvci1wb2ludGVyXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBvcGVuTW9kYWwoe1xuICAgICAgICAgICAgICAgICAgc3JjOiBcIi9pbWFnZXMvS2l0Y2hlbi9JTUdfNjAwOS5QTkdcIixcbiAgICAgICAgICAgICAgICAgIGFsdDogXCJNb2Rlcm4gS2l0Y2hlbiBEZXNpZ24gMVwiLFxuICAgICAgICAgICAgICAgICAgdGl0bGU6IFwiTWluaW1hbGlzdCBFeGNlbGxlbmNlXCIsXG4gICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogXCJDbGVhbiBsaW5lcyBhbmQgbmV1dHJhbCB0b25lcyBjcmVhdGUgYSBzZXJlbmUgY29va2luZyBlbnZpcm9ubWVudC4gVGhpcyBkZXNpZ24gZW1waGFzaXplcyBzaW1wbGljaXR5IGFuZCBmdW5jdGlvbmFsaXR5IHdpdGggaGlkZGVuIHN0b3JhZ2Ugc29sdXRpb25zIGFuZCBzdHJlYW1saW5lZCBhcHBsaWFuY2VzLlwiXG4gICAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgb3ZlcmZsb3ctaGlkZGVuIGgtODAgbWItNCBzaGFkb3ctbGdcIj5cbiAgICAgICAgICAgICAgICAgIDxJbWFnZVxuICAgICAgICAgICAgICAgICAgICBzcmM9XCIvaW1hZ2VzL0tpdGNoZW4vSU1HXzYwMDkuUE5HXCJcbiAgICAgICAgICAgICAgICAgICAgYWx0PVwiTW9kZXJuIEtpdGNoZW4gRGVzaWduIDFcIlxuICAgICAgICAgICAgICAgICAgICB3aWR0aD17NDAwfVxuICAgICAgICAgICAgICAgICAgICBoZWlnaHQ9ezMyMH1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBvYmplY3QtY292ZXIgZ3JvdXAtaG92ZXI6c2NhbGUtMTA1IHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTMwMFwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1mb3Jlc3QtZ3JlZW4tNzAwIG1iLTJcIj5NaW5pbWFsaXN0IEV4Y2VsbGVuY2U8L2gzPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZm9yZXN0LWdyZWVuLTYwMFwiPkNsZWFuIGxpbmVzIGFuZCBuZXV0cmFsIHRvbmVzIGNyZWF0ZSBhIHNlcmVuZSBjb29raW5nIGVudmlyb25tZW50LjwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImdyb3VwIGN1cnNvci1wb2ludGVyXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBvcGVuTW9kYWwoe1xuICAgICAgICAgICAgICAgICAgc3JjOiBcIi9pbWFnZXMvS2l0Y2hlbi9JTUdfNjAxMC5QTkdcIixcbiAgICAgICAgICAgICAgICAgIGFsdDogXCJNb2Rlcm4gS2l0Y2hlbiBEZXNpZ24gMlwiLFxuICAgICAgICAgICAgICAgICAgdGl0bGU6IFwiQ29udGVtcG9yYXJ5IEVsZWdhbmNlXCIsXG4gICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogXCJTb3BoaXN0aWNhdGVkIGRlc2lnbiB3aXRoIHByZW1pdW0gbWF0ZXJpYWxzIGFuZCBmaW5pc2hlcy4gRmVhdHVyZXMgaGlnaC1lbmQgYXBwbGlhbmNlcywgY3VzdG9tIGNhYmluZXRyeSwgYW5kIGVsZWdhbnQgbGlnaHRpbmcgdGhhdCBjcmVhdGVzIGEgbHV4dXJpb3VzIGNvb2tpbmcgZXhwZXJpZW5jZS5cIlxuICAgICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSByb3VuZGVkLWxnIG92ZXJmbG93LWhpZGRlbiBoLTgwIG1iLTQgc2hhZG93LWxnXCI+XG4gICAgICAgICAgICAgICAgICA8SW1hZ2VcbiAgICAgICAgICAgICAgICAgICAgc3JjPVwiL2ltYWdlcy9LaXRjaGVuL0lNR182MDEwLlBOR1wiXG4gICAgICAgICAgICAgICAgICAgIGFsdD1cIk1vZGVybiBLaXRjaGVuIERlc2lnbiAyXCJcbiAgICAgICAgICAgICAgICAgICAgd2lkdGg9ezQwMH1cbiAgICAgICAgICAgICAgICAgICAgaGVpZ2h0PXszMjB9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGwgb2JqZWN0LWNvdmVyIGdyb3VwLWhvdmVyOnNjYWxlLTEwNSB0cmFuc2l0aW9uLXRyYW5zZm9ybSBkdXJhdGlvbi0zMDBcIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZm9yZXN0LWdyZWVuLTcwMCBtYi0yXCI+Q29udGVtcG9yYXJ5IEVsZWdhbmNlPC9oMz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWZvcmVzdC1ncmVlbi02MDBcIj5Tb3BoaXN0aWNhdGVkIGRlc2lnbiB3aXRoIHByZW1pdW0gbWF0ZXJpYWxzIGFuZCBmaW5pc2hlcy48L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJncm91cCBjdXJzb3ItcG9pbnRlclwiXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gb3Blbk1vZGFsKHtcbiAgICAgICAgICAgICAgICAgIHNyYzogXCIvaW1hZ2VzL0tpdGNoZW4vSU1HXzY3MTEuSlBHXCIsXG4gICAgICAgICAgICAgICAgICBhbHQ6IFwiTW9kZXJuIEtpdGNoZW4gRGVzaWduIDNcIixcbiAgICAgICAgICAgICAgICAgIHRpdGxlOiBcIkZ1bmN0aW9uYWwgQmVhdXR5XCIsXG4gICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogXCJQZXJmZWN0IGJsZW5kIG9mIHN0eWxlIGFuZCBwcmFjdGljYWxpdHkgZm9yIGV2ZXJ5ZGF5IGxpdmluZy4gU21hcnQgc3RvcmFnZSBzb2x1dGlvbnMgYW5kIGVyZ29ub21pYyBkZXNpZ24gbWFrZSBjb29raW5nIGFuZCBlbnRlcnRhaW5pbmcgZWZmb3J0bGVzcyBhbmQgZW5qb3lhYmxlLlwiXG4gICAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgb3ZlcmZsb3ctaGlkZGVuIGgtODAgbWItNCBzaGFkb3ctbGdcIj5cbiAgICAgICAgICAgICAgICAgIDxJbWFnZVxuICAgICAgICAgICAgICAgICAgICBzcmM9XCIvaW1hZ2VzL0tpdGNoZW4vSU1HXzY3MTEuSlBHXCJcbiAgICAgICAgICAgICAgICAgICAgYWx0PVwiTW9kZXJuIEtpdGNoZW4gRGVzaWduIDNcIlxuICAgICAgICAgICAgICAgICAgICB3aWR0aD17NDAwfVxuICAgICAgICAgICAgICAgICAgICBoZWlnaHQ9ezMyMH1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBvYmplY3QtY292ZXIgZ3JvdXAtaG92ZXI6c2NhbGUtMTA1IHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTMwMFwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1mb3Jlc3QtZ3JlZW4tNzAwIG1iLTJcIj5GdW5jdGlvbmFsIEJlYXV0eTwvaDM+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1mb3Jlc3QtZ3JlZW4tNjAwXCI+UGVyZmVjdCBibGVuZCBvZiBzdHlsZSBhbmQgcHJhY3RpY2FsaXR5IGZvciBldmVyeWRheSBsaXZpbmcuPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZ3JvdXAgY3Vyc29yLXBvaW50ZXJcIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IG9wZW5Nb2RhbCh7XG4gICAgICAgICAgICAgICAgICBzcmM6IFwiL2ltYWdlcy9LaXRjaGVuL0lNR182NzEyLkpQR1wiLFxuICAgICAgICAgICAgICAgICAgYWx0OiBcIk1vZGVybiBLaXRjaGVuIERlc2lnbiA0XCIsXG4gICAgICAgICAgICAgICAgICB0aXRsZTogXCJMdXh1cnkgTGl2aW5nXCIsXG4gICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogXCJIaWdoLWVuZCBhcHBsaWFuY2VzIGFuZCBjdXN0b20gY2FiaW5ldHJ5IGZvciB0aGUgZGlzY2VybmluZyBob21lb3duZXIuIFByZW1pdW0gbWF0ZXJpYWxzIGFuZCBzb3BoaXN0aWNhdGVkIGZpbmlzaGVzIGNyZWF0ZSBhbiB1cHNjYWxlIGN1bGluYXJ5IGVudmlyb25tZW50LlwiXG4gICAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgb3ZlcmZsb3ctaGlkZGVuIGgtODAgbWItNCBzaGFkb3ctbGdcIj5cbiAgICAgICAgICAgICAgICAgIDxJbWFnZVxuICAgICAgICAgICAgICAgICAgICBzcmM9XCIvaW1hZ2VzL0tpdGNoZW4vSU1HXzY3MTIuSlBHXCJcbiAgICAgICAgICAgICAgICAgICAgYWx0PVwiTW9kZXJuIEtpdGNoZW4gRGVzaWduIDRcIlxuICAgICAgICAgICAgICAgICAgICB3aWR0aD17NDAwfVxuICAgICAgICAgICAgICAgICAgICBoZWlnaHQ9ezMyMH1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBvYmplY3QtY292ZXIgZ3JvdXAtaG92ZXI6c2NhbGUtMTA1IHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTMwMFwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1mb3Jlc3QtZ3JlZW4tNzAwIG1iLTJcIj5MdXh1cnkgTGl2aW5nPC9oMz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWZvcmVzdC1ncmVlbi02MDBcIj5IaWdoLWVuZCBhcHBsaWFuY2VzIGFuZCBjdXN0b20gY2FiaW5ldHJ5IGZvciB0aGUgZGlzY2VybmluZyBob21lb3duZXIuPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZ3JvdXAgY3Vyc29yLXBvaW50ZXJcIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IG9wZW5Nb2RhbCh7XG4gICAgICAgICAgICAgICAgICBzcmM6IFwiL2ltYWdlcy9LaXRjaGVuL0lNR182NzEzLkpQR1wiLFxuICAgICAgICAgICAgICAgICAgYWx0OiBcIk1vZGVybiBLaXRjaGVuIERlc2lnbiA1XCIsXG4gICAgICAgICAgICAgICAgICB0aXRsZTogXCJTbWFydCBEZXNpZ25cIixcbiAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBcIklubm92YXRpdmUgc3RvcmFnZSBzb2x1dGlvbnMgbWF4aW1pemUgc3BhY2UgYW5kIGVmZmljaWVuY3kuIENsZXZlciBvcmdhbml6YXRpb24gc3lzdGVtcyBhbmQgbXVsdGktZnVuY3Rpb25hbCBlbGVtZW50cyBtYWtlIHRoZSBtb3N0IG9mIGV2ZXJ5IHNxdWFyZSBpbmNoLlwiXG4gICAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgb3ZlcmZsb3ctaGlkZGVuIGgtODAgbWItNCBzaGFkb3ctbGdcIj5cbiAgICAgICAgICAgICAgICAgIDxJbWFnZVxuICAgICAgICAgICAgICAgICAgICBzcmM9XCIvaW1hZ2VzL0tpdGNoZW4vSU1HXzY3MTMuSlBHXCJcbiAgICAgICAgICAgICAgICAgICAgYWx0PVwiTW9kZXJuIEtpdGNoZW4gRGVzaWduIDVcIlxuICAgICAgICAgICAgICAgICAgICB3aWR0aD17NDAwfVxuICAgICAgICAgICAgICAgICAgICBoZWlnaHQ9ezMyMH1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBvYmplY3QtY292ZXIgZ3JvdXAtaG92ZXI6c2NhbGUtMTA1IHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTMwMFwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1mb3Jlc3QtZ3JlZW4tNzAwIG1iLTJcIj5TbWFydCBEZXNpZ248L2gzPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZm9yZXN0LWdyZWVuLTYwMFwiPklubm92YXRpdmUgc3RvcmFnZSBzb2x1dGlvbnMgbWF4aW1pemUgc3BhY2UgYW5kIGVmZmljaWVuY3kuPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZ3JvdXAgY3Vyc29yLXBvaW50ZXJcIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IG9wZW5Nb2RhbCh7XG4gICAgICAgICAgICAgICAgICBzcmM6IFwiL2ltYWdlcy9LaXRjaGVuL0lNR182NzE0LkpQR1wiLFxuICAgICAgICAgICAgICAgICAgYWx0OiBcIk1vZGVybiBLaXRjaGVuIERlc2lnbiA2XCIsXG4gICAgICAgICAgICAgICAgICB0aXRsZTogXCJUaW1lbGVzcyBBcHBlYWxcIixcbiAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBcIkNsYXNzaWMgZGVzaWduIGVsZW1lbnRzIHdpdGggbW9kZXJuIGZ1bmN0aW9uYWxpdHkuIFRoaXMga2l0Y2hlbiBjb21iaW5lcyB0cmFkaXRpb25hbCBhZXN0aGV0aWNzIHdpdGggY29udGVtcG9yYXJ5IGNvbnZlbmllbmNlcyBmb3IgZW5kdXJpbmcgc3R5bGUgYW5kIHBlcmZvcm1hbmNlLlwiXG4gICAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgb3ZlcmZsb3ctaGlkZGVuIGgtODAgbWItNCBzaGFkb3ctbGdcIj5cbiAgICAgICAgICAgICAgICAgIDxJbWFnZVxuICAgICAgICAgICAgICAgICAgICBzcmM9XCIvaW1hZ2VzL0tpdGNoZW4vSU1HXzY3MTQuSlBHXCJcbiAgICAgICAgICAgICAgICAgICAgYWx0PVwiTW9kZXJuIEtpdGNoZW4gRGVzaWduIDZcIlxuICAgICAgICAgICAgICAgICAgICB3aWR0aD17NDAwfVxuICAgICAgICAgICAgICAgICAgICBoZWlnaHQ9ezMyMH1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBvYmplY3QtY292ZXIgZ3JvdXAtaG92ZXI6c2NhbGUtMTA1IHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTMwMFwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1mb3Jlc3QtZ3JlZW4tNzAwIG1iLTJcIj5UaW1lbGVzcyBBcHBlYWw8L2gzPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZm9yZXN0LWdyZWVuLTYwMFwiPkNsYXNzaWMgZGVzaWduIGVsZW1lbnRzIHdpdGggbW9kZXJuIGZ1bmN0aW9uYWxpdHkuPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvc2VjdGlvbj5cblxuICAgICAgey8qIENsb3NldCBPcmdhbml6YXRpb24gQ29sbGVjdGlvbiAqL31cbiAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cInB5LTIwIGJnLXdoaXRlXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LThcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTE2XCI+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0zeGwgbWQ6dGV4dC00eGwgZm9udC1ib2xkIHRleHQtZm9yZXN0LWdyZWVuLTcwMCBtYi02IHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgICAgIENsb3NldCA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdvbGQtNTAwXCI+T3JnYW5pemF0aW9uPC9zcGFuPlxuICAgICAgICAgICAgPC9oMj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbGcgdGV4dC1mb3Jlc3QtZ3JlZW4tNjAwIHRleHQtY2VudGVyIG1heC13LTR4bCBteC1hdXRvIG1iLTEyXCI+XG4gICAgICAgICAgICAgIFRyYW5zZm9ybSB5b3VyIHN0b3JhZ2Ugc3BhY2VzIHdpdGggb3VyIGN1c3RvbSBjbG9zZXQgb3JnYW5pemF0aW9uIHNvbHV0aW9ucy4gRnJvbSB3YWxrLWluIHdhcmRyb2Jlc1xuICAgICAgICAgICAgICB0byBjb21wYWN0IHN0b3JhZ2UgYXJlYXMsIHdlIGNyZWF0ZSBzeXN0ZW1zIHRoYXQgbWF4aW1pemUgc3BhY2Ugd2hpbGUgbWFpbnRhaW5pbmcgc3R5bGUgYW5kIGFjY2Vzc2liaWxpdHkuXG4gICAgICAgICAgICA8L3A+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtMyBnYXAtOFwiPlxuICAgICAgICAgICAgICB7LyogQ2xvc2V0IE9yZ2FuaXphdGlvbiBJbWFnZXMgKi99XG4gICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJncm91cCBjdXJzb3ItcG9pbnRlclwiXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gb3Blbk1vZGFsKHtcbiAgICAgICAgICAgICAgICAgIHNyYzogXCIvaW1hZ2VzL0Nsb3NldC9JTUdfNjAwMy5QTkdcIixcbiAgICAgICAgICAgICAgICAgIGFsdDogXCJDbG9zZXQgT3JnYW5pemF0aW9uIDFcIixcbiAgICAgICAgICAgICAgICAgIHRpdGxlOiBcIldhbGstaW4gV2FyZHJvYmVcIixcbiAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBcIkx1eHVyaW91cyB3YWxrLWluIGNsb3NldCB3aXRoIGN1c3RvbSBzaGVsdmluZyBhbmQgaGFuZ2luZyBzb2x1dGlvbnMuIEZlYXR1cmVzIGRlZGljYXRlZCBzcGFjZXMgZm9yIGRpZmZlcmVudCBjbG90aGluZyB0eXBlcywgc2hvZXMsIGFuZCBhY2Nlc3NvcmllcyB3aXRoIGVsZWdhbnQgbGlnaHRpbmcuXCJcbiAgICAgICAgICAgICAgICB9KX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZm9yZXN0LWdyZWVuLTEwMCByb3VuZGVkLWxnIG92ZXJmbG93LWhpZGRlbiBoLTgwIG1iLTRcIj5cbiAgICAgICAgICAgICAgICAgIDxJbWFnZVxuICAgICAgICAgICAgICAgICAgICBzcmM9XCIvaW1hZ2VzL0Nsb3NldC9JTUdfNjAwMy5QTkdcIlxuICAgICAgICAgICAgICAgICAgICBhbHQ9XCJDbG9zZXQgT3JnYW5pemF0aW9uIDFcIlxuICAgICAgICAgICAgICAgICAgICB3aWR0aD17NDAwfVxuICAgICAgICAgICAgICAgICAgICBoZWlnaHQ9ezMyMH1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBvYmplY3QtY292ZXIgZ3JvdXAtaG92ZXI6c2NhbGUtMTA1IHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTMwMFwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1mb3Jlc3QtZ3JlZW4tNzAwIG1iLTJcIj5XYWxrLWluIFdhcmRyb2JlPC9oMz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWZvcmVzdC1ncmVlbi02MDBcIj5MdXh1cmlvdXMgd2Fsay1pbiBjbG9zZXQgd2l0aCBjdXN0b20gc2hlbHZpbmcgYW5kIGhhbmdpbmcgc29sdXRpb25zLjwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImdyb3VwIGN1cnNvci1wb2ludGVyXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBvcGVuTW9kYWwoe1xuICAgICAgICAgICAgICAgICAgc3JjOiBcIi9pbWFnZXMvQ2xvc2V0L0lNR182MDA0LlBOR1wiLFxuICAgICAgICAgICAgICAgICAgYWx0OiBcIkNsb3NldCBPcmdhbml6YXRpb24gMlwiLFxuICAgICAgICAgICAgICAgICAgdGl0bGU6IFwiT3JnYW5pemVkIFN0b3JhZ2VcIixcbiAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBcIkVmZmljaWVudCBzdG9yYWdlIHN5c3RlbSB3aXRoIGRlc2lnbmF0ZWQgc3BhY2VzIGZvciBldmVyeSBpdGVtLiBTbWFydCBvcmdhbml6YXRpb24gc29sdXRpb25zIHRoYXQgbWFrZSBmaW5kaW5nIGFuZCBzdG9yaW5nIGJlbG9uZ2luZ3MgZWZmb3J0bGVzcyBhbmQgaW50dWl0aXZlLlwiXG4gICAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWZvcmVzdC1ncmVlbi0xMDAgcm91bmRlZC1sZyBvdmVyZmxvdy1oaWRkZW4gaC04MCBtYi00XCI+XG4gICAgICAgICAgICAgICAgICA8SW1hZ2VcbiAgICAgICAgICAgICAgICAgICAgc3JjPVwiL2ltYWdlcy9DbG9zZXQvSU1HXzYwMDQuUE5HXCJcbiAgICAgICAgICAgICAgICAgICAgYWx0PVwiQ2xvc2V0IE9yZ2FuaXphdGlvbiAyXCJcbiAgICAgICAgICAgICAgICAgICAgd2lkdGg9ezQwMH1cbiAgICAgICAgICAgICAgICAgICAgaGVpZ2h0PXszMjB9XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGwgb2JqZWN0LWNvdmVyIGdyb3VwLWhvdmVyOnNjYWxlLTEwNSB0cmFuc2l0aW9uLXRyYW5zZm9ybSBkdXJhdGlvbi0zMDBcIlxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZm9yZXN0LWdyZWVuLTcwMCBtYi0yXCI+T3JnYW5pemVkIFN0b3JhZ2U8L2gzPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZm9yZXN0LWdyZWVuLTYwMFwiPkVmZmljaWVudCBzdG9yYWdlIHN5c3RlbSB3aXRoIGRlc2lnbmF0ZWQgc3BhY2VzIGZvciBldmVyeSBpdGVtLjwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImdyb3VwIGN1cnNvci1wb2ludGVyXCJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBvcGVuTW9kYWwoe1xuICAgICAgICAgICAgICAgICAgc3JjOiBcIi9pbWFnZXMvQ2xvc2V0L0lNR182MDA1LlBOR1wiLFxuICAgICAgICAgICAgICAgICAgYWx0OiBcIkNsb3NldCBPcmdhbml6YXRpb24gM1wiLFxuICAgICAgICAgICAgICAgICAgdGl0bGU6IFwiQ3VzdG9tIFNvbHV0aW9uc1wiLFxuICAgICAgICAgICAgICAgICAgZGVzY3JpcHRpb246IFwiVGFpbG9yZWQgb3JnYW5pemF0aW9uIHN5c3RlbXMgZGVzaWduZWQgZm9yIHlvdXIgc3BlY2lmaWMgbmVlZHMuIEV2ZXJ5IGVsZW1lbnQgaXMgY3VzdG9taXplZCB0byBmaXQgeW91ciBsaWZlc3R5bGUsIHNwYWNlIGNvbnN0cmFpbnRzLCBhbmQgc3RvcmFnZSByZXF1aXJlbWVudHMuXCJcbiAgICAgICAgICAgICAgICB9KX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZm9yZXN0LWdyZWVuLTEwMCByb3VuZGVkLWxnIG92ZXJmbG93LWhpZGRlbiBoLTgwIG1iLTRcIj5cbiAgICAgICAgICAgICAgICAgIDxJbWFnZVxuICAgICAgICAgICAgICAgICAgICBzcmM9XCIvaW1hZ2VzL0Nsb3NldC9JTUdfNjAwNS5QTkdcIlxuICAgICAgICAgICAgICAgICAgICBhbHQ9XCJDbG9zZXQgT3JnYW5pemF0aW9uIDNcIlxuICAgICAgICAgICAgICAgICAgICB3aWR0aD17NDAwfVxuICAgICAgICAgICAgICAgICAgICBoZWlnaHQ9ezMyMH1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBvYmplY3QtY292ZXIgZ3JvdXAtaG92ZXI6c2NhbGUtMTA1IHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTMwMFwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1mb3Jlc3QtZ3JlZW4tNzAwIG1iLTJcIj5DdXN0b20gU29sdXRpb25zPC9oMz5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWZvcmVzdC1ncmVlbi02MDBcIj5UYWlsb3JlZCBvcmdhbml6YXRpb24gc3lzdGVtcyBkZXNpZ25lZCBmb3IgeW91ciBzcGVjaWZpYyBuZWVkcy48L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJncm91cCBjdXJzb3ItcG9pbnRlclwiXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gb3Blbk1vZGFsKHtcbiAgICAgICAgICAgICAgICAgIHNyYzogXCIvaW1hZ2VzL0Nsb3NldC9JTUdfNjAwNi5QTkdcIixcbiAgICAgICAgICAgICAgICAgIGFsdDogXCJDbG9zZXQgT3JnYW5pemF0aW9uIDRcIixcbiAgICAgICAgICAgICAgICAgIHRpdGxlOiBcIlNwYWNlIE9wdGltaXphdGlvblwiLFxuICAgICAgICAgICAgICAgICAgZGVzY3JpcHRpb246IFwiTWF4aW1pemluZyBldmVyeSBpbmNoIG9mIGF2YWlsYWJsZSBzcGFjZSB3aXRoIHNtYXJ0IGRlc2lnbi4gVmVydGljYWwgc3RvcmFnZSBzb2x1dGlvbnMgYW5kIG11bHRpLWxldmVsIG9yZ2FuaXphdGlvbiBjcmVhdGUgbWF4aW11bSBjYXBhY2l0eSBpbiBtaW5pbWFsIGZvb3RwcmludC5cIlxuICAgICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1mb3Jlc3QtZ3JlZW4tMTAwIHJvdW5kZWQtbGcgb3ZlcmZsb3ctaGlkZGVuIGgtODAgbWItNFwiPlxuICAgICAgICAgICAgICAgICAgPEltYWdlXG4gICAgICAgICAgICAgICAgICAgIHNyYz1cIi9pbWFnZXMvQ2xvc2V0L0lNR182MDA2LlBOR1wiXG4gICAgICAgICAgICAgICAgICAgIGFsdD1cIkNsb3NldCBPcmdhbml6YXRpb24gNFwiXG4gICAgICAgICAgICAgICAgICAgIHdpZHRoPXs0MDB9XG4gICAgICAgICAgICAgICAgICAgIGhlaWdodD17MzIwfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1mdWxsIG9iamVjdC1jb3ZlciBncm91cC1ob3ZlcjpzY2FsZS0xMDUgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tMzAwXCJcbiAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWZvcmVzdC1ncmVlbi03MDAgbWItMlwiPlNwYWNlIE9wdGltaXphdGlvbjwvaDM+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1mb3Jlc3QtZ3JlZW4tNjAwXCI+TWF4aW1pemluZyBldmVyeSBpbmNoIG9mIGF2YWlsYWJsZSBzcGFjZSB3aXRoIHNtYXJ0IGRlc2lnbi48L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJncm91cCBjdXJzb3ItcG9pbnRlclwiXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gb3Blbk1vZGFsKHtcbiAgICAgICAgICAgICAgICAgIHNyYzogXCIvaW1hZ2VzL0Nsb3NldC9JTUdfNjAwNy5QTkdcIixcbiAgICAgICAgICAgICAgICAgIGFsdDogXCJDbG9zZXQgT3JnYW5pemF0aW9uIDVcIixcbiAgICAgICAgICAgICAgICAgIHRpdGxlOiBcIkVsZWdhbnQgRGVzaWduXCIsXG4gICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogXCJCZWF1dGlmdWwgYW5kIGZ1bmN0aW9uYWwgY2xvc2V0IGRlc2lnbiB0aGF0IGNvbXBsZW1lbnRzIHlvdXIgaG9tZS4gU29waGlzdGljYXRlZCBmaW5pc2hlcyBhbmQgdGhvdWdodGZ1bCBkZXRhaWxzIGNyZWF0ZSBhIGx1eHVyaW91cyBzdG9yYWdlIGV4cGVyaWVuY2UuXCJcbiAgICAgICAgICAgICAgICB9KX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZm9yZXN0LWdyZWVuLTEwMCByb3VuZGVkLWxnIG92ZXJmbG93LWhpZGRlbiBoLTgwIG1iLTRcIj5cbiAgICAgICAgICAgICAgICAgIDxJbWFnZVxuICAgICAgICAgICAgICAgICAgICBzcmM9XCIvaW1hZ2VzL0Nsb3NldC9JTUdfNjAwNy5QTkdcIlxuICAgICAgICAgICAgICAgICAgICBhbHQ9XCJDbG9zZXQgT3JnYW5pemF0aW9uIDVcIlxuICAgICAgICAgICAgICAgICAgICB3aWR0aD17NDAwfVxuICAgICAgICAgICAgICAgICAgICBoZWlnaHQ9ezMyMH1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBvYmplY3QtY292ZXIgZ3JvdXAtaG92ZXI6c2NhbGUtMTA1IHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTMwMFwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1mb3Jlc3QtZ3JlZW4tNzAwIG1iLTJcIj5FbGVnYW50IERlc2lnbjwvaDM+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1mb3Jlc3QtZ3JlZW4tNjAwXCI+QmVhdXRpZnVsIGFuZCBmdW5jdGlvbmFsIGNsb3NldCBkZXNpZ24gdGhhdCBjb21wbGVtZW50cyB5b3VyIGhvbWUuPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZ3JvdXAgY3Vyc29yLXBvaW50ZXJcIlxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IG9wZW5Nb2RhbCh7XG4gICAgICAgICAgICAgICAgICBzcmM6IFwiL2ltYWdlcy9DbG9zZXQvSU1HXzYwMDguUE5HXCIsXG4gICAgICAgICAgICAgICAgICBhbHQ6IFwiQ2xvc2V0IE9yZ2FuaXphdGlvbiA2XCIsXG4gICAgICAgICAgICAgICAgICB0aXRsZTogXCJDb21wbGV0ZSBPcmdhbml6YXRpb25cIixcbiAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBcIkNvbXByZWhlbnNpdmUgc3RvcmFnZSBzb2x1dGlvbiBmb3IgYWxsIHlvdXIgYmVsb25naW5ncy4gRXZlcnkgaXRlbSBoYXMgaXRzIHBsYWNlIGluIHRoaXMgdGhvdWdodGZ1bGx5IGRlc2lnbmVkIG9yZ2FuaXphdGlvbiBzeXN0ZW0gdGhhdCBtYWludGFpbnMgb3JkZXIgYW5kIGFjY2Vzc2liaWxpdHkuXCJcbiAgICAgICAgICAgICAgICB9KX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctZm9yZXN0LWdyZWVuLTEwMCByb3VuZGVkLWxnIG92ZXJmbG93LWhpZGRlbiBoLTgwIG1iLTRcIj5cbiAgICAgICAgICAgICAgICAgIDxJbWFnZVxuICAgICAgICAgICAgICAgICAgICBzcmM9XCIvaW1hZ2VzL0Nsb3NldC9JTUdfNjAwOC5QTkdcIlxuICAgICAgICAgICAgICAgICAgICBhbHQ9XCJDbG9zZXQgT3JnYW5pemF0aW9uIDZcIlxuICAgICAgICAgICAgICAgICAgICB3aWR0aD17NDAwfVxuICAgICAgICAgICAgICAgICAgICBoZWlnaHQ9ezMyMH1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBvYmplY3QtY292ZXIgZ3JvdXAtaG92ZXI6c2NhbGUtMTA1IHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTMwMFwiXG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1mb3Jlc3QtZ3JlZW4tNzAwIG1iLTJcIj5Db21wbGV0ZSBPcmdhbml6YXRpb248L2gzPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZm9yZXN0LWdyZWVuLTYwMFwiPkNvbXByZWhlbnNpdmUgc3RvcmFnZSBzb2x1dGlvbiBmb3IgYWxsIHlvdXIgYmVsb25naW5ncy48L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9zZWN0aW9uPlxuXG4gICAgICB7LyogQ2FsbCB0byBBY3Rpb24gKi99XG4gICAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJweS0yMCBiZy1mb3Jlc3QtZ3JlZW4tNjAwXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LTggdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0zeGwgbWQ6dGV4dC00eGwgZm9udC1ib2xkIHRleHQtd2hpdGUgbWItNlwiPlxuICAgICAgICAgICAgUmVhZHkgdG8gU3RhcnQgWW91ciA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdvbGQtNDAwXCI+UHJvamVjdD88L3NwYW4+XG4gICAgICAgICAgPC9oMj5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWxnIHRleHQtZm9yZXN0LWdyZWVuLTEwMCBtYi04IG1heC13LTJ4bCBteC1hdXRvXCI+XG4gICAgICAgICAgICBMZXQncyBicmluZyB5b3VyIHZpc2lvbiB0byBsaWZlLiBDb250YWN0IHVzIHRvZGF5IHRvIGRpc2N1c3MgeW91ciBpbnRlcmlvciBkZXNpZ24gbmVlZHMuXG4gICAgICAgICAgPC9wPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBzbTpmbGV4LXJvdyBnYXAtNCBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgPExpbmsgaHJlZj1cIi8jY29udGFjdFwiIGNsYXNzTmFtZT1cImJnLWdvbGQtNTAwIHRleHQtd2hpdGUgcHgtOCBweS0zIHJvdW5kZWQtbGcgdGV4dC1sZyBmb250LXNlbWlib2xkIGhvdmVyOmJnLWdvbGQtNjAwIHRyYW5zaXRpb24gZHVyYXRpb24tMzAwXCI+XG4gICAgICAgICAgICAgIEdldCBTdGFydGVkXG4gICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICA8TGluayBocmVmPVwiL1wiIGNsYXNzTmFtZT1cImJvcmRlci0yIGJvcmRlci13aGl0ZSB0ZXh0LXdoaXRlIHB4LTggcHktMyByb3VuZGVkLWxnIHRleHQtbGcgZm9udC1zZW1pYm9sZCBob3ZlcjpiZy13aGl0ZSBob3Zlcjp0ZXh0LWZvcmVzdC1ncmVlbi02MDAgdHJhbnNpdGlvbiBkdXJhdGlvbi0zMDBcIj5cbiAgICAgICAgICAgICAgQmFjayB0byBIb21lXG4gICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9zZWN0aW9uPlxuXG4gICAgICB7LyogRm9vdGVyICovfVxuICAgICAgPGZvb3RlciBjbGFzc05hbWU9XCJiZy1mb3Jlc3QtZ3JlZW4tODAwIHRleHQtd2hpdGUgcHktMTJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC00IHNtOnB4LTYgbGc6cHgtOFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtNCBnYXAtOFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb2wtc3Bhbi0xIG1kOmNvbC1zcGFuLTJcIj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdvbGQtNDAwIG1iLTRcIj5Nb2Rlcm4gSGF2ZW48L2gzPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWZvcmVzdC1ncmVlbi0yMDAgbWItNFwiPlxuICAgICAgICAgICAgICAgIENyZWF0aW5nIGJlYXV0aWZ1bCwgZnVuY3Rpb25hbCBzcGFjZXMgdGhhdCByZWZsZWN0IHlvdXIgdW5pcXVlIHN0eWxlIGFuZCBlbmhhbmNlIHlvdXIgbGlmZXN0eWxlLlxuICAgICAgICAgICAgICAgIFlvdXIgZHJlYW0gaG9tZSBpcyBqdXN0IGEgY29uc3VsdGF0aW9uIGF3YXkuXG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICAgIDxhIGhyZWY9XCIjXCIgY2xhc3NOYW1lPVwidGV4dC1nb2xkLTQwMCBob3Zlcjp0ZXh0LWdvbGQtMzAwXCI+8J+TmDwvYT5cbiAgICAgICAgICAgICAgICA8YSBocmVmPVwiI1wiIGNsYXNzTmFtZT1cInRleHQtZ29sZC00MDAgaG92ZXI6dGV4dC1nb2xkLTMwMFwiPvCfk7c8L2E+XG4gICAgICAgICAgICAgICAgPGEgaHJlZj1cIiNcIiBjbGFzc05hbWU9XCJ0ZXh0LWdvbGQtNDAwIGhvdmVyOnRleHQtZ29sZC0zMDBcIj7wn5CmPC9hPlxuICAgICAgICAgICAgICAgIDxhIGhyZWY9XCIjXCIgY2xhc3NOYW1lPVwidGV4dC1nb2xkLTQwMCBob3Zlcjp0ZXh0LWdvbGQtMzAwXCI+8J+SvDwvYT5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1nb2xkLTQwMCBtYi00XCI+UXVpY2sgTGlua3M8L2g0PlxuICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwic3BhY2UteS0yIHRleHQtZm9yZXN0LWdyZWVuLTIwMFwiPlxuICAgICAgICAgICAgICAgIDxsaT48TGluayBocmVmPVwiL1wiIGNsYXNzTmFtZT1cImhvdmVyOnRleHQtZ29sZC00MDBcIj5Ib21lPC9MaW5rPjwvbGk+XG4gICAgICAgICAgICAgICAgPGxpPjxMaW5rIGhyZWY9XCIvI2Fib3V0XCIgY2xhc3NOYW1lPVwiaG92ZXI6dGV4dC1nb2xkLTQwMFwiPkFib3V0PC9MaW5rPjwvbGk+XG4gICAgICAgICAgICAgICAgPGxpPjxMaW5rIGhyZWY9XCIvI3NlcnZpY2VzXCIgY2xhc3NOYW1lPVwiaG92ZXI6dGV4dC1nb2xkLTQwMFwiPlNlcnZpY2VzPC9MaW5rPjwvbGk+XG4gICAgICAgICAgICAgICAgPGxpPjxMaW5rIGhyZWY9XCIvZ2FsbGVyeVwiIGNsYXNzTmFtZT1cImhvdmVyOnRleHQtZ29sZC00MDBcIj5HYWxsZXJ5PC9MaW5rPjwvbGk+XG4gICAgICAgICAgICAgICAgPGxpPjxMaW5rIGhyZWY9XCIvI2NvbnRhY3RcIiBjbGFzc05hbWU9XCJob3Zlcjp0ZXh0LWdvbGQtNDAwXCI+Q29udGFjdDwvTGluaz48L2xpPlxuICAgICAgICAgICAgICA8L3VsPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZ29sZC00MDAgbWItNFwiPkNvbGxlY3Rpb25zPC9oND5cbiAgICAgICAgICAgICAgPHVsIGNsYXNzTmFtZT1cInNwYWNlLXktMiB0ZXh0LWZvcmVzdC1ncmVlbi0yMDBcIj5cbiAgICAgICAgICAgICAgICA8bGk+S2l0Y2hlbiBSZWNvbnN0cnVjdGlvbjwvbGk+XG4gICAgICAgICAgICAgICAgPGxpPk1vZGVybiBLaXRjaGVuIERlc2lnbnM8L2xpPlxuICAgICAgICAgICAgICAgIDxsaT5DbG9zZXQgT3JnYW5pemF0aW9uPC9saT5cbiAgICAgICAgICAgICAgICA8bGk+Q3VzdG9tIFN0b3JhZ2UgU29sdXRpb25zPC9saT5cbiAgICAgICAgICAgICAgICA8bGk+SW50ZXJpb3IgU3R5bGluZzwvbGk+XG4gICAgICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJvcmRlci10IGJvcmRlci1mb3Jlc3QtZ3JlZW4tNzAwIG10LTggcHQtOCB0ZXh0LWNlbnRlciB0ZXh0LWZvcmVzdC1ncmVlbi0yMDBcIj5cbiAgICAgICAgICAgIDxwPiZjb3B5OyAyMDI0IE1vZGVybiBIYXZlbiBJbnRlcmlvciBEZXNpZ24uIEFsbCByaWdodHMgcmVzZXJ2ZWQuPC9wPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZm9vdGVyPlxuXG4gICAgICB7LyogSW1hZ2UgTW9kYWwgKi99XG4gICAgICB7c2VsZWN0ZWRJbWFnZSAmJiAoXG4gICAgICAgIDxkaXZcbiAgICAgICAgICBjbGFzc05hbWU9XCJmaXhlZCBpbnNldC0wIGJnLWJsYWNrIGJnLW9wYWNpdHktNzUgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgei01MCBwLTRcIlxuICAgICAgICAgIG9uQ2xpY2s9e2Nsb3NlTW9kYWx9XG4gICAgICAgID5cbiAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJyZWxhdGl2ZSBtYXgtdy00eGwgbWF4LWgtZnVsbCBiZy13aGl0ZSByb3VuZGVkLWxnIG92ZXJmbG93LWhpZGRlblwiXG4gICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4gZS5zdG9wUHJvcGFnYXRpb24oKX1cbiAgICAgICAgICA+XG4gICAgICAgICAgICB7LyogQ2xvc2UgYnV0dG9uICovfVxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXtjbG9zZU1vZGFsfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtNCByaWdodC00IHotMTAgYmctZm9yZXN0LWdyZWVuLTYwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtZnVsbCB3LTEwIGgtMTAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgaG92ZXI6YmctZm9yZXN0LWdyZWVuLTcwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIOKclVxuICAgICAgICAgICAgPC9idXR0b24+XG5cbiAgICAgICAgICAgIHsvKiBJbWFnZSAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgICAgPEltYWdlXG4gICAgICAgICAgICAgICAgc3JjPXtzZWxlY3RlZEltYWdlLnNyY31cbiAgICAgICAgICAgICAgICBhbHQ9e3NlbGVjdGVkSW1hZ2UuYWx0fVxuICAgICAgICAgICAgICAgIHdpZHRoPXsxMjAwfVxuICAgICAgICAgICAgICAgIGhlaWdodD17ODAwfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBoLWF1dG8gbWF4LWgtWzgwdmhdIG9iamVjdC1jb250YWluXCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7LyogSW1hZ2UgaW5mbyAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC02IGJnLXdoaXRlXCI+XG4gICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1mb3Jlc3QtZ3JlZW4tNzAwIG1iLTJcIj57c2VsZWN0ZWRJbWFnZS50aXRsZX08L2gzPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWZvcmVzdC1ncmVlbi02MDBcIj57c2VsZWN0ZWRJbWFnZS5kZXNjcmlwdGlvbn08L3A+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuICAgIDwvbWFpbj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJJbWFnZSIsIkxpbmsiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIkdhbGxlcnkiLCJzZWxlY3RlZEltYWdlIiwic2V0U2VsZWN0ZWRJbWFnZSIsIm9wZW5Nb2RhbCIsImltYWdlIiwiY2xvc2VNb2RhbCIsImhhbmRsZUtleURvd24iLCJldmVudCIsImtleSIsImRvY3VtZW50IiwiYWRkRXZlbnRMaXN0ZW5lciIsImJvZHkiLCJzdHlsZSIsIm92ZXJmbG93IiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsIm1haW4iLCJjbGFzc05hbWUiLCJuYXYiLCJkaXYiLCJocmVmIiwic2VjdGlvbiIsImgxIiwic3BhbiIsInAiLCJoMiIsIm9uQ2xpY2siLCJzcmMiLCJhbHQiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwid2lkdGgiLCJoZWlnaHQiLCJoMyIsImZvb3RlciIsImEiLCJoNCIsInVsIiwibGkiLCJlIiwic3RvcFByb3BhZ2F0aW9uIiwiYnV0dG9uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/gallery/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"9974e75019d9\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbW9kZXJuLWhhdmVuLy4vc3JjL2FwcC9nbG9iYWxzLmNzcz80ZmNjIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiOTk3NGU3NTAxOWQ5XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/gallery/page.tsx":
/*!**********************************!*\
  !*** ./src/app/gallery/page.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Flame workspace\morderm-haven\src\app\gallery\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Flame workspace\morderm-haven\src\app\gallery\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Modern Haven - Interior Design\",\n    description: \"Transform your space with Modern Haven's expert interior design services. Creating beautiful, functional spaces with forest green, gold, and white elegance.\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className),\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 19,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFJTUE7QUFGaUI7QUFJaEIsTUFBTUMsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUU7QUFFYSxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1I7SUFDQSxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztrQkFDVCw0RUFBQ0M7WUFBS0MsV0FBV1QsK0pBQWU7c0JBQUdLOzs7Ozs7Ozs7OztBQUd6QyIsInNvdXJjZXMiOlsid2VicGFjazovL21vZGVybi1oYXZlbi8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tIFwibmV4dC9mb250L2dvb2dsZVwiO1xuaW1wb3J0IFwiLi9nbG9iYWxzLmNzc1wiO1xuXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogW1wibGF0aW5cIl0gfSk7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiBcIk1vZGVybiBIYXZlbiAtIEludGVyaW9yIERlc2lnblwiLFxuICBkZXNjcmlwdGlvbjogXCJUcmFuc2Zvcm0geW91ciBzcGFjZSB3aXRoIE1vZGVybiBIYXZlbidzIGV4cGVydCBpbnRlcmlvciBkZXNpZ24gc2VydmljZXMuIENyZWF0aW5nIGJlYXV0aWZ1bCwgZnVuY3Rpb25hbCBzcGFjZXMgd2l0aCBmb3Jlc3QgZ3JlZW4sIGdvbGQsIGFuZCB3aGl0ZSBlbGVnYW5jZS5cIixcbn07XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IFJlYWRvbmx5PHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0+KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+e2NoaWxkcmVufTwvYm9keT5cbiAgICA8L2h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiaW50ZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fgallery%2Fpage&page=%2Fgallery%2Fpage&appPaths=%2Fgallery%2Fpage&pagePath=private-next-app-dir%2Fgallery%2Fpage.tsx&appDir=C%3A%5CFlame%20workspace%5Cmorderm-haven%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CFlame%20workspace%5Cmorderm-haven&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();