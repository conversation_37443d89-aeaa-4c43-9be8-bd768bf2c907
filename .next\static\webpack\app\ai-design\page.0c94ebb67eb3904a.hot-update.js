"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/ai-design/page",{

/***/ "(app-pages-browser)/./src/app/ai-design/page.tsx":
/*!************************************!*\
  !*** ./src/app/ai-design/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AIDesign; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction AIDesign() {\n    _s();\n    const [prompt, setPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [generatedImages, setGeneratedImages] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [suggestion, setSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    // Interior design keywords to validate prompts\n    const interiorKeywords = [\n        \"kitchen\",\n        \"bedroom\",\n        \"living room\",\n        \"bathroom\",\n        \"dining room\",\n        \"office\",\n        \"closet\",\n        \"interior\",\n        \"home\",\n        \"house\",\n        \"apartment\",\n        \"room\",\n        \"space\",\n        \"design\",\n        \"furniture\",\n        \"cabinet\",\n        \"counter\",\n        \"sofa\",\n        \"chair\",\n        \"table\",\n        \"bed\",\n        \"shelf\",\n        \"storage\",\n        \"modern\",\n        \"contemporary\",\n        \"traditional\",\n        \"minimalist\",\n        \"luxury\",\n        \"cozy\",\n        \"lighting\",\n        \"window\",\n        \"wall\",\n        \"floor\",\n        \"ceiling\",\n        \"decor\",\n        \"renovation\"\n    ];\n    const validatePrompt = (inputPrompt)=>{\n        const lowerPrompt = inputPrompt.toLowerCase();\n        return interiorKeywords.some((keyword)=>lowerPrompt.includes(keyword));\n    };\n    const enhancePrompt = (userPrompt)=>{\n        // Add interior design context to ensure relevant results\n        const baseContext = \"Interior design, home decor, architectural photography, professional lighting, high quality, detailed, \";\n        // Check if prompt already contains interior context\n        const lowerPrompt = userPrompt.toLowerCase();\n        if (lowerPrompt.includes(\"interior\") || lowerPrompt.includes(\"room\") || lowerPrompt.includes(\"kitchen\") || lowerPrompt.includes(\"bedroom\")) {\n            return \"\".concat(baseContext).concat(userPrompt);\n        }\n        return \"\".concat(baseContext, \"modern \").concat(userPrompt, \" interior design\");\n    };\n    const generateImage = async ()=>{\n        if (!prompt.trim()) {\n            setError(\"Please enter a design description\");\n            return;\n        }\n        if (!validatePrompt(prompt)) {\n            setError(\"Please describe an interior design or home-related concept (e.g., 'modern kitchen', 'cozy bedroom', 'minimalist living room')\");\n            return;\n        }\n        setIsGenerating(true);\n        setError(\"\");\n        setSuggestion(\"\");\n        try {\n            const enhancedPrompt = enhancePrompt(prompt);\n            const response = await fetch(\"/api/generate-image\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    prompt: enhancedPrompt\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to generate image\");\n            }\n            const data = await response.json();\n            const newImage = {\n                url: data.imageUrl,\n                prompt: prompt,\n                timestamp: Date.now()\n            };\n            setGeneratedImages((prev)=>[\n                    newImage,\n                    ...prev\n                ]);\n            setPrompt(\"\");\n        } catch (err) {\n            var _err_response;\n            // Parse error response\n            let errorMessage = \"Failed to generate image. Please try again.\";\n            let suggestionMessage = \"\";\n            if ((_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.data) {\n                errorMessage = err.response.data.error || errorMessage;\n                suggestionMessage = err.response.data.suggestion || \"\";\n            } else if (err.message) {\n                errorMessage = err.message;\n            }\n            setError(errorMessage);\n            setSuggestion(suggestionMessage);\n            console.error(\"Error generating image:\", err);\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !isGenerating) {\n            generateImage();\n        }\n    };\n    const samplePrompts = [\n        \"Modern minimalist kitchen with white cabinets and marble countertops\",\n        \"Cozy bedroom with warm lighting and natural wood furniture\",\n        \"Contemporary living room with large windows and neutral colors\",\n        \"Luxury bathroom with marble tiles and gold fixtures\",\n        \"Scandinavian dining room with wooden table and pendant lights\",\n        \"Walk-in closet with organized storage and elegant lighting\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"bg-white shadow-sm border-b border-forest-green-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/\",\n                                    className: \"text-2xl font-bold text-forest-green-600\",\n                                    children: \"Modern Haven\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:block\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-10 flex items-baseline space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"Home\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/#about\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"About\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/#services\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"Services\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/gallery\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"Gallery\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/ai-design\",\n                                            className: \"bg-gold-500 text-white px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"AI Design\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/#contact\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"Contact\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative bg-gradient-to-br from-forest-green-50 to-white py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-5xl font-bold text-forest-green-700 mb-6\",\n                                children: [\n                                    \"AI \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gold-500\",\n                                        children: \"Design Studio\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 18\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-forest-green-600 mb-8 max-w-3xl mx-auto\",\n                                children: \"Bring your interior design ideas to life with our AI-powered design generator. Describe your vision and watch it transform into stunning visual concepts.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-forest-green-50 rounded-lg p-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold text-forest-green-700 mb-6\",\n                                                children: \"Create Your Design\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"prompt\",\n                                                                className: \"block text-sm font-medium text-forest-green-700 mb-2\",\n                                                                children: \"Describe your interior design vision\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                lineNumber: 174,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                id: \"prompt\",\n                                                                value: prompt,\n                                                                onChange: (e)=>setPrompt(e.target.value),\n                                                                onKeyPress: handleKeyPress,\n                                                                placeholder: \"e.g., Modern kitchen with white cabinets, marble countertops, and stainless steel appliances...\",\n                                                                className: \"w-full px-4 py-3 border border-forest-green-200 rounded-lg focus:ring-2 focus:ring-gold-500 focus:border-transparent resize-none\",\n                                                                rows: 6,\n                                                                disabled: isGenerating\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                lineNumber: 177,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-red-600 text-sm bg-red-50 p-3 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: error\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                lineNumber: 191,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            suggestion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"mt-2 text-blue-600 bg-blue-50 p-2 rounded border-l-4 border-blue-400\",\n                                                                children: [\n                                                                    \"\\uD83D\\uDCA1 \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Suggestion:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                        lineNumber: 194,\n                                                                        columnNumber: 30\n                                                                    }, this),\n                                                                    \" \",\n                                                                    suggestion\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                lineNumber: 193,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 190,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: generateImage,\n                                                        disabled: isGenerating || !prompt.trim(),\n                                                        className: \"w-full bg-gold-500 text-white py-3 px-6 rounded-lg font-semibold hover:bg-gold-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition duration-300\",\n                                                        children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                    lineNumber: 207,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Generating Design...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                            lineNumber: 206,\n                                                            columnNumber: 23\n                                                        }, this) : \"Generate Design\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white border border-forest-green-200 rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-4\",\n                                                children: \"\\uD83D\\uDCA1 Try these sample prompts:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: samplePrompts.map((samplePrompt, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setPrompt(samplePrompt),\n                                                        className: \"w-full text-left p-3 bg-forest-green-50 border border-forest-green-200 rounded-lg hover:border-gold-500 hover:bg-gold-50 transition duration-200\",\n                                                        disabled: isGenerating,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-forest-green-600\",\n                                                            children: samplePrompt\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                            lineNumber: 230,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, index, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-forest-green-600 rounded-lg p-6 text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold mb-4\",\n                                                children: \"\\uD83C\\uDFAF Tips for Better Results\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-2 text-forest-green-100 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Be specific about room types (kitchen, bedroom, living room, etc.)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Include style preferences (modern, traditional, minimalist, luxury)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Mention colors, materials, and lighting preferences\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Describe the mood you want to create (cozy, elegant, bright, etc.)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Include furniture and decor elements you envision\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-forest-green-50 rounded-lg p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-forest-green-700 mb-6\",\n                                            children: generatedImages.length > 0 ? \"Your Generated Designs\" : \"Generated Designs Will Appear Here\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 17\n                                        }, this),\n                                        generatedImages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-6xl mb-4\",\n                                                    children: \"\\uD83C\\uDFA8\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-forest-green-600 mb-4\",\n                                                    children: \"Start by describing your interior design vision in the left panel.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-forest-green-500\",\n                                                    children: 'Your AI-generated designs will appear here once you click \"Generate Design\".'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 257,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6 max-h-[800px] overflow-y-auto\",\n                                            children: generatedImages.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white rounded-lg shadow-lg overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative h-64\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                                src: image.url,\n                                                                alt: \"Generated design: \".concat(image.prompt),\n                                                                fill: true,\n                                                                className: \"object-cover\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                lineNumber: 271,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                            lineNumber: 270,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-forest-green-600 text-sm mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"Prompt:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                            lineNumber: 280,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \" \",\n                                                                        image.prompt\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                    lineNumber: 279,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-500 text-xs\",\n                                                                    children: [\n                                                                        \"Generated on \",\n                                                                        new Date(image.timestamp).toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                    lineNumber: 282,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-3 flex space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>{\n                                                                                const link = document.createElement(\"a\");\n                                                                                link.href = image.url;\n                                                                                link.download = \"modern-haven-design-\".concat(image.timestamp, \".png\");\n                                                                                link.click();\n                                                                            },\n                                                                            className: \"text-xs bg-gold-500 text-white px-3 py-1 rounded hover:bg-gold-600 transition duration-200\",\n                                                                            children: \"Download\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                            lineNumber: 286,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>setPrompt(image.prompt),\n                                                                            className: \"text-xs bg-forest-green-600 text-white px-3 py-1 rounded hover:bg-forest-green-700 transition duration-200\",\n                                                                            children: \"Use Prompt\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                            lineNumber: 297,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                    lineNumber: 285,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-forest-green-800 text-white py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-1 md:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-gold-400 mb-4\",\n                                            children: \"Modern Haven\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-forest-green-200 mb-4\",\n                                            children: \"Creating beautiful, functional spaces that reflect your unique style and enhance your lifestyle. Your dream home is just a consultation away.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gold-400 hover:text-gold-300\",\n                                                    children: \"\\uD83D\\uDCD8\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gold-400 hover:text-gold-300\",\n                                                    children: \"\\uD83D\\uDCF7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gold-400 hover:text-gold-300\",\n                                                    children: \"\\uD83D\\uDC26\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gold-400 hover:text-gold-300\",\n                                                    children: \"\\uD83D\\uDCBC\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold text-gold-400 mb-4\",\n                                            children: \"Quick Links\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-forest-green-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"Home\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 335,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/#about\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"About\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/#services\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"Services\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/gallery\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"Gallery\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/ai-design\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"AI Design\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/#contact\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"Contact\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold text-gold-400 mb-4\",\n                                            children: \"AI Features\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-forest-green-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Interior Design Generation\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 346,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Room Visualization\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Style Exploration\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Design Inspiration\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Custom Concepts\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 350,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-forest-green-700 mt-8 pt-8 text-center text-forest-green-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"\\xa9 2024 Modern Haven Interior Design. All rights reserved.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                            lineNumber: 354,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                    lineNumber: 317,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                lineNumber: 316,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n        lineNumber: 122,\n        columnNumber: 5\n    }, this);\n}\n_s(AIDesign, \"VCjgKJk8u9gPQnI6P2vgaG/fvZk=\");\n_c = AIDesign;\nvar _c;\n$RefreshReg$(_c, \"AIDesign\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvYWktZGVzaWduL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQytCO0FBQ0Y7QUFDSTtBQVFsQixTQUFTRzs7SUFDdEIsTUFBTSxDQUFDQyxRQUFRQyxVQUFVLEdBQUdILCtDQUFRQSxDQUFDO0lBQ3JDLE1BQU0sQ0FBQ0ksY0FBY0MsZ0JBQWdCLEdBQUdMLCtDQUFRQSxDQUFDO0lBQ2pELE1BQU0sQ0FBQ00saUJBQWlCQyxtQkFBbUIsR0FBR1AsK0NBQVFBLENBQW1CLEVBQUU7SUFDM0UsTUFBTSxDQUFDUSxPQUFPQyxTQUFTLEdBQUdULCtDQUFRQSxDQUFDO0lBQ25DLE1BQU0sQ0FBQ1UsWUFBWUMsY0FBYyxHQUFHWCwrQ0FBUUEsQ0FBQztJQUU3QywrQ0FBK0M7SUFDL0MsTUFBTVksbUJBQW1CO1FBQ3ZCO1FBQVc7UUFBVztRQUFlO1FBQVk7UUFBZTtRQUFVO1FBQzFFO1FBQVk7UUFBUTtRQUFTO1FBQWE7UUFBUTtRQUFTO1FBQVU7UUFDckU7UUFBVztRQUFXO1FBQVE7UUFBUztRQUFTO1FBQU87UUFBUztRQUNoRTtRQUFVO1FBQWdCO1FBQWU7UUFBYztRQUFVO1FBQ2pFO1FBQVk7UUFBVTtRQUFRO1FBQVM7UUFBVztRQUFTO0tBQzVEO0lBRUQsTUFBTUMsaUJBQWlCLENBQUNDO1FBQ3RCLE1BQU1DLGNBQWNELFlBQVlFLFdBQVc7UUFDM0MsT0FBT0osaUJBQWlCSyxJQUFJLENBQUNDLENBQUFBLFVBQVdILFlBQVlJLFFBQVEsQ0FBQ0Q7SUFDL0Q7SUFFQSxNQUFNRSxnQkFBZ0IsQ0FBQ0M7UUFDckIseURBQXlEO1FBQ3pELE1BQU1DLGNBQWM7UUFFcEIsb0RBQW9EO1FBQ3BELE1BQU1QLGNBQWNNLFdBQVdMLFdBQVc7UUFDMUMsSUFBSUQsWUFBWUksUUFBUSxDQUFDLGVBQWVKLFlBQVlJLFFBQVEsQ0FBQyxXQUFXSixZQUFZSSxRQUFRLENBQUMsY0FBY0osWUFBWUksUUFBUSxDQUFDLFlBQVk7WUFDMUksT0FBTyxHQUFpQkUsT0FBZEMsYUFBeUIsT0FBWEQ7UUFDMUI7UUFFQSxPQUFPLEdBQXdCQSxPQUFyQkMsYUFBWSxXQUFvQixPQUFYRCxZQUFXO0lBQzVDO0lBRUEsTUFBTUUsZ0JBQWdCO1FBQ3BCLElBQUksQ0FBQ3JCLE9BQU9zQixJQUFJLElBQUk7WUFDbEJmLFNBQVM7WUFDVDtRQUNGO1FBRUEsSUFBSSxDQUFDSSxlQUFlWCxTQUFTO1lBQzNCTyxTQUFTO1lBQ1Q7UUFDRjtRQUVBSixnQkFBZ0I7UUFDaEJJLFNBQVM7UUFDVEUsY0FBYztRQUVkLElBQUk7WUFDRixNQUFNYyxpQkFBaUJMLGNBQWNsQjtZQUVyQyxNQUFNd0IsV0FBVyxNQUFNQyxNQUFNLHVCQUF1QjtnQkFDbERDLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsZ0JBQWdCO2dCQUNsQjtnQkFDQUMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO29CQUFFOUIsUUFBUXVCO2dCQUFlO1lBQ2hEO1lBRUEsSUFBSSxDQUFDQyxTQUFTTyxFQUFFLEVBQUU7Z0JBQ2hCLE1BQU0sSUFBSUMsTUFBTTtZQUNsQjtZQUVBLE1BQU1DLE9BQU8sTUFBTVQsU0FBU1UsSUFBSTtZQUVoQyxNQUFNQyxXQUEyQjtnQkFDL0JDLEtBQUtILEtBQUtJLFFBQVE7Z0JBQ2xCckMsUUFBUUE7Z0JBQ1JzQyxXQUFXQyxLQUFLQyxHQUFHO1lBQ3JCO1lBRUFuQyxtQkFBbUJvQyxDQUFBQSxPQUFRO29CQUFDTjt1QkFBYU07aUJBQUs7WUFDOUN4QyxVQUFVO1FBQ1osRUFBRSxPQUFPeUMsS0FBVTtnQkFLYkE7WUFKSix1QkFBdUI7WUFDdkIsSUFBSUMsZUFBZTtZQUNuQixJQUFJQyxvQkFBb0I7WUFFeEIsS0FBSUYsZ0JBQUFBLElBQUlsQixRQUFRLGNBQVprQixvQ0FBQUEsY0FBY1QsSUFBSSxFQUFFO2dCQUN0QlUsZUFBZUQsSUFBSWxCLFFBQVEsQ0FBQ1MsSUFBSSxDQUFDM0IsS0FBSyxJQUFJcUM7Z0JBQzFDQyxvQkFBb0JGLElBQUlsQixRQUFRLENBQUNTLElBQUksQ0FBQ3pCLFVBQVUsSUFBSTtZQUN0RCxPQUFPLElBQUlrQyxJQUFJRyxPQUFPLEVBQUU7Z0JBQ3RCRixlQUFlRCxJQUFJRyxPQUFPO1lBQzVCO1lBRUF0QyxTQUFTb0M7WUFDVGxDLGNBQWNtQztZQUNkRSxRQUFReEMsS0FBSyxDQUFDLDJCQUEyQm9DO1FBQzNDLFNBQVU7WUFDUnZDLGdCQUFnQjtRQUNsQjtJQUNGO0lBRUEsTUFBTTRDLGlCQUFpQixDQUFDQztRQUN0QixJQUFJQSxFQUFFQyxHQUFHLEtBQUssV0FBVyxDQUFDL0MsY0FBYztZQUN0Q21CO1FBQ0Y7SUFDRjtJQUVBLE1BQU02QixnQkFBZ0I7UUFDcEI7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO0tBQ0Q7SUFFRCxxQkFDRSw4REFBQ0M7UUFBS0MsV0FBVTs7MEJBRWQsOERBQUNDO2dCQUFJRCxXQUFVOzBCQUNiLDRFQUFDRTtvQkFBSUYsV0FBVTs4QkFDYiw0RUFBQ0U7d0JBQUlGLFdBQVU7OzBDQUNiLDhEQUFDRTtnQ0FBSUYsV0FBVTswQ0FDYiw0RUFBQ3ZELGlEQUFJQTtvQ0FBQzBELE1BQUs7b0NBQUlILFdBQVU7OENBQTJDOzs7Ozs7Ozs7OzswQ0FFdEUsOERBQUNFO2dDQUFJRixXQUFVOzBDQUNiLDRFQUFDRTtvQ0FBSUYsV0FBVTs7c0RBQ2IsOERBQUN2RCxpREFBSUE7NENBQUMwRCxNQUFLOzRDQUFJSCxXQUFVO3NEQUFxRjs7Ozs7O3NEQUM5Ryw4REFBQ3ZELGlEQUFJQTs0Q0FBQzBELE1BQUs7NENBQVVILFdBQVU7c0RBQXFGOzs7Ozs7c0RBQ3BILDhEQUFDdkQsaURBQUlBOzRDQUFDMEQsTUFBSzs0Q0FBYUgsV0FBVTtzREFBcUY7Ozs7OztzREFDdkgsOERBQUN2RCxpREFBSUE7NENBQUMwRCxNQUFLOzRDQUFXSCxXQUFVO3NEQUFxRjs7Ozs7O3NEQUNySCw4REFBQ3ZELGlEQUFJQTs0Q0FBQzBELE1BQUs7NENBQWFILFdBQVU7c0RBQWtFOzs7Ozs7c0RBQ3BHLDhEQUFDdkQsaURBQUlBOzRDQUFDMEQsTUFBSzs0Q0FBWUgsV0FBVTtzREFBcUY7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFRaEksOERBQUNJO2dCQUFRSixXQUFVOzBCQUNqQiw0RUFBQ0U7b0JBQUlGLFdBQVU7OEJBQ2IsNEVBQUNFO3dCQUFJRixXQUFVOzswQ0FDYiw4REFBQ0s7Z0NBQUdMLFdBQVU7O29DQUE0RDtrREFDckUsOERBQUNNO3dDQUFLTixXQUFVO2tEQUFnQjs7Ozs7Ozs7Ozs7OzBDQUVyQyw4REFBQ087Z0NBQUVQLFdBQVU7MENBQXVEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQVMxRSw4REFBQ0k7Z0JBQVFKLFdBQVU7MEJBQ2pCLDRFQUFDRTtvQkFBSUYsV0FBVTs4QkFDYiw0RUFBQ0U7d0JBQUlGLFdBQVU7OzBDQUdiLDhEQUFDRTtnQ0FBSUYsV0FBVTs7a0RBRWIsOERBQUNFO3dDQUFJRixXQUFVOzswREFDYiw4REFBQ1E7Z0RBQUdSLFdBQVU7MERBQWdEOzs7Ozs7MERBSTlELDhEQUFDRTtnREFBSUYsV0FBVTs7a0VBQ2IsOERBQUNFOzswRUFDQyw4REFBQ087Z0VBQU1DLFNBQVE7Z0VBQVNWLFdBQVU7MEVBQXVEOzs7Ozs7MEVBR3pGLDhEQUFDVztnRUFDQ0MsSUFBRztnRUFDSEMsT0FBT2pFO2dFQUNQa0UsVUFBVSxDQUFDbEIsSUFBTS9DLFVBQVUrQyxFQUFFbUIsTUFBTSxDQUFDRixLQUFLO2dFQUN6Q0csWUFBWXJCO2dFQUNac0IsYUFBWTtnRUFDWmpCLFdBQVU7Z0VBQ1ZrQixNQUFNO2dFQUNOQyxVQUFVckU7Ozs7Ozs7Ozs7OztvREFJYkksdUJBQ0MsOERBQUNnRDt3REFBSUYsV0FBVTs7MEVBQ2IsOERBQUNPO2dFQUFFUCxXQUFVOzBFQUFlOUM7Ozs7Ozs0REFDM0JFLDRCQUNDLDhEQUFDbUQ7Z0VBQUVQLFdBQVU7O29FQUF1RTtrRkFDL0UsOERBQUNvQjtrRkFBTzs7Ozs7O29FQUFvQjtvRUFBRWhFOzs7Ozs7Ozs7Ozs7O2tFQU16Qyw4REFBQ2lFO3dEQUNDQyxTQUFTckQ7d0RBQ1RrRCxVQUFVckUsZ0JBQWdCLENBQUNGLE9BQU9zQixJQUFJO3dEQUN0QzhCLFdBQVU7a0VBRVRsRCw2QkFDQyw4REFBQ29EOzREQUFJRixXQUFVOzs4RUFDYiw4REFBQ0U7b0VBQUlGLFdBQVU7Ozs7OztnRUFBdUU7Ozs7OzttRUFJeEY7Ozs7Ozs7Ozs7Ozs7Ozs7OztrREFPUiw4REFBQ0U7d0NBQUlGLFdBQVU7OzBEQUNiLDhEQUFDdUI7Z0RBQUd2QixXQUFVOzBEQUFtRDs7Ozs7OzBEQUdqRSw4REFBQ0U7Z0RBQUlGLFdBQVU7MERBQ1pGLGNBQWMwQixHQUFHLENBQUMsQ0FBQ0MsY0FBY0Msc0JBQ2hDLDhEQUFDTDt3REFFQ0MsU0FBUyxJQUFNekUsVUFBVTRFO3dEQUN6QnpCLFdBQVU7d0RBQ1ZtQixVQUFVckU7a0VBRVYsNEVBQUN3RDs0REFBS04sV0FBVTtzRUFBaUN5Qjs7Ozs7O3VEQUw1Q0M7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBWWIsOERBQUN4Qjt3Q0FBSUYsV0FBVTs7MERBQ2IsOERBQUN1QjtnREFBR3ZCLFdBQVU7MERBQXlCOzs7Ozs7MERBQ3ZDLDhEQUFDMkI7Z0RBQUczQixXQUFVOztrRUFDWiw4REFBQzRCO2tFQUFHOzs7Ozs7a0VBQ0osOERBQUNBO2tFQUFHOzs7Ozs7a0VBQ0osOERBQUNBO2tFQUFHOzs7Ozs7a0VBQ0osOERBQUNBO2tFQUFHOzs7Ozs7a0VBQ0osOERBQUNBO2tFQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBTVYsOERBQUMxQjtnQ0FBSUYsV0FBVTswQ0FDYiw0RUFBQ0U7b0NBQUlGLFdBQVU7O3NEQUNiLDhEQUFDdUI7NENBQUd2QixXQUFVO3NEQUNYaEQsZ0JBQWdCNkUsTUFBTSxHQUFHLElBQUksMkJBQTJCOzs7Ozs7d0NBRzFEN0UsZ0JBQWdCNkUsTUFBTSxLQUFLLGtCQUMxQiw4REFBQzNCOzRDQUFJRixXQUFVOzs4REFDYiw4REFBQ0U7b0RBQUlGLFdBQVU7OERBQWdCOzs7Ozs7OERBQy9CLDhEQUFDTztvREFBRVAsV0FBVTs4REFBNkI7Ozs7Ozs4REFHMUMsOERBQUNPO29EQUFFUCxXQUFVOzhEQUFnQzs7Ozs7Ozs7Ozs7aUVBSy9DLDhEQUFDRTs0Q0FBSUYsV0FBVTtzREFDWmhELGdCQUFnQndFLEdBQUcsQ0FBQyxDQUFDTSxPQUFPSixzQkFDM0IsOERBQUN4QjtvREFBZ0JGLFdBQVU7O3NFQUN6Qiw4REFBQ0U7NERBQUlGLFdBQVU7c0VBQ2IsNEVBQUN4RCxrREFBS0E7Z0VBQ0p1RixLQUFLRCxNQUFNOUMsR0FBRztnRUFDZGdELEtBQUsscUJBQWtDLE9BQWJGLE1BQU1sRixNQUFNO2dFQUN0Q3FGLElBQUk7Z0VBQ0pqQyxXQUFVOzs7Ozs7Ozs7OztzRUFHZCw4REFBQ0U7NERBQUlGLFdBQVU7OzhFQUNiLDhEQUFDTztvRUFBRVAsV0FBVTs7c0ZBQ1gsOERBQUNvQjtzRkFBTzs7Ozs7O3dFQUFnQjt3RUFBRVUsTUFBTWxGLE1BQU07Ozs7Ozs7OEVBRXhDLDhEQUFDMkQ7b0VBQUVQLFdBQVU7O3dFQUF3Qjt3RUFDckIsSUFBSWIsS0FBSzJDLE1BQU01QyxTQUFTLEVBQUVnRCxjQUFjOzs7Ozs7OzhFQUV4RCw4REFBQ2hDO29FQUFJRixXQUFVOztzRkFDYiw4REFBQ3FCOzRFQUNDQyxTQUFTO2dGQUNQLE1BQU1hLE9BQU9DLFNBQVNDLGFBQWEsQ0FBQztnRkFDcENGLEtBQUtoQyxJQUFJLEdBQUcyQixNQUFNOUMsR0FBRztnRkFDckJtRCxLQUFLRyxRQUFRLEdBQUcsdUJBQXVDLE9BQWhCUixNQUFNNUMsU0FBUyxFQUFDO2dGQUN2RGlELEtBQUtJLEtBQUs7NEVBQ1o7NEVBQ0F2QyxXQUFVO3NGQUNYOzs7Ozs7c0ZBR0QsOERBQUNxQjs0RUFDQ0MsU0FBUyxJQUFNekUsVUFBVWlGLE1BQU1sRixNQUFNOzRFQUNyQ29ELFdBQVU7c0ZBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7bURBL0JHMEI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBK0MxQiw4REFBQ2M7Z0JBQU94QyxXQUFVOzBCQUNoQiw0RUFBQ0U7b0JBQUlGLFdBQVU7O3NDQUNiLDhEQUFDRTs0QkFBSUYsV0FBVTs7OENBQ2IsOERBQUNFO29DQUFJRixXQUFVOztzREFDYiw4REFBQ3VCOzRDQUFHdkIsV0FBVTtzREFBd0M7Ozs7OztzREFDdEQsOERBQUNPOzRDQUFFUCxXQUFVO3NEQUE2Qjs7Ozs7O3NEQUkxQyw4REFBQ0U7NENBQUlGLFdBQVU7OzhEQUNiLDhEQUFDeUM7b0RBQUV0QyxNQUFLO29EQUFJSCxXQUFVOzhEQUFvQzs7Ozs7OzhEQUMxRCw4REFBQ3lDO29EQUFFdEMsTUFBSztvREFBSUgsV0FBVTs4REFBb0M7Ozs7Ozs4REFDMUQsOERBQUN5QztvREFBRXRDLE1BQUs7b0RBQUlILFdBQVU7OERBQW9DOzs7Ozs7OERBQzFELDhEQUFDeUM7b0RBQUV0QyxNQUFLO29EQUFJSCxXQUFVOzhEQUFvQzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUc5RCw4REFBQ0U7O3NEQUNDLDhEQUFDd0M7NENBQUcxQyxXQUFVO3NEQUEyQzs7Ozs7O3NEQUN6RCw4REFBQzJCOzRDQUFHM0IsV0FBVTs7OERBQ1osOERBQUM0Qjs4REFBRyw0RUFBQ25GLGlEQUFJQTt3REFBQzBELE1BQUs7d0RBQUlILFdBQVU7a0VBQXNCOzs7Ozs7Ozs7Ozs4REFDbkQsOERBQUM0Qjs4REFBRyw0RUFBQ25GLGlEQUFJQTt3REFBQzBELE1BQUs7d0RBQVVILFdBQVU7a0VBQXNCOzs7Ozs7Ozs7Ozs4REFDekQsOERBQUM0Qjs4REFBRyw0RUFBQ25GLGlEQUFJQTt3REFBQzBELE1BQUs7d0RBQWFILFdBQVU7a0VBQXNCOzs7Ozs7Ozs7Ozs4REFDNUQsOERBQUM0Qjs4REFBRyw0RUFBQ25GLGlEQUFJQTt3REFBQzBELE1BQUs7d0RBQVdILFdBQVU7a0VBQXNCOzs7Ozs7Ozs7Ozs4REFDMUQsOERBQUM0Qjs4REFBRyw0RUFBQ25GLGlEQUFJQTt3REFBQzBELE1BQUs7d0RBQWFILFdBQVU7a0VBQXNCOzs7Ozs7Ozs7Ozs4REFDNUQsOERBQUM0Qjs4REFBRyw0RUFBQ25GLGlEQUFJQTt3REFBQzBELE1BQUs7d0RBQVlILFdBQVU7a0VBQXNCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4Q0FHL0QsOERBQUNFOztzREFDQyw4REFBQ3dDOzRDQUFHMUMsV0FBVTtzREFBMkM7Ozs7OztzREFDekQsOERBQUMyQjs0Q0FBRzNCLFdBQVU7OzhEQUNaLDhEQUFDNEI7OERBQUc7Ozs7Ozs4REFDSiw4REFBQ0E7OERBQUc7Ozs7Ozs4REFDSiw4REFBQ0E7OERBQUc7Ozs7Ozs4REFDSiw4REFBQ0E7OERBQUc7Ozs7Ozs4REFDSiw4REFBQ0E7OERBQUc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FJViw4REFBQzFCOzRCQUFJRixXQUFVO3NDQUNiLDRFQUFDTzswQ0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU1mO0dBN1Z3QjVEO0tBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvYWktZGVzaWduL3BhZ2UudHN4PzUwMTciXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5pbXBvcnQgSW1hZ2UgZnJvbSBcIm5leHQvaW1hZ2VcIjtcbmltcG9ydCBMaW5rIGZyb20gXCJuZXh0L2xpbmtcIjtcbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSBcInJlYWN0XCI7XG5cbmludGVyZmFjZSBHZW5lcmF0ZWRJbWFnZSB7XG4gIHVybDogc3RyaW5nO1xuICBwcm9tcHQ6IHN0cmluZztcbiAgdGltZXN0YW1wOiBudW1iZXI7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEFJRGVzaWduKCkge1xuICBjb25zdCBbcHJvbXB0LCBzZXRQcm9tcHRdID0gdXNlU3RhdGUoXCJcIik7XG4gIGNvbnN0IFtpc0dlbmVyYXRpbmcsIHNldElzR2VuZXJhdGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtnZW5lcmF0ZWRJbWFnZXMsIHNldEdlbmVyYXRlZEltYWdlc10gPSB1c2VTdGF0ZTxHZW5lcmF0ZWRJbWFnZVtdPihbXSk7XG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGUoXCJcIik7XG4gIGNvbnN0IFtzdWdnZXN0aW9uLCBzZXRTdWdnZXN0aW9uXSA9IHVzZVN0YXRlKFwiXCIpO1xuXG4gIC8vIEludGVyaW9yIGRlc2lnbiBrZXl3b3JkcyB0byB2YWxpZGF0ZSBwcm9tcHRzXG4gIGNvbnN0IGludGVyaW9yS2V5d29yZHMgPSBbXG4gICAgXCJraXRjaGVuXCIsIFwiYmVkcm9vbVwiLCBcImxpdmluZyByb29tXCIsIFwiYmF0aHJvb21cIiwgXCJkaW5pbmcgcm9vbVwiLCBcIm9mZmljZVwiLCBcImNsb3NldFwiLFxuICAgIFwiaW50ZXJpb3JcIiwgXCJob21lXCIsIFwiaG91c2VcIiwgXCJhcGFydG1lbnRcIiwgXCJyb29tXCIsIFwic3BhY2VcIiwgXCJkZXNpZ25cIiwgXCJmdXJuaXR1cmVcIixcbiAgICBcImNhYmluZXRcIiwgXCJjb3VudGVyXCIsIFwic29mYVwiLCBcImNoYWlyXCIsIFwidGFibGVcIiwgXCJiZWRcIiwgXCJzaGVsZlwiLCBcInN0b3JhZ2VcIixcbiAgICBcIm1vZGVyblwiLCBcImNvbnRlbXBvcmFyeVwiLCBcInRyYWRpdGlvbmFsXCIsIFwibWluaW1hbGlzdFwiLCBcImx1eHVyeVwiLCBcImNvenlcIixcbiAgICBcImxpZ2h0aW5nXCIsIFwid2luZG93XCIsIFwid2FsbFwiLCBcImZsb29yXCIsIFwiY2VpbGluZ1wiLCBcImRlY29yXCIsIFwicmVub3ZhdGlvblwiXG4gIF07XG5cbiAgY29uc3QgdmFsaWRhdGVQcm9tcHQgPSAoaW5wdXRQcm9tcHQ6IHN0cmluZyk6IGJvb2xlYW4gPT4ge1xuICAgIGNvbnN0IGxvd2VyUHJvbXB0ID0gaW5wdXRQcm9tcHQudG9Mb3dlckNhc2UoKTtcbiAgICByZXR1cm4gaW50ZXJpb3JLZXl3b3Jkcy5zb21lKGtleXdvcmQgPT4gbG93ZXJQcm9tcHQuaW5jbHVkZXMoa2V5d29yZCkpO1xuICB9O1xuXG4gIGNvbnN0IGVuaGFuY2VQcm9tcHQgPSAodXNlclByb21wdDogc3RyaW5nKTogc3RyaW5nID0+IHtcbiAgICAvLyBBZGQgaW50ZXJpb3IgZGVzaWduIGNvbnRleHQgdG8gZW5zdXJlIHJlbGV2YW50IHJlc3VsdHNcbiAgICBjb25zdCBiYXNlQ29udGV4dCA9IFwiSW50ZXJpb3IgZGVzaWduLCBob21lIGRlY29yLCBhcmNoaXRlY3R1cmFsIHBob3RvZ3JhcGh5LCBwcm9mZXNzaW9uYWwgbGlnaHRpbmcsIGhpZ2ggcXVhbGl0eSwgZGV0YWlsZWQsIFwiO1xuXG4gICAgLy8gQ2hlY2sgaWYgcHJvbXB0IGFscmVhZHkgY29udGFpbnMgaW50ZXJpb3IgY29udGV4dFxuICAgIGNvbnN0IGxvd2VyUHJvbXB0ID0gdXNlclByb21wdC50b0xvd2VyQ2FzZSgpO1xuICAgIGlmIChsb3dlclByb21wdC5pbmNsdWRlcyhcImludGVyaW9yXCIpIHx8IGxvd2VyUHJvbXB0LmluY2x1ZGVzKFwicm9vbVwiKSB8fCBsb3dlclByb21wdC5pbmNsdWRlcyhcImtpdGNoZW5cIikgfHwgbG93ZXJQcm9tcHQuaW5jbHVkZXMoXCJiZWRyb29tXCIpKSB7XG4gICAgICByZXR1cm4gYCR7YmFzZUNvbnRleHR9JHt1c2VyUHJvbXB0fWA7XG4gICAgfVxuXG4gICAgcmV0dXJuIGAke2Jhc2VDb250ZXh0fW1vZGVybiAke3VzZXJQcm9tcHR9IGludGVyaW9yIGRlc2lnbmA7XG4gIH07XG5cbiAgY29uc3QgZ2VuZXJhdGVJbWFnZSA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIXByb21wdC50cmltKCkpIHtcbiAgICAgIHNldEVycm9yKFwiUGxlYXNlIGVudGVyIGEgZGVzaWduIGRlc2NyaXB0aW9uXCIpO1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIGlmICghdmFsaWRhdGVQcm9tcHQocHJvbXB0KSkge1xuICAgICAgc2V0RXJyb3IoXCJQbGVhc2UgZGVzY3JpYmUgYW4gaW50ZXJpb3IgZGVzaWduIG9yIGhvbWUtcmVsYXRlZCBjb25jZXB0IChlLmcuLCAnbW9kZXJuIGtpdGNoZW4nLCAnY296eSBiZWRyb29tJywgJ21pbmltYWxpc3QgbGl2aW5nIHJvb20nKVwiKTtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBzZXRJc0dlbmVyYXRpbmcodHJ1ZSk7XG4gICAgc2V0RXJyb3IoXCJcIik7XG4gICAgc2V0U3VnZ2VzdGlvbihcIlwiKTtcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCBlbmhhbmNlZFByb21wdCA9IGVuaGFuY2VQcm9tcHQocHJvbXB0KTtcblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9nZW5lcmF0ZS1pbWFnZScsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7IHByb21wdDogZW5oYW5jZWRQcm9tcHQgfSksXG4gICAgICB9KTtcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ZhaWxlZCB0byBnZW5lcmF0ZSBpbWFnZScpO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuXG4gICAgICBjb25zdCBuZXdJbWFnZTogR2VuZXJhdGVkSW1hZ2UgPSB7XG4gICAgICAgIHVybDogZGF0YS5pbWFnZVVybCxcbiAgICAgICAgcHJvbXB0OiBwcm9tcHQsXG4gICAgICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKSxcbiAgICAgIH07XG5cbiAgICAgIHNldEdlbmVyYXRlZEltYWdlcyhwcmV2ID0+IFtuZXdJbWFnZSwgLi4ucHJldl0pO1xuICAgICAgc2V0UHJvbXB0KFwiXCIpO1xuICAgIH0gY2F0Y2ggKGVycjogYW55KSB7XG4gICAgICAvLyBQYXJzZSBlcnJvciByZXNwb25zZVxuICAgICAgbGV0IGVycm9yTWVzc2FnZSA9IFwiRmFpbGVkIHRvIGdlbmVyYXRlIGltYWdlLiBQbGVhc2UgdHJ5IGFnYWluLlwiO1xuICAgICAgbGV0IHN1Z2dlc3Rpb25NZXNzYWdlID0gXCJcIjtcblxuICAgICAgaWYgKGVyci5yZXNwb25zZT8uZGF0YSkge1xuICAgICAgICBlcnJvck1lc3NhZ2UgPSBlcnIucmVzcG9uc2UuZGF0YS5lcnJvciB8fCBlcnJvck1lc3NhZ2U7XG4gICAgICAgIHN1Z2dlc3Rpb25NZXNzYWdlID0gZXJyLnJlc3BvbnNlLmRhdGEuc3VnZ2VzdGlvbiB8fCBcIlwiO1xuICAgICAgfSBlbHNlIGlmIChlcnIubWVzc2FnZSkge1xuICAgICAgICBlcnJvck1lc3NhZ2UgPSBlcnIubWVzc2FnZTtcbiAgICAgIH1cblxuICAgICAgc2V0RXJyb3IoZXJyb3JNZXNzYWdlKTtcbiAgICAgIHNldFN1Z2dlc3Rpb24oc3VnZ2VzdGlvbk1lc3NhZ2UpO1xuICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGdlbmVyYXRpbmcgaW1hZ2U6XCIsIGVycik7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzR2VuZXJhdGluZyhmYWxzZSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUtleVByZXNzID0gKGU6IFJlYWN0LktleWJvYXJkRXZlbnQpID0+IHtcbiAgICBpZiAoZS5rZXkgPT09ICdFbnRlcicgJiYgIWlzR2VuZXJhdGluZykge1xuICAgICAgZ2VuZXJhdGVJbWFnZSgpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBzYW1wbGVQcm9tcHRzID0gW1xuICAgIFwiTW9kZXJuIG1pbmltYWxpc3Qga2l0Y2hlbiB3aXRoIHdoaXRlIGNhYmluZXRzIGFuZCBtYXJibGUgY291bnRlcnRvcHNcIixcbiAgICBcIkNvenkgYmVkcm9vbSB3aXRoIHdhcm0gbGlnaHRpbmcgYW5kIG5hdHVyYWwgd29vZCBmdXJuaXR1cmVcIixcbiAgICBcIkNvbnRlbXBvcmFyeSBsaXZpbmcgcm9vbSB3aXRoIGxhcmdlIHdpbmRvd3MgYW5kIG5ldXRyYWwgY29sb3JzXCIsXG4gICAgXCJMdXh1cnkgYmF0aHJvb20gd2l0aCBtYXJibGUgdGlsZXMgYW5kIGdvbGQgZml4dHVyZXNcIixcbiAgICBcIlNjYW5kaW5hdmlhbiBkaW5pbmcgcm9vbSB3aXRoIHdvb2RlbiB0YWJsZSBhbmQgcGVuZGFudCBsaWdodHNcIixcbiAgICBcIldhbGstaW4gY2xvc2V0IHdpdGggb3JnYW5pemVkIHN0b3JhZ2UgYW5kIGVsZWdhbnQgbGlnaHRpbmdcIlxuICBdO1xuXG4gIHJldHVybiAoXG4gICAgPG1haW4gY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLXdoaXRlXCI+XG4gICAgICB7LyogTmF2aWdhdGlvbiAqL31cbiAgICAgIDxuYXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgc2hhZG93LXNtIGJvcmRlci1iIGJvcmRlci1mb3Jlc3QtZ3JlZW4tMTAwXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LThcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlciBoLTE2XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XG4gICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvXCIgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZm9yZXN0LWdyZWVuLTYwMFwiPk1vZGVybiBIYXZlbjwvTGluaz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoaWRkZW4gbWQ6YmxvY2tcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtbC0xMCBmbGV4IGl0ZW1zLWJhc2VsaW5lIHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvXCIgY2xhc3NOYW1lPVwidGV4dC1mb3Jlc3QtZ3JlZW4tNjAwIGhvdmVyOnRleHQtZ29sZC01MDAgcHgtMyBweS0yIHJvdW5kZWQtbWQgdGV4dC1zbSBmb250LW1lZGl1bVwiPkhvbWU8L0xpbms+XG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi8jYWJvdXRcIiBjbGFzc05hbWU9XCJ0ZXh0LWZvcmVzdC1ncmVlbi02MDAgaG92ZXI6dGV4dC1nb2xkLTUwMCBweC0zIHB5LTIgcm91bmRlZC1tZCB0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+QWJvdXQ8L0xpbms+XG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi8jc2VydmljZXNcIiBjbGFzc05hbWU9XCJ0ZXh0LWZvcmVzdC1ncmVlbi02MDAgaG92ZXI6dGV4dC1nb2xkLTUwMCBweC0zIHB5LTIgcm91bmRlZC1tZCB0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+U2VydmljZXM8L0xpbms+XG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9nYWxsZXJ5XCIgY2xhc3NOYW1lPVwidGV4dC1mb3Jlc3QtZ3JlZW4tNjAwIGhvdmVyOnRleHQtZ29sZC01MDAgcHgtMyBweS0yIHJvdW5kZWQtbWQgdGV4dC1zbSBmb250LW1lZGl1bVwiPkdhbGxlcnk8L0xpbms+XG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9haS1kZXNpZ25cIiBjbGFzc05hbWU9XCJiZy1nb2xkLTUwMCB0ZXh0LXdoaXRlIHB4LTMgcHktMiByb3VuZGVkLW1kIHRleHQtc20gZm9udC1tZWRpdW1cIj5BSSBEZXNpZ248L0xpbms+XG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi8jY29udGFjdFwiIGNsYXNzTmFtZT1cInRleHQtZm9yZXN0LWdyZWVuLTYwMCBob3Zlcjp0ZXh0LWdvbGQtNTAwIHB4LTMgcHktMiByb3VuZGVkLW1kIHRleHQtc20gZm9udC1tZWRpdW1cIj5Db250YWN0PC9MaW5rPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvbmF2PlxuXG4gICAgICB7LyogSGVybyBTZWN0aW9uICovfVxuICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwicmVsYXRpdmUgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1mb3Jlc3QtZ3JlZW4tNTAgdG8td2hpdGUgcHktMTZcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC00IHNtOnB4LTYgbGc6cHgtOFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTR4bCBtZDp0ZXh0LTV4bCBmb250LWJvbGQgdGV4dC1mb3Jlc3QtZ3JlZW4tNzAwIG1iLTZcIj5cbiAgICAgICAgICAgICAgQUkgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1nb2xkLTUwMFwiPkRlc2lnbiBTdHVkaW88L3NwYW4+XG4gICAgICAgICAgICA8L2gxPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14bCB0ZXh0LWZvcmVzdC1ncmVlbi02MDAgbWItOCBtYXgtdy0zeGwgbXgtYXV0b1wiPlxuICAgICAgICAgICAgICBCcmluZyB5b3VyIGludGVyaW9yIGRlc2lnbiBpZGVhcyB0byBsaWZlIHdpdGggb3VyIEFJLXBvd2VyZWQgZGVzaWduIGdlbmVyYXRvci5cbiAgICAgICAgICAgICAgRGVzY3JpYmUgeW91ciB2aXNpb24gYW5kIHdhdGNoIGl0IHRyYW5zZm9ybSBpbnRvIHN0dW5uaW5nIHZpc3VhbCBjb25jZXB0cy5cbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L3NlY3Rpb24+XG5cbiAgICAgIHsvKiBBSSBEZXNpZ24gR2VuZXJhdG9yIC0gVHdvIENvbHVtbiBMYXlvdXQgKi99XG4gICAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJweS0yMCBiZy13aGl0ZVwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTd4bCBteC1hdXRvIHB4LTQgc206cHgtNiBsZzpweC04XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIGxnOmdyaWQtY29scy0yIGdhcC04XCI+XG5cbiAgICAgICAgICAgIHsvKiBMZWZ0IENvbHVtbiAtIElucHV0IGFuZCBDb250cm9scyAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS04XCI+XG4gICAgICAgICAgICAgIHsvKiBJbnB1dCBTZWN0aW9uICovfVxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWZvcmVzdC1ncmVlbi01MCByb3VuZGVkLWxnIHAtOFwiPlxuICAgICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1mb3Jlc3QtZ3JlZW4tNzAwIG1iLTZcIj5cbiAgICAgICAgICAgICAgICAgIENyZWF0ZSBZb3VyIERlc2lnblxuICAgICAgICAgICAgICAgIDwvaDI+XG5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGh0bWxGb3I9XCJwcm9tcHRcIiBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZm9yZXN0LWdyZWVuLTcwMCBtYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgRGVzY3JpYmUgeW91ciBpbnRlcmlvciBkZXNpZ24gdmlzaW9uXG4gICAgICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDx0ZXh0YXJlYVxuICAgICAgICAgICAgICAgICAgICAgIGlkPVwicHJvbXB0XCJcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17cHJvbXB0fVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0UHJvbXB0KGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICBvbktleVByZXNzPXtoYW5kbGVLZXlQcmVzc31cbiAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cImUuZy4sIE1vZGVybiBraXRjaGVuIHdpdGggd2hpdGUgY2FiaW5ldHMsIG1hcmJsZSBjb3VudGVydG9wcywgYW5kIHN0YWlubGVzcyBzdGVlbCBhcHBsaWFuY2VzLi4uXCJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgcHgtNCBweS0zIGJvcmRlciBib3JkZXItZm9yZXN0LWdyZWVuLTIwMCByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWdvbGQtNTAwIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudCByZXNpemUtbm9uZVwiXG4gICAgICAgICAgICAgICAgICAgICAgcm93cz17Nn1cbiAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNHZW5lcmF0aW5nfVxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgIHtlcnJvciAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yZWQtNjAwIHRleHQtc20gYmctcmVkLTUwIHAtMyByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj57ZXJyb3J9PC9wPlxuICAgICAgICAgICAgICAgICAgICAgIHtzdWdnZXN0aW9uICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTIgdGV4dC1ibHVlLTYwMCBiZy1ibHVlLTUwIHAtMiByb3VuZGVkIGJvcmRlci1sLTQgYm9yZGVyLWJsdWUtNDAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIPCfkqEgPHN0cm9uZz5TdWdnZXN0aW9uOjwvc3Ryb25nPiB7c3VnZ2VzdGlvbn1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17Z2VuZXJhdGVJbWFnZX1cbiAgICAgICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2lzR2VuZXJhdGluZyB8fCAhcHJvbXB0LnRyaW0oKX1cbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJnLWdvbGQtNTAwIHRleHQtd2hpdGUgcHktMyBweC02IHJvdW5kZWQtbGcgZm9udC1zZW1pYm9sZCBob3ZlcjpiZy1nb2xkLTYwMCBkaXNhYmxlZDpiZy1ncmF5LTMwMCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgdHJhbnNpdGlvbiBkdXJhdGlvbi0zMDBcIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICB7aXNHZW5lcmF0aW5nID8gKFxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTUgdy01IGJvcmRlci1iLTIgYm9yZGVyLXdoaXRlIG1yLTJcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIEdlbmVyYXRpbmcgRGVzaWduLi4uXG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgXCJHZW5lcmF0ZSBEZXNpZ25cIlxuICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgIHsvKiBTYW1wbGUgUHJvbXB0cyAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy13aGl0ZSBib3JkZXIgYm9yZGVyLWZvcmVzdC1ncmVlbi0yMDAgcm91bmRlZC1sZyBwLTZcIj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LXNlbWlib2xkIHRleHQtZm9yZXN0LWdyZWVuLTcwMCBtYi00XCI+XG4gICAgICAgICAgICAgICAgICDwn5KhIFRyeSB0aGVzZSBzYW1wbGUgcHJvbXB0czpcbiAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICB7c2FtcGxlUHJvbXB0cy5tYXAoKHNhbXBsZVByb21wdCwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIGtleT17aW5kZXh9XG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0UHJvbXB0KHNhbXBsZVByb21wdCl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIHRleHQtbGVmdCBwLTMgYmctZm9yZXN0LWdyZWVuLTUwIGJvcmRlciBib3JkZXItZm9yZXN0LWdyZWVuLTIwMCByb3VuZGVkLWxnIGhvdmVyOmJvcmRlci1nb2xkLTUwMCBob3ZlcjpiZy1nb2xkLTUwIHRyYW5zaXRpb24gZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17aXNHZW5lcmF0aW5nfVxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWZvcmVzdC1ncmVlbi02MDBcIj57c2FtcGxlUHJvbXB0fTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIFRpcHMgU2VjdGlvbiAqL31cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1mb3Jlc3QtZ3JlZW4tNjAwIHJvdW5kZWQtbGcgcC02IHRleHQtd2hpdGVcIj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LWJvbGQgbWItNFwiPvCfjq8gVGlwcyBmb3IgQmV0dGVyIFJlc3VsdHM8L2gzPlxuICAgICAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJzcGFjZS15LTIgdGV4dC1mb3Jlc3QtZ3JlZW4tMTAwIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgIDxsaT7igKIgQmUgc3BlY2lmaWMgYWJvdXQgcm9vbSB0eXBlcyAoa2l0Y2hlbiwgYmVkcm9vbSwgbGl2aW5nIHJvb20sIGV0Yy4pPC9saT5cbiAgICAgICAgICAgICAgICAgIDxsaT7igKIgSW5jbHVkZSBzdHlsZSBwcmVmZXJlbmNlcyAobW9kZXJuLCB0cmFkaXRpb25hbCwgbWluaW1hbGlzdCwgbHV4dXJ5KTwvbGk+XG4gICAgICAgICAgICAgICAgICA8bGk+4oCiIE1lbnRpb24gY29sb3JzLCBtYXRlcmlhbHMsIGFuZCBsaWdodGluZyBwcmVmZXJlbmNlczwvbGk+XG4gICAgICAgICAgICAgICAgICA8bGk+4oCiIERlc2NyaWJlIHRoZSBtb29kIHlvdSB3YW50IHRvIGNyZWF0ZSAoY296eSwgZWxlZ2FudCwgYnJpZ2h0LCBldGMuKTwvbGk+XG4gICAgICAgICAgICAgICAgICA8bGk+4oCiIEluY2x1ZGUgZnVybml0dXJlIGFuZCBkZWNvciBlbGVtZW50cyB5b3UgZW52aXNpb248L2xpPlxuICAgICAgICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIHsvKiBSaWdodCBDb2x1bW4gLSBHZW5lcmF0ZWQgSW1hZ2VzICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZy1mb3Jlc3QtZ3JlZW4tNTAgcm91bmRlZC1sZyBwLTZcIj5cbiAgICAgICAgICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZm9yZXN0LWdyZWVuLTcwMCBtYi02XCI+XG4gICAgICAgICAgICAgICAgICB7Z2VuZXJhdGVkSW1hZ2VzLmxlbmd0aCA+IDAgPyAnWW91ciBHZW5lcmF0ZWQgRGVzaWducycgOiAnR2VuZXJhdGVkIERlc2lnbnMgV2lsbCBBcHBlYXIgSGVyZSd9XG4gICAgICAgICAgICAgICAgPC9oMz5cblxuICAgICAgICAgICAgICAgIHtnZW5lcmF0ZWRJbWFnZXMubGVuZ3RoID09PSAwID8gKFxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS0xMlwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtNnhsIG1iLTRcIj7wn46oPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZm9yZXN0LWdyZWVuLTYwMCBtYi00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgU3RhcnQgYnkgZGVzY3JpYmluZyB5b3VyIGludGVyaW9yIGRlc2lnbiB2aXNpb24gaW4gdGhlIGxlZnQgcGFuZWwuXG4gICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWZvcmVzdC1ncmVlbi01MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICBZb3VyIEFJLWdlbmVyYXRlZCBkZXNpZ25zIHdpbGwgYXBwZWFyIGhlcmUgb25jZSB5b3UgY2xpY2sgXCJHZW5lcmF0ZSBEZXNpZ25cIi5cbiAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02IG1heC1oLVs4MDBweF0gb3ZlcmZsb3cteS1hdXRvXCI+XG4gICAgICAgICAgICAgICAgICAgIHtnZW5lcmF0ZWRJbWFnZXMubWFwKChpbWFnZSwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93LWxnIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBoLTY0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxJbWFnZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNyYz17aW1hZ2UudXJsfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFsdD17YEdlbmVyYXRlZCBkZXNpZ246ICR7aW1hZ2UucHJvbXB0fWB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZmlsbFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm9iamVjdC1jb3ZlclwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC00XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZm9yZXN0LWdyZWVuLTYwMCB0ZXh0LXNtIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Ryb25nPlByb21wdDo8L3N0cm9uZz4ge2ltYWdlLnByb21wdH1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwIHRleHQteHNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBHZW5lcmF0ZWQgb24ge25ldyBEYXRlKGltYWdlLnRpbWVzdGFtcCkudG9Mb2NhbGVTdHJpbmcoKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm10LTMgZmxleCBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGxpbmsgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdhJyk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxpbmsuaHJlZiA9IGltYWdlLnVybDtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbGluay5kb3dubG9hZCA9IGBtb2Rlcm4taGF2ZW4tZGVzaWduLSR7aW1hZ2UudGltZXN0YW1wfS5wbmdgO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsaW5rLmNsaWNrKCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC14cyBiZy1nb2xkLTUwMCB0ZXh0LXdoaXRlIHB4LTMgcHktMSByb3VuZGVkIGhvdmVyOmJnLWdvbGQtNjAwIHRyYW5zaXRpb24gZHVyYXRpb24tMjAwXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBEb3dubG9hZFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFByb21wdChpbWFnZS5wcm9tcHQpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC14cyBiZy1mb3Jlc3QtZ3JlZW4tNjAwIHRleHQtd2hpdGUgcHgtMyBweS0xIHJvdW5kZWQgaG92ZXI6YmctZm9yZXN0LWdyZWVuLTcwMCB0cmFuc2l0aW9uIGR1cmF0aW9uLTIwMFwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgVXNlIFByb21wdFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9zZWN0aW9uPlxuXG4gICAgICB7LyogRm9vdGVyICovfVxuICAgICAgPGZvb3RlciBjbGFzc05hbWU9XCJiZy1mb3Jlc3QtZ3JlZW4tODAwIHRleHQtd2hpdGUgcHktMTJcIj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy03eGwgbXgtYXV0byBweC00IHNtOnB4LTYgbGc6cHgtOFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtNCBnYXAtOFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb2wtc3Bhbi0xIG1kOmNvbC1zcGFuLTJcIj5cbiAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdvbGQtNDAwIG1iLTRcIj5Nb2Rlcm4gSGF2ZW48L2gzPlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWZvcmVzdC1ncmVlbi0yMDAgbWItNFwiPlxuICAgICAgICAgICAgICAgIENyZWF0aW5nIGJlYXV0aWZ1bCwgZnVuY3Rpb25hbCBzcGFjZXMgdGhhdCByZWZsZWN0IHlvdXIgdW5pcXVlIHN0eWxlIGFuZCBlbmhhbmNlIHlvdXIgbGlmZXN0eWxlLlxuICAgICAgICAgICAgICAgIFlvdXIgZHJlYW0gaG9tZSBpcyBqdXN0IGEgY29uc3VsdGF0aW9uIGF3YXkuXG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtNFwiPlxuICAgICAgICAgICAgICAgIDxhIGhyZWY9XCIjXCIgY2xhc3NOYW1lPVwidGV4dC1nb2xkLTQwMCBob3Zlcjp0ZXh0LWdvbGQtMzAwXCI+8J+TmDwvYT5cbiAgICAgICAgICAgICAgICA8YSBocmVmPVwiI1wiIGNsYXNzTmFtZT1cInRleHQtZ29sZC00MDAgaG92ZXI6dGV4dC1nb2xkLTMwMFwiPvCfk7c8L2E+XG4gICAgICAgICAgICAgICAgPGEgaHJlZj1cIiNcIiBjbGFzc05hbWU9XCJ0ZXh0LWdvbGQtNDAwIGhvdmVyOnRleHQtZ29sZC0zMDBcIj7wn5CmPC9hPlxuICAgICAgICAgICAgICAgIDxhIGhyZWY9XCIjXCIgY2xhc3NOYW1lPVwidGV4dC1nb2xkLTQwMCBob3Zlcjp0ZXh0LWdvbGQtMzAwXCI+8J+SvDwvYT5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1nb2xkLTQwMCBtYi00XCI+UXVpY2sgTGlua3M8L2g0PlxuICAgICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwic3BhY2UteS0yIHRleHQtZm9yZXN0LWdyZWVuLTIwMFwiPlxuICAgICAgICAgICAgICAgIDxsaT48TGluayBocmVmPVwiL1wiIGNsYXNzTmFtZT1cImhvdmVyOnRleHQtZ29sZC00MDBcIj5Ib21lPC9MaW5rPjwvbGk+XG4gICAgICAgICAgICAgICAgPGxpPjxMaW5rIGhyZWY9XCIvI2Fib3V0XCIgY2xhc3NOYW1lPVwiaG92ZXI6dGV4dC1nb2xkLTQwMFwiPkFib3V0PC9MaW5rPjwvbGk+XG4gICAgICAgICAgICAgICAgPGxpPjxMaW5rIGhyZWY9XCIvI3NlcnZpY2VzXCIgY2xhc3NOYW1lPVwiaG92ZXI6dGV4dC1nb2xkLTQwMFwiPlNlcnZpY2VzPC9MaW5rPjwvbGk+XG4gICAgICAgICAgICAgICAgPGxpPjxMaW5rIGhyZWY9XCIvZ2FsbGVyeVwiIGNsYXNzTmFtZT1cImhvdmVyOnRleHQtZ29sZC00MDBcIj5HYWxsZXJ5PC9MaW5rPjwvbGk+XG4gICAgICAgICAgICAgICAgPGxpPjxMaW5rIGhyZWY9XCIvYWktZGVzaWduXCIgY2xhc3NOYW1lPVwiaG92ZXI6dGV4dC1nb2xkLTQwMFwiPkFJIERlc2lnbjwvTGluaz48L2xpPlxuICAgICAgICAgICAgICAgIDxsaT48TGluayBocmVmPVwiLyNjb250YWN0XCIgY2xhc3NOYW1lPVwiaG92ZXI6dGV4dC1nb2xkLTQwMFwiPkNvbnRhY3Q8L0xpbms+PC9saT5cbiAgICAgICAgICAgICAgPC91bD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWdvbGQtNDAwIG1iLTRcIj5BSSBGZWF0dXJlczwvaDQ+XG4gICAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJzcGFjZS15LTIgdGV4dC1mb3Jlc3QtZ3JlZW4tMjAwXCI+XG4gICAgICAgICAgICAgICAgPGxpPkludGVyaW9yIERlc2lnbiBHZW5lcmF0aW9uPC9saT5cbiAgICAgICAgICAgICAgICA8bGk+Um9vbSBWaXN1YWxpemF0aW9uPC9saT5cbiAgICAgICAgICAgICAgICA8bGk+U3R5bGUgRXhwbG9yYXRpb248L2xpPlxuICAgICAgICAgICAgICAgIDxsaT5EZXNpZ24gSW5zcGlyYXRpb248L2xpPlxuICAgICAgICAgICAgICAgIDxsaT5DdXN0b20gQ29uY2VwdHM8L2xpPlxuICAgICAgICAgICAgICA8L3VsPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJib3JkZXItdCBib3JkZXItZm9yZXN0LWdyZWVuLTcwMCBtdC04IHB0LTggdGV4dC1jZW50ZXIgdGV4dC1mb3Jlc3QtZ3JlZW4tMjAwXCI+XG4gICAgICAgICAgICA8cD4mY29weTsgMjAyNCBNb2Rlcm4gSGF2ZW4gSW50ZXJpb3IgRGVzaWduLiBBbGwgcmlnaHRzIHJlc2VydmVkLjwvcD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Zvb3Rlcj5cbiAgICA8L21haW4+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiSW1hZ2UiLCJMaW5rIiwidXNlU3RhdGUiLCJBSURlc2lnbiIsInByb21wdCIsInNldFByb21wdCIsImlzR2VuZXJhdGluZyIsInNldElzR2VuZXJhdGluZyIsImdlbmVyYXRlZEltYWdlcyIsInNldEdlbmVyYXRlZEltYWdlcyIsImVycm9yIiwic2V0RXJyb3IiLCJzdWdnZXN0aW9uIiwic2V0U3VnZ2VzdGlvbiIsImludGVyaW9yS2V5d29yZHMiLCJ2YWxpZGF0ZVByb21wdCIsImlucHV0UHJvbXB0IiwibG93ZXJQcm9tcHQiLCJ0b0xvd2VyQ2FzZSIsInNvbWUiLCJrZXl3b3JkIiwiaW5jbHVkZXMiLCJlbmhhbmNlUHJvbXB0IiwidXNlclByb21wdCIsImJhc2VDb250ZXh0IiwiZ2VuZXJhdGVJbWFnZSIsInRyaW0iLCJlbmhhbmNlZFByb21wdCIsInJlc3BvbnNlIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJvayIsIkVycm9yIiwiZGF0YSIsImpzb24iLCJuZXdJbWFnZSIsInVybCIsImltYWdlVXJsIiwidGltZXN0YW1wIiwiRGF0ZSIsIm5vdyIsInByZXYiLCJlcnIiLCJlcnJvck1lc3NhZ2UiLCJzdWdnZXN0aW9uTWVzc2FnZSIsIm1lc3NhZ2UiLCJjb25zb2xlIiwiaGFuZGxlS2V5UHJlc3MiLCJlIiwia2V5Iiwic2FtcGxlUHJvbXB0cyIsIm1haW4iLCJjbGFzc05hbWUiLCJuYXYiLCJkaXYiLCJocmVmIiwic2VjdGlvbiIsImgxIiwic3BhbiIsInAiLCJoMiIsImxhYmVsIiwiaHRtbEZvciIsInRleHRhcmVhIiwiaWQiLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwidGFyZ2V0Iiwib25LZXlQcmVzcyIsInBsYWNlaG9sZGVyIiwicm93cyIsImRpc2FibGVkIiwic3Ryb25nIiwiYnV0dG9uIiwib25DbGljayIsImgzIiwibWFwIiwic2FtcGxlUHJvbXB0IiwiaW5kZXgiLCJ1bCIsImxpIiwibGVuZ3RoIiwiaW1hZ2UiLCJzcmMiLCJhbHQiLCJmaWxsIiwidG9Mb2NhbGVTdHJpbmciLCJsaW5rIiwiZG9jdW1lbnQiLCJjcmVhdGVFbGVtZW50IiwiZG93bmxvYWQiLCJjbGljayIsImZvb3RlciIsImEiLCJoNCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ai-design/page.tsx\n"));

/***/ })

});