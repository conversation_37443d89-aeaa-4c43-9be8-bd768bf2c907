"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/ai-design/page",{

/***/ "(app-pages-browser)/./src/app/ai-design/page.tsx":
/*!************************************!*\
  !*** ./src/app/ai-design/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AIDesign; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction AIDesign() {\n    _s();\n    const [prompt, setPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [generatedContent, setGeneratedContent] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [suggestion, setSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [uploadedImage, setUploadedImage] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    // Interior design keywords to validate prompts\n    const interiorKeywords = [\n        \"kitchen\",\n        \"bedroom\",\n        \"living room\",\n        \"bathroom\",\n        \"dining room\",\n        \"office\",\n        \"closet\",\n        \"interior\",\n        \"home\",\n        \"house\",\n        \"apartment\",\n        \"room\",\n        \"space\",\n        \"design\",\n        \"furniture\",\n        \"cabinet\",\n        \"counter\",\n        \"sofa\",\n        \"chair\",\n        \"table\",\n        \"bed\",\n        \"shelf\",\n        \"storage\",\n        \"modern\",\n        \"contemporary\",\n        \"traditional\",\n        \"minimalist\",\n        \"luxury\",\n        \"cozy\",\n        \"lighting\",\n        \"window\",\n        \"wall\",\n        \"floor\",\n        \"ceiling\",\n        \"decor\",\n        \"renovation\"\n    ];\n    const validatePrompt = (inputPrompt)=>{\n        const lowerPrompt = inputPrompt.toLowerCase();\n        return interiorKeywords.some((keyword)=>lowerPrompt.includes(keyword));\n    };\n    const enhancePrompt = (userPrompt)=>{\n        // Add interior design context to ensure relevant results\n        const baseContext = \"Interior design, home decor, architectural photography, professional lighting, high quality, detailed, \";\n        // Check if prompt already contains interior context\n        const lowerPrompt = userPrompt.toLowerCase();\n        if (lowerPrompt.includes(\"interior\") || lowerPrompt.includes(\"room\") || lowerPrompt.includes(\"kitchen\") || lowerPrompt.includes(\"bedroom\")) {\n            return \"\".concat(baseContext).concat(userPrompt);\n        }\n        return \"\".concat(baseContext, \"modern \").concat(userPrompt, \" interior design\");\n    };\n    const handleImageUpload = (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // Validate file type\n            if (!file.type.startsWith(\"image/\")) {\n                setError(\"Please upload a valid image file\");\n                return;\n            }\n            // Validate file size (max 5MB)\n            if (file.size > 5 * 1024 * 1024) {\n                setError(\"Image size should be less than 5MB\");\n                return;\n            }\n            const reader = new FileReader();\n            reader.onload = (e)=>{\n                var _e_target;\n                const preview = (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result;\n                setUploadedImage({\n                    file,\n                    preview\n                });\n                setError(\"\");\n            };\n            reader.readAsDataURL(file);\n        }\n    };\n    const removeUploadedImage = ()=>{\n        setUploadedImage(null);\n    };\n    const generateImage = async ()=>{\n        if (!prompt.trim()) {\n            setError(\"Please enter a design description\");\n            return;\n        }\n        if (!validatePrompt(prompt)) {\n            setError(\"Please describe an interior design or home-related concept (e.g., 'modern kitchen', 'cozy bedroom', 'minimalist living room')\");\n            return;\n        }\n        setIsGenerating(true);\n        setError(\"\");\n        setSuggestion(\"\");\n        try {\n            const enhancedPrompt = enhancePrompt(prompt);\n            const response = await fetch(\"/api/generate-image\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    prompt: enhancedPrompt,\n                    uploadedImage: uploadedImage === null || uploadedImage === void 0 ? void 0 : uploadedImage.preview\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to generate image\");\n            }\n            const data = await response.json();\n            const newContent = {\n                url: data.imageUrl,\n                textDescription: data.textDescription,\n                prompt: prompt,\n                timestamp: Date.now(),\n                message: data.message\n            };\n            setGeneratedContent((prev)=>[\n                    newContent,\n                    ...prev\n                ]);\n            setPrompt(\"\");\n        } catch (err) {\n            var _err_response;\n            // Parse error response\n            let errorMessage = \"Failed to generate image. Please try again.\";\n            let suggestionMessage = \"\";\n            if ((_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.data) {\n                errorMessage = err.response.data.error || errorMessage;\n                suggestionMessage = err.response.data.suggestion || \"\";\n            } else if (err.message) {\n                errorMessage = err.message;\n            }\n            setError(errorMessage);\n            setSuggestion(suggestionMessage);\n            console.error(\"Error generating image:\", err);\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !isGenerating) {\n            generateImage();\n        }\n    };\n    const samplePrompts = [\n        \"Modern minimalist kitchen with white cabinets and marble countertops\",\n        \"Cozy bedroom with warm lighting and natural wood furniture\",\n        \"Contemporary living room with large windows and neutral colors\",\n        \"Luxury bathroom with marble tiles and gold fixtures\",\n        \"Scandinavian dining room with wooden table and pendant lights\",\n        \"Walk-in closet with organized storage and elegant lighting\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"bg-white shadow-sm border-b border-forest-green-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/\",\n                                    className: \"text-2xl font-bold text-forest-green-600\",\n                                    children: \"Modern Haven\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:block\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-10 flex items-baseline space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"Home\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/#about\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"About\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/#services\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"Services\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/gallery\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"Gallery\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/ai-design\",\n                                            className: \"bg-gold-500 text-white px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"AI Design\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/#contact\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"Contact\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative bg-gradient-to-br from-forest-green-50 to-white py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-5xl font-bold text-forest-green-700 mb-6\",\n                                children: [\n                                    \"AI \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gold-500\",\n                                        children: \"Design Studio\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 18\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-forest-green-600 mb-8 max-w-3xl mx-auto\",\n                                children: \"Bring your interior design ideas to life with our advanced AI technology. Describe your vision and upload reference images to get stunning design concepts.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-forest-green-50 rounded-lg p-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold text-forest-green-700 mb-6\",\n                                                children: \"Create Your Design\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"prompt\",\n                                                                className: \"block text-sm font-medium text-forest-green-700 mb-2\",\n                                                                children: \"Describe your interior design vision\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                lineNumber: 216,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                id: \"prompt\",\n                                                                value: prompt,\n                                                                onChange: (e)=>setPrompt(e.target.value),\n                                                                onKeyPress: handleKeyPress,\n                                                                placeholder: \"e.g., Modern kitchen with white cabinets, marble countertops, and stainless steel appliances...\",\n                                                                className: \"w-full px-4 py-3 border border-forest-green-200 rounded-lg focus:ring-2 focus:ring-gold-500 focus:border-transparent resize-none text-forest-green-800 placeholder-forest-green-400 bg-white\",\n                                                                rows: 6,\n                                                                disabled: isGenerating\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                lineNumber: 219,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-red-600 text-sm bg-red-50 p-3 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: error\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                lineNumber: 233,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            suggestion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"mt-2 text-blue-600 bg-blue-50 p-2 rounded border-l-4 border-blue-400\",\n                                                                children: [\n                                                                    \"\\uD83D\\uDCA1 \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Suggestion:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                        lineNumber: 236,\n                                                                        columnNumber: 30\n                                                                    }, this),\n                                                                    \" \",\n                                                                    suggestion\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                lineNumber: 235,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: generateImage,\n                                                        disabled: isGenerating || !prompt.trim(),\n                                                        className: \"w-full bg-gold-500 text-white py-3 px-6 rounded-lg font-semibold hover:bg-gold-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition duration-300\",\n                                                        children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                    lineNumber: 249,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Generating Design...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                            lineNumber: 248,\n                                                            columnNumber: 23\n                                                        }, this) : \"Generate Design\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white border border-forest-green-200 rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-4\",\n                                                children: \"\\uD83D\\uDDBC️ Upload Reference Image (Optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 17\n                                            }, this),\n                                            !uploadedImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-2 border-dashed border-forest-green-300 rounded-lg p-6 text-center hover:border-gold-500 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"file\",\n                                                        accept: \"image/*\",\n                                                        onChange: handleImageUpload,\n                                                        className: \"hidden\",\n                                                        id: \"image-upload\",\n                                                        disabled: isGenerating\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"image-upload\",\n                                                        className: \"cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-4xl mb-2\",\n                                                                children: \"\\uD83D\\uDCF7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                lineNumber: 276,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-forest-green-600 mb-2\",\n                                                                children: \"Click to upload an image\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                lineNumber: 277,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-forest-green-500\",\n                                                                children: \"Upload a room photo for AI to redesign or use as inspiration\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                lineNumber: 278,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400 mt-2\",\n                                                                children: \"Supports JPG, PNG (max 5MB)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                lineNumber: 281,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative h-48 rounded-lg overflow-hidden\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                            src: uploadedImage.preview,\n                                                            alt: \"Uploaded reference\",\n                                                            fill: true,\n                                                            className: \"object-cover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                            lineNumber: 289,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: removeUploadedImage,\n                                                        className: \"absolute top-2 right-2 bg-red-500 text-white rounded-full w-8 h-8 flex items-center justify-center hover:bg-red-600 transition-colors\",\n                                                        disabled: isGenerating,\n                                                        children: \"✕\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-forest-green-600 mt-2\",\n                                                        children: \"Reference image uploaded. The AI will use this for inspiration.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white border border-forest-green-200 rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-4\",\n                                                children: \"\\uD83D\\uDCA1 Try these sample prompts:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 312,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: samplePrompts.map((samplePrompt, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setPrompt(samplePrompt),\n                                                        className: \"w-full text-left p-3 bg-forest-green-50 border border-forest-green-200 rounded-lg hover:border-gold-500 hover:bg-gold-50 transition duration-200\",\n                                                        disabled: isGenerating,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-forest-green-600\",\n                                                            children: samplePrompt\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                            lineNumber: 323,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, index, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-forest-green-600 rounded-lg p-6 text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold mb-4\",\n                                                children: \"\\uD83C\\uDFAF Tips for Better Results\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-2 text-forest-green-100 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Be specific about room types (kitchen, bedroom, living room, etc.)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 333,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Include style preferences (modern, traditional, minimalist, luxury)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Mention colors, materials, and lighting preferences\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Describe the mood you want to create (cozy, elegant, bright, etc.)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Include furniture and decor elements you envision\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Upload reference images to help the AI understand your vision\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-forest-green-50 rounded-lg p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-forest-green-700 mb-6\",\n                                            children: generatedContent.length > 0 ? \"Your Generated Designs\" : \"Generated Designs Will Appear Here\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 17\n                                        }, this),\n                                        generatedContent.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-6xl mb-4\",\n                                                    children: \"\\uD83C\\uDFA8\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-forest-green-600 mb-4\",\n                                                    children: \"Start by describing your interior design vision in the left panel.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-forest-green-500\",\n                                                    children: 'Your AI-generated designs will appear here once you click \"Generate Design\".'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6 h-[500px] overflow-y-auto\",\n                                            children: generatedContent.map((content, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white rounded-lg shadow-lg overflow-hidden\",\n                                                    children: [\n                                                        content.url ? // Display image if available\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative h-64\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                                src: content.url,\n                                                                alt: \"Generated design: \".concat(content.prompt),\n                                                                fill: true,\n                                                                className: \"object-cover\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                lineNumber: 367,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 27\n                                                        }, this) : // Display text description from Gemini\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-6 bg-gradient-to-br from-gold-50 to-forest-green-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-4xl mb-4 text-center\",\n                                                                    children: \"\\uD83C\\uDFE0✨\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                    lineNumber: 377,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-lg font-semibold text-forest-green-700 mb-3\",\n                                                                    children: \"AI Design Concept\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                    lineNumber: 378,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-forest-green-600 text-sm leading-relaxed\",\n                                                                    children: content.textDescription\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                    lineNumber: 381,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                content.message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-3 p-3 bg-blue-50 border-l-4 border-blue-400 rounded\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-blue-700 text-sm\",\n                                                                        children: [\n                                                                            \"\\uD83D\\uDCA1 \",\n                                                                            content.message\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                        lineNumber: 386,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                    lineNumber: 385,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-forest-green-600 text-sm mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"Prompt:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                            lineNumber: 395,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \" \",\n                                                                        content.prompt\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                    lineNumber: 394,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-500 text-xs mb-3\",\n                                                                    children: [\n                                                                        \"Generated on \",\n                                                                        new Date(content.timestamp).toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                    lineNumber: 397,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex space-x-2\",\n                                                                    children: [\n                                                                        content.url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>{\n                                                                                const link = document.createElement(\"a\");\n                                                                                link.href = content.url;\n                                                                                link.download = \"modern-haven-design-\".concat(content.timestamp, \".png\");\n                                                                                link.click();\n                                                                            },\n                                                                            className: \"text-xs bg-gold-500 text-white px-3 py-1 rounded hover:bg-gold-600 transition duration-200\",\n                                                                            children: \"Download Image\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                            lineNumber: 402,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        content.textDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>{\n                                                                                navigator.clipboard.writeText(content.textDescription);\n                                                                                alert(\"Description copied to clipboard!\");\n                                                                            },\n                                                                            className: \"text-xs bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600 transition duration-200\",\n                                                                            children: \"Copy Description\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                            lineNumber: 415,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>setPrompt(content.prompt),\n                                                                            className: \"text-xs bg-forest-green-600 text-white px-3 py-1 rounded hover:bg-forest-green-700 transition duration-200\",\n                                                                            children: \"Use Prompt\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                            lineNumber: 425,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                    lineNumber: 400,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                    lineNumber: 203,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                lineNumber: 202,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-forest-green-800 text-white py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-1 md:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-gold-400 mb-4\",\n                                            children: \"Modern Haven\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 448,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-forest-green-200 mb-4\",\n                                            children: \"Creating beautiful, functional spaces that reflect your unique style and enhance your lifestyle. Your dream home is just a consultation away.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 449,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gold-400 hover:text-gold-300\",\n                                                    children: \"\\uD83D\\uDCD8\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 454,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gold-400 hover:text-gold-300\",\n                                                    children: \"\\uD83D\\uDCF7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gold-400 hover:text-gold-300\",\n                                                    children: \"\\uD83D\\uDC26\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 456,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gold-400 hover:text-gold-300\",\n                                                    children: \"\\uD83D\\uDCBC\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 453,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                    lineNumber: 447,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold text-gold-400 mb-4\",\n                                            children: \"Quick Links\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-forest-green-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"Home\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 463,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/#about\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"About\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/#services\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"Services\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 465,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/gallery\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"Gallery\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/ai-design\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"AI Design\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 467,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/#contact\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"Contact\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 468,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                    lineNumber: 460,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold text-gold-400 mb-4\",\n                                            children: \"AI Features\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-forest-green-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Interior Design Generation\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 474,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Room Visualization\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Style Exploration\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Design Inspiration\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Custom Concepts\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                            lineNumber: 446,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-forest-green-700 mt-8 pt-8 text-center text-forest-green-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"\\xa9 2024 Modern Haven Interior Design. All rights reserved.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                lineNumber: 483,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                            lineNumber: 482,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                    lineNumber: 445,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                lineNumber: 444,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n        lineNumber: 164,\n        columnNumber: 5\n    }, this);\n}\n_s(AIDesign, \"V9MX+/BdIKtjWl9dtpMff2sffEM=\");\n_c = AIDesign;\nvar _c;\n$RefreshReg$(_c, \"AIDesign\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ai-design/page.tsx\n"));

/***/ })

});