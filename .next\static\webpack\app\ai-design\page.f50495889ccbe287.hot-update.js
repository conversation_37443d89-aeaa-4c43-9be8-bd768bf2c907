"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/ai-design/page",{

/***/ "(app-pages-browser)/./src/app/ai-design/page.tsx":
/*!************************************!*\
  !*** ./src/app/ai-design/page.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AIDesign; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction AIDesign() {\n    _s();\n    const [prompt, setPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [isGenerating, setIsGenerating] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    const [generatedContent, setGeneratedContent] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)([]);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [suggestion, setSuggestion] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(\"\");\n    const [uploadedImage, setUploadedImage] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    // Interior design keywords to validate prompts\n    const interiorKeywords = [\n        \"kitchen\",\n        \"bedroom\",\n        \"living room\",\n        \"bathroom\",\n        \"dining room\",\n        \"office\",\n        \"closet\",\n        \"interior\",\n        \"home\",\n        \"house\",\n        \"apartment\",\n        \"room\",\n        \"space\",\n        \"design\",\n        \"furniture\",\n        \"cabinet\",\n        \"counter\",\n        \"sofa\",\n        \"chair\",\n        \"table\",\n        \"bed\",\n        \"shelf\",\n        \"storage\",\n        \"modern\",\n        \"contemporary\",\n        \"traditional\",\n        \"minimalist\",\n        \"luxury\",\n        \"cozy\",\n        \"lighting\",\n        \"window\",\n        \"wall\",\n        \"floor\",\n        \"ceiling\",\n        \"decor\",\n        \"renovation\"\n    ];\n    const validatePrompt = (inputPrompt)=>{\n        const lowerPrompt = inputPrompt.toLowerCase();\n        return interiorKeywords.some((keyword)=>lowerPrompt.includes(keyword));\n    };\n    const enhancePrompt = (userPrompt)=>{\n        // Add interior design context to ensure relevant results\n        const baseContext = \"Interior design, home decor, architectural photography, professional lighting, high quality, detailed, \";\n        // Check if prompt already contains interior context\n        const lowerPrompt = userPrompt.toLowerCase();\n        if (lowerPrompt.includes(\"interior\") || lowerPrompt.includes(\"room\") || lowerPrompt.includes(\"kitchen\") || lowerPrompt.includes(\"bedroom\")) {\n            return \"\".concat(baseContext).concat(userPrompt);\n        }\n        return \"\".concat(baseContext, \"modern \").concat(userPrompt, \" interior design\");\n    };\n    const handleImageUpload = (event)=>{\n        var _event_target_files;\n        const file = (_event_target_files = event.target.files) === null || _event_target_files === void 0 ? void 0 : _event_target_files[0];\n        if (file) {\n            // Validate file type\n            if (!file.type.startsWith(\"image/\")) {\n                setError(\"Please upload a valid image file\");\n                return;\n            }\n            // Validate file size (max 5MB)\n            if (file.size > 5 * 1024 * 1024) {\n                setError(\"Image size should be less than 5MB\");\n                return;\n            }\n            const reader = new FileReader();\n            reader.onload = (e)=>{\n                var _e_target;\n                const preview = (_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result;\n                setUploadedImage({\n                    file,\n                    preview\n                });\n                setError(\"\");\n            };\n            reader.readAsDataURL(file);\n        }\n    };\n    const removeUploadedImage = ()=>{\n        setUploadedImage(null);\n    };\n    const generateImage = async ()=>{\n        if (!prompt.trim()) {\n            setError(\"Please enter a design description\");\n            return;\n        }\n        if (!validatePrompt(prompt)) {\n            setError(\"Please describe an interior design or home-related concept (e.g., 'modern kitchen', 'cozy bedroom', 'minimalist living room')\");\n            return;\n        }\n        setIsGenerating(true);\n        setError(\"\");\n        setSuggestion(\"\");\n        try {\n            const enhancedPrompt = enhancePrompt(prompt);\n            const response = await fetch(\"/api/generate-image\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    prompt: enhancedPrompt,\n                    uploadedImage: uploadedImage === null || uploadedImage === void 0 ? void 0 : uploadedImage.preview\n                })\n            });\n            if (!response.ok) {\n                throw new Error(\"Failed to generate image\");\n            }\n            const data = await response.json();\n            const newContent = {\n                url: data.imageUrl,\n                textDescription: data.textDescription,\n                prompt: prompt,\n                timestamp: Date.now(),\n                message: data.message\n            };\n            setGeneratedContent((prev)=>[\n                    newContent,\n                    ...prev\n                ]);\n            setPrompt(\"\");\n        } catch (err) {\n            var _err_response;\n            // Parse error response\n            let errorMessage = \"Failed to generate image. Please try again.\";\n            let suggestionMessage = \"\";\n            if ((_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.data) {\n                errorMessage = err.response.data.error || errorMessage;\n                suggestionMessage = err.response.data.suggestion || \"\";\n            } else if (err.message) {\n                errorMessage = err.message;\n            }\n            setError(errorMessage);\n            setSuggestion(suggestionMessage);\n            console.error(\"Error generating image:\", err);\n        } finally{\n            setIsGenerating(false);\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === \"Enter\" && !isGenerating) {\n            generateImage();\n        }\n    };\n    const samplePrompts = [\n        \"Modern minimalist kitchen with white cabinets and marble countertops\",\n        \"Cozy bedroom with warm lighting and natural wood furniture\",\n        \"Contemporary living room with large windows and neutral colors\",\n        \"Luxury bathroom with marble tiles and gold fixtures\",\n        \"Scandinavian dining room with wooden table and pendant lights\",\n        \"Walk-in closet with organized storage and elegant lighting\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"bg-white shadow-sm border-b border-forest-green-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            src: \"/images/modern-haven-logo.png\",\n                                            alt: \"Modern Haven Logo\",\n                                            width: 120,\n                                            height: 40,\n                                            className: \"h-10 w-auto\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/\",\n                                        className: \"text-2xl font-bold\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Modern\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gold-500\",\n                                                children: \"Haven\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:block\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-10 flex items-baseline space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"Home\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/#about\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"About\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/#services\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"Services\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 188,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/gallery\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"Gallery\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/ai-design\",\n                                            className: \"bg-gold-500 text-white px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"AI Design\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/#contact\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"Contact\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative bg-gradient-to-br from-forest-green-50 to-white py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-5xl font-bold text-forest-green-700 mb-6\",\n                                children: [\n                                    \"AI \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gold-500\",\n                                        children: \"Design Studio\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 18\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-forest-green-600 mb-8 max-w-3xl mx-auto\",\n                                children: \"Bring your interior design ideas to life with our advanced AI technology. Describe your vision and upload reference images to get stunning design concepts.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                lineNumber: 199,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-forest-green-50 rounded-lg p-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-bold text-forest-green-700 mb-6\",\n                                                children: \"Create Your Design\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"prompt\",\n                                                                className: \"block text-sm font-medium text-forest-green-700 mb-2\",\n                                                                children: \"Describe your interior design vision\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                lineNumber: 228,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                id: \"prompt\",\n                                                                value: prompt,\n                                                                onChange: (e)=>setPrompt(e.target.value),\n                                                                onKeyPress: handleKeyPress,\n                                                                placeholder: \"e.g., Modern kitchen with white cabinets, marble countertops, and stainless steel appliances...\",\n                                                                className: \"w-full px-4 py-3 border border-forest-green-200 rounded-lg focus:ring-2 focus:ring-gold-500 focus:border-transparent resize-none text-forest-green-800 placeholder-forest-green-400 bg-white\",\n                                                                rows: 6,\n                                                                disabled: isGenerating\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                lineNumber: 231,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-red-600 text-sm bg-red-50 p-3 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium\",\n                                                                children: error\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                lineNumber: 245,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            suggestion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"mt-2 text-blue-600 bg-blue-50 p-2 rounded border-l-4 border-blue-400\",\n                                                                children: [\n                                                                    \"\\uD83D\\uDCA1 \",\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Suggestion:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                        lineNumber: 248,\n                                                                        columnNumber: 30\n                                                                    }, this),\n                                                                    \" \",\n                                                                    suggestion\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                lineNumber: 247,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: generateImage,\n                                                        disabled: isGenerating || !prompt.trim(),\n                                                        className: \"w-full bg-gold-500 text-white py-3 px-6 rounded-lg font-semibold hover:bg-gold-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition duration-300\",\n                                                        children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                    lineNumber: 261,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Generating Design...\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                            lineNumber: 260,\n                                                            columnNumber: 23\n                                                        }, this) : \"Generate Design\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 254,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white border border-forest-green-200 rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-4\",\n                                                children: \"\\uD83D\\uDDBC️ Upload Reference Image (Optional)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 17\n                                            }, this),\n                                            !uploadedImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-2 border-dashed border-forest-green-300 rounded-lg p-6 text-center hover:border-gold-500 transition-colors\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"file\",\n                                                        accept: \"image/*\",\n                                                        onChange: handleImageUpload,\n                                                        className: \"hidden\",\n                                                        id: \"image-upload\",\n                                                        disabled: isGenerating\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        htmlFor: \"image-upload\",\n                                                        className: \"cursor-pointer\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-4xl mb-2\",\n                                                                children: \"\\uD83D\\uDCF7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                lineNumber: 288,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-forest-green-600 mb-2\",\n                                                                children: \"Click to upload an image\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                lineNumber: 289,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-forest-green-500\",\n                                                                children: \"Upload a room photo for AI to redesign or use as inspiration\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                lineNumber: 290,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400 mt-2\",\n                                                                children: \"Supports JPG, PNG (max 5MB)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative h-48 rounded-lg overflow-hidden\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                            src: uploadedImage.preview,\n                                                            alt: \"Uploaded reference\",\n                                                            fill: true,\n                                                            className: \"object-cover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                            lineNumber: 301,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: removeUploadedImage,\n                                                        className: \"absolute top-2 right-2 bg-red-500 text-white rounded-full w-8 h-8 flex items-center justify-center hover:bg-red-600 transition-colors\",\n                                                        disabled: isGenerating,\n                                                        children: \"✕\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 308,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-forest-green-600 mt-2\",\n                                                        children: \"Reference image uploaded. The AI will use this for inspiration.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                        lineNumber: 272,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white border border-forest-green-200 rounded-lg p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-4\",\n                                                children: \"\\uD83D\\uDCA1 Try these sample prompts:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: samplePrompts.map((samplePrompt, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setPrompt(samplePrompt),\n                                                        className: \"w-full text-left p-3 bg-forest-green-50 border border-forest-green-200 rounded-lg hover:border-gold-500 hover:bg-gold-50 transition duration-200\",\n                                                        disabled: isGenerating,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-forest-green-600\",\n                                                            children: samplePrompt\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                            lineNumber: 335,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, index, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-forest-green-600 rounded-lg p-6 text-white\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold mb-4\",\n                                                children: \"\\uD83C\\uDFAF Tips for Better Results\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-2 text-forest-green-100 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Be specific about room types (kitchen, bedroom, living room, etc.)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Include style preferences (modern, traditional, minimalist, luxury)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Mention colors, materials, and lighting preferences\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Describe the mood you want to create (cozy, elegant, bright, etc.)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Include furniture and decor elements you envision\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: \"• Upload reference images to help the AI understand your vision\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-forest-green-50 rounded-lg p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-forest-green-700 mb-6\",\n                                            children: generatedContent.length > 0 ? \"Your Generated Designs\" : \"Generated Designs Will Appear Here\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 17\n                                        }, this),\n                                        generatedContent.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center py-12\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-6xl mb-4\",\n                                                    children: \"\\uD83C\\uDFA8\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-forest-green-600 mb-4\",\n                                                    children: \"Start by describing your interior design vision in the left panel.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-forest-green-500\",\n                                                    children: 'Your AI-generated designs will appear here once you click \"Generate Design\".'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 368,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6 h-[500px] overflow-y-auto\",\n                                            children: generatedContent.map((content, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white rounded-lg shadow-lg overflow-hidden\",\n                                                    children: [\n                                                        content.url ? // Display image if available\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative h-64\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                                src: content.url,\n                                                                alt: \"Generated design: \".concat(content.prompt),\n                                                                fill: true,\n                                                                className: \"object-cover\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 27\n                                                        }, this) : // Display text description from Gemini\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-6 bg-gradient-to-br from-gold-50 to-forest-green-50\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-4xl mb-4 text-center\",\n                                                                    children: \"\\uD83C\\uDFE0✨\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                    lineNumber: 389,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"text-lg font-semibold text-forest-green-700 mb-3\",\n                                                                    children: \"AI Design Concept\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                    lineNumber: 390,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-forest-green-600 text-sm leading-relaxed\",\n                                                                    children: content.textDescription\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                    lineNumber: 393,\n                                                                    columnNumber: 29\n                                                                }, this),\n                                                                content.message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"mt-3 p-3 bg-blue-50 border-l-4 border-blue-400 rounded\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-blue-700 text-sm\",\n                                                                        children: [\n                                                                            \"\\uD83D\\uDCA1 \",\n                                                                            content.message\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                        lineNumber: 398,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                    lineNumber: 397,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-forest-green-600 text-sm mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"Prompt:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                            lineNumber: 407,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \" \",\n                                                                        content.prompt\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                    lineNumber: 406,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-500 text-xs mb-3\",\n                                                                    children: [\n                                                                        \"Generated on \",\n                                                                        new Date(content.timestamp).toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                    lineNumber: 409,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex space-x-2\",\n                                                                    children: [\n                                                                        content.url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>{\n                                                                                const link = document.createElement(\"a\");\n                                                                                link.href = content.url;\n                                                                                link.download = \"modern-haven-design-\".concat(content.timestamp, \".png\");\n                                                                                link.click();\n                                                                            },\n                                                                            className: \"text-xs bg-gold-500 text-white px-3 py-1 rounded hover:bg-gold-600 transition duration-200\",\n                                                                            children: \"Download Image\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                            lineNumber: 414,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        content.textDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>{\n                                                                                navigator.clipboard.writeText(content.textDescription);\n                                                                                alert(\"Description copied to clipboard!\");\n                                                                            },\n                                                                            className: \"text-xs bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600 transition duration-200\",\n                                                                            children: \"Copy Description\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                            lineNumber: 427,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>setPrompt(content.prompt),\n                                                                            className: \"text-xs bg-forest-green-600 text-white px-3 py-1 rounded hover:bg-forest-green-700 transition duration-200\",\n                                                                            children: \"Use Prompt\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                            lineNumber: 437,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                                    lineNumber: 412,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                            lineNumber: 405,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                    lineNumber: 215,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-forest-green-800 text-white py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-1 md:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-gold-400 mb-4\",\n                                            children: \"Modern Haven\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-forest-green-200 mb-4\",\n                                            children: \"Creating beautiful, functional spaces that reflect your unique style and enhance your lifestyle. Your dream home is just a consultation away.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gold-400 hover:text-gold-300\",\n                                                    children: \"\\uD83D\\uDCD8\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gold-400 hover:text-gold-300\",\n                                                    children: \"\\uD83D\\uDCF7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gold-400 hover:text-gold-300\",\n                                                    children: \"\\uD83D\\uDC26\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gold-400 hover:text-gold-300\",\n                                                    children: \"\\uD83D\\uDCBC\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 469,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 465,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold text-gold-400 mb-4\",\n                                            children: \"Quick Links\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 473,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-forest-green-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"Home\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 475,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/#about\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"About\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 476,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/#services\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"Services\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 477,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/gallery\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"Gallery\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 478,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 478,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/ai-design\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"AI Design\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/#contact\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"Contact\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                    lineNumber: 472,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold text-gold-400 mb-4\",\n                                            children: \"AI Features\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 484,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-forest-green-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Interior Design Generation\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 486,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Room Visualization\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 487,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Style Exploration\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 488,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Design Inspiration\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 489,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Custom Concepts\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                                    lineNumber: 490,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                            lineNumber: 485,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                    lineNumber: 483,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                            lineNumber: 458,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-forest-green-700 mt-8 pt-8 text-center text-forest-green-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"\\xa9 2024 Modern Haven Interior Design. All rights reserved.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                                lineNumber: 495,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                            lineNumber: 494,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                    lineNumber: 457,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n                lineNumber: 456,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\ai-design\\\\page.tsx\",\n        lineNumber: 164,\n        columnNumber: 5\n    }, this);\n}\n_s(AIDesign, \"V9MX+/BdIKtjWl9dtpMff2sffEM=\");\n_c = AIDesign;\nvar _c;\n$RefreshReg$(_c, \"AIDesign\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ai-design/page.tsx\n"));

/***/ })

});