"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/gallery/page",{

/***/ "(app-pages-browser)/./src/app/gallery/page.tsx":
/*!**********************************!*\
  !*** ./src/app/gallery/page.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Gallery; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction Gallery() {\n    _s();\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const openModal = (image)=>{\n        setSelectedImage(image);\n    };\n    const closeModal = ()=>{\n        setSelectedImage(null);\n    };\n    // Handle keyboard events\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(()=>{\n        const handleKeyDown = (event)=>{\n            if (event.key === \"Escape\" && selectedImage) {\n                closeModal();\n            }\n        };\n        if (selectedImage) {\n            document.addEventListener(\"keydown\", handleKeyDown);\n            document.body.style.overflow = \"hidden\"; // Prevent background scrolling\n        } else {\n            document.body.style.overflow = \"unset\";\n        }\n        return ()=>{\n            document.removeEventListener(\"keydown\", handleKeyDown);\n            document.body.style.overflow = \"unset\";\n        };\n    }, [\n        selectedImage\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"bg-white shadow-sm border-b border-forest-green-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/\",\n                                    className: \"text-2xl font-bold text-forest-green-600\",\n                                    children: \"Modern Haven\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 51,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:block\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-10 flex items-baseline space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"Home\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"#about\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"About\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 56,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"#services\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"Services\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/gallery\",\n                                            className: \"bg-gold-500 text-white px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"Gallery\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/ai-design\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"AI Design\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"#contact\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"Contact\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 54,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative bg-gradient-to-br from-forest-green-50 to-white py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-5xl font-bold text-forest-green-700 mb-6\",\n                                children: [\n                                    \"Our \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gold-500\",\n                                        children: \"Gallery\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 71,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-forest-green-600 mb-8 max-w-3xl mx-auto\",\n                                children: \"Explore our diverse collections of interior design projects, from modern kitchens to organized storage solutions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl md:text-4xl font-bold text-forest-green-700 mb-6 text-center\",\n                                children: [\n                                    \"Kitchen \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gold-500\",\n                                        children: \"Reconstruction\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 23\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 85,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-forest-green-600 text-center max-w-4xl mx-auto mb-12\",\n                                children: \"Witness the complete transformation of a kitchen space from start to finish. This collection showcases our expertise in full-scale kitchen renovations, featuring modern design principles, functional layouts, and premium finishes that create the heart of the home.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/kitchen-reconstruction/IMG_7906.jpg\",\n                                                alt: \"Kitchen Reconstruction - Initial Phase\",\n                                                title: \"Initial Phase\",\n                                                description: \"The beginning of our comprehensive kitchen renovation project. This phase involves planning, measurements, and preparing the space for transformation.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/kitchen-reconstruction/IMG_7906.jpg\",\n                                                    alt: \"Kitchen Reconstruction - Initial Phase\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Initial Phase\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"The beginning of our comprehensive kitchen renovation project.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/kitchen-reconstruction/IMG_7908.jpg\",\n                                                alt: \"Kitchen Reconstruction - Demolition\",\n                                                title: \"Demolition Phase\",\n                                                description: \"Careful removal of existing structures to prepare for the new design. This phase requires precision to preserve structural integrity while clearing space for improvements.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/kitchen-reconstruction/IMG_7908.jpg\",\n                                                    alt: \"Kitchen Reconstruction - Demolition\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Demolition Phase\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Careful removal of existing structures to prepare for the new design.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/kitchen-reconstruction/IMG_7910.jpg\",\n                                                alt: \"Kitchen Reconstruction - Structural Work\",\n                                                title: \"Structural Work\",\n                                                description: \"Foundation and structural improvements for the new kitchen layout. This phase includes electrical, plumbing, and framework modifications to support the new design.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/kitchen-reconstruction/IMG_7910.jpg\",\n                                                    alt: \"Kitchen Reconstruction - Structural Work\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Structural Work\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Foundation and structural improvements for the new kitchen layout.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/kitchen-reconstruction/IMG_7911.jpg\",\n                                                alt: \"Kitchen Reconstruction - Installation\",\n                                                title: \"Installation Phase\",\n                                                description: \"Installing cabinets, countertops, and essential kitchen elements. This phase brings the design to life with custom cabinetry and premium materials.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/kitchen-reconstruction/IMG_7911.jpg\",\n                                                    alt: \"Kitchen Reconstruction - Installation\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Installation Phase\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Installing cabinets, countertops, and essential kitchen elements.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/kitchen-reconstruction/IMG_7913.jpg\",\n                                                alt: \"Kitchen Reconstruction - Finishing Touches\",\n                                                title: \"Finishing Touches\",\n                                                description: \"Adding the final details that bring the design vision to life. This includes hardware installation, lighting fixtures, and decorative elements.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/kitchen-reconstruction/IMG_7913.jpg\",\n                                                    alt: \"Kitchen Reconstruction - Finishing Touches\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Finishing Touches\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Adding the final details that bring the design vision to life.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/kitchen-reconstruction/IMG_7915.jpg\",\n                                                alt: \"Kitchen Reconstruction - Completed\",\n                                                title: \"Final Result\",\n                                                description: \"The stunning completed kitchen transformation. A perfect blend of functionality and style, showcasing modern design principles and premium craftsmanship.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/kitchen-reconstruction/IMG_7915.jpg\",\n                                                    alt: \"Kitchen Reconstruction - Completed\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Final Result\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"The stunning completed kitchen transformation.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 225,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-forest-green-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl md:text-4xl font-bold text-forest-green-700 mb-6 text-center\",\n                                children: [\n                                    \"Modern \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gold-500\",\n                                        children: \"Kitchen Designs\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 22\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-forest-green-600 text-center max-w-4xl mx-auto mb-12\",\n                                children: \"Our collection of contemporary kitchen designs featuring sleek lines, innovative storage solutions, and cutting-edge appliances. Each design balances functionality with aesthetic appeal to create the perfect culinary workspace.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/Kitchen/IMG_6009.PNG\",\n                                                alt: \"Modern Kitchen Design 1\",\n                                                title: \"Minimalist Excellence\",\n                                                description: \"Clean lines and neutral tones create a serene cooking environment. This design emphasizes simplicity and functionality with hidden storage solutions and streamlined appliances.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg overflow-hidden h-80 mb-4 shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Kitchen/IMG_6009.PNG\",\n                                                    alt: \"Modern Kitchen Design 1\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Minimalist Excellence\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Clean lines and neutral tones create a serene cooking environment.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/Kitchen/IMG_6010.PNG\",\n                                                alt: \"Modern Kitchen Design 2\",\n                                                title: \"Contemporary Elegance\",\n                                                description: \"Sophisticated design with premium materials and finishes. Features high-end appliances, custom cabinetry, and elegant lighting that creates a luxurious cooking experience.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg overflow-hidden h-80 mb-4 shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Kitchen/IMG_6010.PNG\",\n                                                    alt: \"Modern Kitchen Design 2\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Contemporary Elegance\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Sophisticated design with premium materials and finishes.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/Kitchen/IMG_6711.JPG\",\n                                                alt: \"Modern Kitchen Design 3\",\n                                                title: \"Functional Beauty\",\n                                                description: \"Perfect blend of style and practicality for everyday living. Smart storage solutions and ergonomic design make cooking and entertaining effortless and enjoyable.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg overflow-hidden h-80 mb-4 shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Kitchen/IMG_6711.JPG\",\n                                                    alt: \"Modern Kitchen Design 3\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Functional Beauty\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Perfect blend of style and practicality for everyday living.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/Kitchen/IMG_6712.JPG\",\n                                                alt: \"Modern Kitchen Design 4\",\n                                                title: \"Luxury Living\",\n                                                description: \"High-end appliances and custom cabinetry for the discerning homeowner. Premium materials and sophisticated finishes create an upscale culinary environment.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg overflow-hidden h-80 mb-4 shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Kitchen/IMG_6712.JPG\",\n                                                    alt: \"Modern Kitchen Design 4\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Luxury Living\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"High-end appliances and custom cabinetry for the discerning homeowner.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/Kitchen/IMG_6713.JPG\",\n                                                alt: \"Modern Kitchen Design 5\",\n                                                title: \"Smart Design\",\n                                                description: \"Innovative storage solutions maximize space and efficiency. Clever organization systems and multi-functional elements make the most of every square inch.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg overflow-hidden h-80 mb-4 shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Kitchen/IMG_6713.JPG\",\n                                                    alt: \"Modern Kitchen Design 5\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Smart Design\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Innovative storage solutions maximize space and efficiency.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/Kitchen/IMG_6714.JPG\",\n                                                alt: \"Modern Kitchen Design 6\",\n                                                title: \"Timeless Appeal\",\n                                                description: \"Classic design elements with modern functionality. This kitchen combines traditional aesthetics with contemporary conveniences for enduring style and performance.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg overflow-hidden h-80 mb-4 shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Kitchen/IMG_6714.JPG\",\n                                                    alt: \"Modern Kitchen Design 6\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Timeless Appeal\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Classic design elements with modern functionality.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl md:text-4xl font-bold text-forest-green-700 mb-6 text-center\",\n                                children: [\n                                    \"Closet \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gold-500\",\n                                        children: \"Organization\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 22\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-forest-green-600 text-center max-w-4xl mx-auto mb-12\",\n                                children: \"Transform your storage spaces with our custom closet organization solutions. From walk-in wardrobes to compact storage areas, we create systems that maximize space while maintaining style and accessibility.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/Closet/IMG_6003.PNG\",\n                                                alt: \"Closet Organization 1\",\n                                                title: \"Walk-in Wardrobe\",\n                                                description: \"Luxurious walk-in closet with custom shelving and hanging solutions. Features dedicated spaces for different clothing types, shoes, and accessories with elegant lighting.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Closet/IMG_6003.PNG\",\n                                                    alt: \"Closet Organization 1\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Walk-in Wardrobe\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 415,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Luxurious walk-in closet with custom shelving and hanging solutions.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/Closet/IMG_6004.PNG\",\n                                                alt: \"Closet Organization 2\",\n                                                title: \"Organized Storage\",\n                                                description: \"Efficient storage system with designated spaces for every item. Smart organization solutions that make finding and storing belongings effortless and intuitive.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Closet/IMG_6004.PNG\",\n                                                    alt: \"Closet Organization 2\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Organized Storage\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Efficient storage system with designated spaces for every item.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/Closet/IMG_6005.PNG\",\n                                                alt: \"Closet Organization 3\",\n                                                title: \"Custom Solutions\",\n                                                description: \"Tailored organization systems designed for your specific needs. Every element is customized to fit your lifestyle, space constraints, and storage requirements.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Closet/IMG_6005.PNG\",\n                                                    alt: \"Closet Organization 3\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 451,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 450,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Custom Solutions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Tailored organization systems designed for your specific needs.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/Closet/IMG_6006.PNG\",\n                                                alt: \"Closet Organization 4\",\n                                                title: \"Space Optimization\",\n                                                description: \"Maximizing every inch of available space with smart design. Vertical storage solutions and multi-level organization create maximum capacity in minimal footprint.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Closet/IMG_6006.PNG\",\n                                                    alt: \"Closet Organization 4\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 473,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Space Optimization\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 481,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Maximizing every inch of available space with smart design.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 463,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/Closet/IMG_6007.PNG\",\n                                                alt: \"Closet Organization 5\",\n                                                title: \"Elegant Design\",\n                                                description: \"Beautiful and functional closet design that complements your home. Sophisticated finishes and thoughtful details create a luxurious storage experience.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Closet/IMG_6007.PNG\",\n                                                    alt: \"Closet Organization 5\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 495,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Elegant Design\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 503,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Beautiful and functional closet design that complements your home.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 485,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/Closet/IMG_6008.PNG\",\n                                                alt: \"Closet Organization 6\",\n                                                title: \"Complete Organization\",\n                                                description: \"Comprehensive storage solution for all your belongings. Every item has its place in this thoughtfully designed organization system that maintains order and accessibility.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Closet/IMG_6008.PNG\",\n                                                    alt: \"Closet Organization 6\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 517,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 516,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Complete Organization\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 525,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Comprehensive storage solution for all your belongings.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 526,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 395,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                        lineNumber: 386,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                    lineNumber: 385,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                lineNumber: 384,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-forest-green-600\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-white mb-6\",\n                            children: [\n                                \"Ready to Start Your \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gold-400\",\n                                    children: \"Project?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 537,\n                                    columnNumber: 33\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                            lineNumber: 536,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-forest-green-100 mb-8 max-w-2xl mx-auto\",\n                            children: \"Let's bring your vision to life. Contact us today to discuss your interior design needs.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                            lineNumber: 539,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/#contact\",\n                                    className: \"bg-gold-500 text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-gold-600 transition duration-300\",\n                                    children: \"Get Started\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 543,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/\",\n                                    className: \"border-2 border-white text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-white hover:text-forest-green-600 transition duration-300\",\n                                    children: \"Back to Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 546,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                            lineNumber: 542,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                    lineNumber: 535,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                lineNumber: 534,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-forest-green-800 text-white py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-1 md:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-gold-400 mb-4\",\n                                            children: \"Modern Haven\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 558,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-forest-green-200 mb-4\",\n                                            children: \"Creating beautiful, functional spaces that reflect your unique style and enhance your lifestyle. Your dream home is just a consultation away.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 559,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gold-400 hover:text-gold-300\",\n                                                    children: \"\\uD83D\\uDCD8\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 564,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gold-400 hover:text-gold-300\",\n                                                    children: \"\\uD83D\\uDCF7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 565,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gold-400 hover:text-gold-300\",\n                                                    children: \"\\uD83D\\uDC26\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 566,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gold-400 hover:text-gold-300\",\n                                                    children: \"\\uD83D\\uDCBC\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 567,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 563,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 557,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold text-gold-400 mb-4\",\n                                            children: \"Quick Links\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 571,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-forest-green-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"Home\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                        lineNumber: 573,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 573,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/#about\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"About\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                        lineNumber: 574,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 574,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/#services\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"Services\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                        lineNumber: 575,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 575,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/gallery\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"Gallery\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                        lineNumber: 576,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 576,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/#contact\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"Contact\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                        lineNumber: 577,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 577,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 572,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 570,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold text-gold-400 mb-4\",\n                                            children: \"Collections\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 581,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-forest-green-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Kitchen Reconstruction\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 583,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Modern Kitchen Designs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 584,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Closet Organization\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 585,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Custom Storage Solutions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 586,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Interior Styling\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 587,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 582,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 580,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                            lineNumber: 556,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-forest-green-700 mt-8 pt-8 text-center text-forest-green-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"\\xa9 2024 Modern Haven Interior Design. All rights reserved.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 592,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                            lineNumber: 591,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                    lineNumber: 555,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                lineNumber: 554,\n                columnNumber: 7\n            }, this),\n            selectedImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4\",\n                onClick: closeModal,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative max-w-4xl max-h-full bg-white rounded-lg overflow-hidden\",\n                    onClick: (e)=>e.stopPropagation(),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: closeModal,\n                            className: \"absolute top-4 right-4 z-10 bg-forest-green-600 text-white rounded-full w-10 h-10 flex items-center justify-center hover:bg-forest-green-700 transition-colors\",\n                            children: \"✕\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                            lineNumber: 608,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                src: selectedImage.src,\n                                alt: selectedImage.alt,\n                                width: 1200,\n                                height: 800,\n                                className: \"w-full h-auto max-h-[80vh] object-contain\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 617,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                            lineNumber: 616,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 bg-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-forest-green-700 mb-2\",\n                                    children: selectedImage.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 628,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-forest-green-600\",\n                                    children: selectedImage.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 629,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                            lineNumber: 627,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                    lineNumber: 603,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                lineNumber: 599,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, this);\n}\n_s(Gallery, \"JEj6HdkTQ+WTAGk4QGMhDxCHE+w=\");\n_c = Gallery;\nvar _c;\n$RefreshReg$(_c, \"Gallery\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/gallery/page.tsx\n"));

/***/ })

});