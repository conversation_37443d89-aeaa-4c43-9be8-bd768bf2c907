"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/gallery/page",{

/***/ "(app-pages-browser)/./src/app/gallery/page.tsx":
/*!**********************************!*\
  !*** ./src/app/gallery/page.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Gallery; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction Gallery() {\n    _s();\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const openModal = (image)=>{\n        setSelectedImage(image);\n    };\n    const closeModal = ()=>{\n        setSelectedImage(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"bg-white shadow-sm border-b border-forest-green-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/\",\n                                    className: \"text-2xl font-bold text-forest-green-600\",\n                                    children: \"Modern Haven\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:block\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-10 flex items-baseline space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"Home\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 34,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"#about\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"About\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 35,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"#services\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"Services\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/gallery\",\n                                            className: \"bg-gold-500 text-white px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"Gallery\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"#contact\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"Contact\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative bg-gradient-to-br from-forest-green-50 to-white py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-5xl font-bold text-forest-green-700 mb-6\",\n                                children: [\n                                    \"Our \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gold-500\",\n                                        children: \"Gallery\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 50,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-forest-green-600 mb-8 max-w-3xl mx-auto\",\n                                children: \"Explore our diverse collections of interior design projects, from modern kitchens to organized storage solutions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl md:text-4xl font-bold text-forest-green-700 mb-6 text-center\",\n                                children: [\n                                    \"Kitchen \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gold-500\",\n                                        children: \"Reconstruction\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 23\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-forest-green-600 text-center max-w-4xl mx-auto mb-12\",\n                                children: \"Witness the complete transformation of a kitchen space from start to finish. This collection showcases our expertise in full-scale kitchen renovations, featuring modern design principles, functional layouts, and premium finishes that create the heart of the home.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/kitchen-reconstruction/IMG_7906.jpg\",\n                                                alt: \"Kitchen Reconstruction - Initial Phase\",\n                                                title: \"Initial Phase\",\n                                                description: \"The beginning of our comprehensive kitchen renovation project. This phase involves planning, measurements, and preparing the space for transformation.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/kitchen-reconstruction/IMG_7906.jpg\",\n                                                    alt: \"Kitchen Reconstruction - Initial Phase\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Initial Phase\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"The beginning of our comprehensive kitchen renovation project.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/kitchen-reconstruction/IMG_7908.jpg\",\n                                                alt: \"Kitchen Reconstruction - Demolition\",\n                                                title: \"Demolition Phase\",\n                                                description: \"Careful removal of existing structures to prepare for the new design. This phase requires precision to preserve structural integrity while clearing space for improvements.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/kitchen-reconstruction/IMG_7908.jpg\",\n                                                    alt: \"Kitchen Reconstruction - Demolition\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Demolition Phase\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Careful removal of existing structures to prepare for the new design.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/kitchen-reconstruction/IMG_7910.jpg\",\n                                                alt: \"Kitchen Reconstruction - Structural Work\",\n                                                title: \"Structural Work\",\n                                                description: \"Foundation and structural improvements for the new kitchen layout. This phase includes electrical, plumbing, and framework modifications to support the new design.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/kitchen-reconstruction/IMG_7910.jpg\",\n                                                    alt: \"Kitchen Reconstruction - Structural Work\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Structural Work\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Foundation and structural improvements for the new kitchen layout.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/kitchen-reconstruction/IMG_7911.jpg\",\n                                                alt: \"Kitchen Reconstruction - Installation\",\n                                                title: \"Installation Phase\",\n                                                description: \"Installing cabinets, countertops, and essential kitchen elements. This phase brings the design to life with custom cabinetry and premium materials.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/kitchen-reconstruction/IMG_7911.jpg\",\n                                                    alt: \"Kitchen Reconstruction - Installation\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Installation Phase\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Installing cabinets, countertops, and essential kitchen elements.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/kitchen-reconstruction/IMG_7913.jpg\",\n                                                alt: \"Kitchen Reconstruction - Finishing Touches\",\n                                                title: \"Finishing Touches\",\n                                                description: \"Adding the final details that bring the design vision to life. This includes hardware installation, lighting fixtures, and decorative elements.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/kitchen-reconstruction/IMG_7913.jpg\",\n                                                    alt: \"Kitchen Reconstruction - Finishing Touches\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Finishing Touches\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Adding the final details that bring the design vision to life.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/kitchen-reconstruction/IMG_7915.jpg\",\n                                                alt: \"Kitchen Reconstruction - Completed\",\n                                                title: \"Final Result\",\n                                                description: \"The stunning completed kitchen transformation. A perfect blend of functionality and style, showcasing modern design principles and premium craftsmanship.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/kitchen-reconstruction/IMG_7915.jpg\",\n                                                    alt: \"Kitchen Reconstruction - Completed\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Final Result\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"The stunning completed kitchen transformation.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-forest-green-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl md:text-4xl font-bold text-forest-green-700 mb-6 text-center\",\n                                children: [\n                                    \"Modern \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gold-500\",\n                                        children: \"Kitchen Designs\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 22\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-forest-green-600 text-center max-w-4xl mx-auto mb-12\",\n                                children: \"Our collection of contemporary kitchen designs featuring sleek lines, innovative storage solutions, and cutting-edge appliances. Each design balances functionality with aesthetic appeal to create the perfect culinary workspace.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/Kitchen/IMG_6009.PNG\",\n                                                alt: \"Modern Kitchen Design 1\",\n                                                title: \"Minimalist Excellence\",\n                                                description: \"Clean lines and neutral tones create a serene cooking environment. This design emphasizes simplicity and functionality with hidden storage solutions and streamlined appliances.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg overflow-hidden h-80 mb-4 shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Kitchen/IMG_6009.PNG\",\n                                                    alt: \"Modern Kitchen Design 1\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Minimalist Excellence\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Clean lines and neutral tones create a serene cooking environment.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/Kitchen/IMG_6010.PNG\",\n                                                alt: \"Modern Kitchen Design 2\",\n                                                title: \"Contemporary Elegance\",\n                                                description: \"Sophisticated design with premium materials and finishes. Features high-end appliances, custom cabinetry, and elegant lighting that creates a luxurious cooking experience.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg overflow-hidden h-80 mb-4 shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Kitchen/IMG_6010.PNG\",\n                                                    alt: \"Modern Kitchen Design 2\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Contemporary Elegance\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Sophisticated design with premium materials and finishes.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/Kitchen/IMG_6711.JPG\",\n                                                alt: \"Modern Kitchen Design 3\",\n                                                title: \"Functional Beauty\",\n                                                description: \"Perfect blend of style and practicality for everyday living. Smart storage solutions and ergonomic design make cooking and entertaining effortless and enjoyable.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg overflow-hidden h-80 mb-4 shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Kitchen/IMG_6711.JPG\",\n                                                    alt: \"Modern Kitchen Design 3\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Functional Beauty\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Perfect blend of style and practicality for everyday living.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 269,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/Kitchen/IMG_6712.JPG\",\n                                                alt: \"Modern Kitchen Design 4\",\n                                                title: \"Luxury Living\",\n                                                description: \"High-end appliances and custom cabinetry for the discerning homeowner. Premium materials and sophisticated finishes create an upscale culinary environment.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg overflow-hidden h-80 mb-4 shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Kitchen/IMG_6712.JPG\",\n                                                    alt: \"Modern Kitchen Design 4\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 301,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Luxury Living\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"High-end appliances and custom cabinetry for the discerning homeowner.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/Kitchen/IMG_6713.JPG\",\n                                                alt: \"Modern Kitchen Design 5\",\n                                                title: \"Smart Design\",\n                                                description: \"Innovative storage solutions maximize space and efficiency. Clever organization systems and multi-functional elements make the most of every square inch.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg overflow-hidden h-80 mb-4 shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Kitchen/IMG_6713.JPG\",\n                                                    alt: \"Modern Kitchen Design 5\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Smart Design\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Innovative storage solutions maximize space and efficiency.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/Kitchen/IMG_6714.JPG\",\n                                                alt: \"Modern Kitchen Design 6\",\n                                                title: \"Timeless Appeal\",\n                                                description: \"Classic design elements with modern functionality. This kitchen combines traditional aesthetics with contemporary conveniences for enduring style and performance.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg overflow-hidden h-80 mb-4 shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Kitchen/IMG_6714.JPG\",\n                                                    alt: \"Modern Kitchen Design 6\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Timeless Appeal\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Classic design elements with modern functionality.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 354,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                lineNumber: 211,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl md:text-4xl font-bold text-forest-green-700 mb-6 text-center\",\n                                children: [\n                                    \"Closet \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gold-500\",\n                                        children: \"Organization\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 22\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-forest-green-600 text-center max-w-4xl mx-auto mb-12\",\n                                children: \"Transform your storage spaces with our custom closet organization solutions. From walk-in wardrobes to compact storage areas, we create systems that maximize space while maintaining style and accessibility.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 368,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/Closet/IMG_6003.PNG\",\n                                                alt: \"Closet Organization 1\",\n                                                title: \"Walk-in Wardrobe\",\n                                                description: \"Luxurious walk-in closet with custom shelving and hanging solutions. Features dedicated spaces for different clothing types, shoes, and accessories with elegant lighting.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Closet/IMG_6003.PNG\",\n                                                    alt: \"Closet Organization 1\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Walk-in Wardrobe\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Luxurious walk-in closet with custom shelving and hanging solutions.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/Closet/IMG_6004.PNG\",\n                                                alt: \"Closet Organization 2\",\n                                                title: \"Organized Storage\",\n                                                description: \"Efficient storage system with designated spaces for every item. Smart organization solutions that make finding and storing belongings effortless and intuitive.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Closet/IMG_6004.PNG\",\n                                                    alt: \"Closet Organization 2\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Organized Storage\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 415,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Efficient storage system with designated spaces for every item.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/Closet/IMG_6005.PNG\",\n                                                alt: \"Closet Organization 3\",\n                                                title: \"Custom Solutions\",\n                                                description: \"Tailored organization systems designed for your specific needs. Every element is customized to fit your lifestyle, space constraints, and storage requirements.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Closet/IMG_6005.PNG\",\n                                                    alt: \"Closet Organization 3\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Custom Solutions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Tailored organization systems designed for your specific needs.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 419,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Closet/IMG_6006.PNG\",\n                                                    alt: \"Closet Organization 4\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 442,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Space Optimization\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Maximizing every inch of available space with smart design.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 441,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Closet/IMG_6007.PNG\",\n                                                    alt: \"Closet Organization 5\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Elegant Design\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Beautiful and functional closet design that complements your home.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Closet/IMG_6008.PNG\",\n                                                    alt: \"Closet Organization 6\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 471,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Complete Organization\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 479,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Comprehensive storage solution for all your belongings.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 480,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 373,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                        lineNumber: 364,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                    lineNumber: 363,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                lineNumber: 362,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-forest-green-600\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-white mb-6\",\n                            children: [\n                                \"Ready to Start Your \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gold-400\",\n                                    children: \"Project?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 491,\n                                    columnNumber: 33\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                            lineNumber: 490,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-forest-green-100 mb-8 max-w-2xl mx-auto\",\n                            children: \"Let's bring your vision to life. Contact us today to discuss your interior design needs.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                            lineNumber: 493,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/#contact\",\n                                    className: \"bg-gold-500 text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-gold-600 transition duration-300\",\n                                    children: \"Get Started\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/\",\n                                    className: \"border-2 border-white text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-white hover:text-forest-green-600 transition duration-300\",\n                                    children: \"Back to Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 500,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                            lineNumber: 496,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                    lineNumber: 489,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                lineNumber: 488,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-forest-green-800 text-white py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-1 md:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-gold-400 mb-4\",\n                                            children: \"Modern Haven\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-forest-green-200 mb-4\",\n                                            children: \"Creating beautiful, functional spaces that reflect your unique style and enhance your lifestyle. Your dream home is just a consultation away.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 513,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gold-400 hover:text-gold-300\",\n                                                    children: \"\\uD83D\\uDCD8\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gold-400 hover:text-gold-300\",\n                                                    children: \"\\uD83D\\uDCF7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 519,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gold-400 hover:text-gold-300\",\n                                                    children: \"\\uD83D\\uDC26\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 520,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gold-400 hover:text-gold-300\",\n                                                    children: \"\\uD83D\\uDCBC\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 521,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 517,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 511,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold text-gold-400 mb-4\",\n                                            children: \"Quick Links\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 525,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-forest-green-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"Home\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/#about\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"About\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                        lineNumber: 528,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 528,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/#services\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"Services\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                        lineNumber: 529,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/gallery\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"Gallery\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                        lineNumber: 530,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 530,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/#contact\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"Contact\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                        lineNumber: 531,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 531,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 526,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 524,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold text-gold-400 mb-4\",\n                                            children: \"Collections\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-forest-green-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Kitchen Reconstruction\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 537,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Modern Kitchen Designs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 538,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Closet Organization\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Custom Storage Solutions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 540,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Interior Styling\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 541,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 536,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 534,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                            lineNumber: 510,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-forest-green-700 mt-8 pt-8 text-center text-forest-green-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"\\xa9 2024 Modern Haven Interior Design. All rights reserved.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 546,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                            lineNumber: 545,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                    lineNumber: 509,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                lineNumber: 508,\n                columnNumber: 7\n            }, this),\n            selectedImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative max-w-4xl max-h-full bg-white rounded-lg overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: closeModal,\n                            className: \"absolute top-4 right-4 z-10 bg-forest-green-600 text-white rounded-full w-10 h-10 flex items-center justify-center hover:bg-forest-green-700 transition-colors\",\n                            children: \"✕\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                            lineNumber: 556,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                src: selectedImage.src,\n                                alt: selectedImage.alt,\n                                width: 1200,\n                                height: 800,\n                                className: \"w-full h-auto max-h-[80vh] object-contain\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 565,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                            lineNumber: 564,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 bg-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-forest-green-700 mb-2\",\n                                    children: selectedImage.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 576,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-forest-green-600\",\n                                    children: selectedImage.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 577,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                            lineNumber: 575,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                    lineNumber: 554,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                lineNumber: 553,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n_s(Gallery, \"gNHFCSJ3h3sH8HFrpxN7hUqoxHs=\");\n_c = Gallery;\nvar _c;\n$RefreshReg$(_c, \"Gallery\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/gallery/page.tsx\n"));

/***/ })

});