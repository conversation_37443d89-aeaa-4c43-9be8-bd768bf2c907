"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/gallery/page",{

/***/ "(app-pages-browser)/./src/app/gallery/page.tsx":
/*!**********************************!*\
  !*** ./src/app/gallery/page.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Gallery; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction Gallery() {\n    _s();\n    const [selectedImage, setSelectedImage] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(null);\n    const openModal = (image)=>{\n        setSelectedImage(image);\n    };\n    const closeModal = ()=>{\n        setSelectedImage(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-white\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"bg-white shadow-sm border-b border-forest-green-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/\",\n                                    className: \"text-2xl font-bold text-forest-green-600\",\n                                    children: \"Modern Haven\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 30,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"hidden md:block\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-10 flex items-baseline space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"Home\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 34,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"#about\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"About\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 35,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"#services\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"Services\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"/gallery\",\n                                            className: \"bg-gold-500 text-white px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"Gallery\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            href: \"#contact\",\n                                            className: \"text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium\",\n                                            children: \"Contact\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 33,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative bg-gradient-to-br from-forest-green-50 to-white py-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-5xl font-bold text-forest-green-700 mb-6\",\n                                children: [\n                                    \"Our \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gold-500\",\n                                        children: \"Gallery\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 50,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-forest-green-600 mb-8 max-w-3xl mx-auto\",\n                                children: \"Explore our diverse collections of interior design projects, from modern kitchens to organized storage solutions\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl md:text-4xl font-bold text-forest-green-700 mb-6 text-center\",\n                                children: [\n                                    \"Kitchen \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gold-500\",\n                                        children: \"Reconstruction\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 23\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-forest-green-600 text-center max-w-4xl mx-auto mb-12\",\n                                children: \"Witness the complete transformation of a kitchen space from start to finish. This collection showcases our expertise in full-scale kitchen renovations, featuring modern design principles, functional layouts, and premium finishes that create the heart of the home.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 66,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/kitchen-reconstruction/IMG_7906.jpg\",\n                                                alt: \"Kitchen Reconstruction - Initial Phase\",\n                                                title: \"Initial Phase\",\n                                                description: \"The beginning of our comprehensive kitchen renovation project. This phase involves planning, measurements, and preparing the space for transformation.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/kitchen-reconstruction/IMG_7906.jpg\",\n                                                    alt: \"Kitchen Reconstruction - Initial Phase\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 84,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Initial Phase\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 92,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"The beginning of our comprehensive kitchen renovation project.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/kitchen-reconstruction/IMG_7908.jpg\",\n                                                alt: \"Kitchen Reconstruction - Demolition\",\n                                                title: \"Demolition Phase\",\n                                                description: \"Careful removal of existing structures to prepare for the new design. This phase requires precision to preserve structural integrity while clearing space for improvements.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/kitchen-reconstruction/IMG_7908.jpg\",\n                                                    alt: \"Kitchen Reconstruction - Demolition\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Demolition Phase\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Careful removal of existing structures to prepare for the new design.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/kitchen-reconstruction/IMG_7910.jpg\",\n                                                alt: \"Kitchen Reconstruction - Structural Work\",\n                                                title: \"Structural Work\",\n                                                description: \"Foundation and structural improvements for the new kitchen layout. This phase includes electrical, plumbing, and framework modifications to support the new design.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/kitchen-reconstruction/IMG_7910.jpg\",\n                                                    alt: \"Kitchen Reconstruction - Structural Work\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 128,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Structural Work\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 136,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Foundation and structural improvements for the new kitchen layout.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/kitchen-reconstruction/IMG_7911.jpg\",\n                                                alt: \"Kitchen Reconstruction - Installation\",\n                                                title: \"Installation Phase\",\n                                                description: \"Installing cabinets, countertops, and essential kitchen elements. This phase brings the design to life with custom cabinetry and premium materials.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/kitchen-reconstruction/IMG_7911.jpg\",\n                                                    alt: \"Kitchen Reconstruction - Installation\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Installation Phase\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Installing cabinets, countertops, and essential kitchen elements.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/kitchen-reconstruction/IMG_7913.jpg\",\n                                                alt: \"Kitchen Reconstruction - Finishing Touches\",\n                                                title: \"Finishing Touches\",\n                                                description: \"Adding the final details that bring the design vision to life. This includes hardware installation, lighting fixtures, and decorative elements.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/kitchen-reconstruction/IMG_7913.jpg\",\n                                                    alt: \"Kitchen Reconstruction - Finishing Touches\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Finishing Touches\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 180,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Adding the final details that bring the design vision to life.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        onClick: ()=>openModal({\n                                                src: \"/images/kitchen-reconstruction/IMG_7915.jpg\",\n                                                alt: \"Kitchen Reconstruction - Completed\",\n                                                title: \"Final Result\",\n                                                description: \"The stunning completed kitchen transformation. A perfect blend of functionality and style, showcasing modern design principles and premium craftsmanship.\"\n                                            }),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/kitchen-reconstruction/IMG_7915.jpg\",\n                                                    alt: \"Kitchen Reconstruction - Completed\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Final Result\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"The stunning completed kitchen transformation.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-forest-green-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl md:text-4xl font-bold text-forest-green-700 mb-6 text-center\",\n                                children: [\n                                    \"Modern \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gold-500\",\n                                        children: \"Kitchen Designs\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 22\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-forest-green-600 text-center max-w-4xl mx-auto mb-12\",\n                                children: \"Our collection of contemporary kitchen designs featuring sleek lines, innovative storage solutions, and cutting-edge appliances. Each design balances functionality with aesthetic appeal to create the perfect culinary workspace.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg overflow-hidden h-80 mb-4 shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Kitchen/IMG_6009.PNG\",\n                                                    alt: \"Modern Kitchen Design 1\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Minimalist Excellence\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Clean lines and neutral tones create a serene cooking environment.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg overflow-hidden h-80 mb-4 shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Kitchen/IMG_6010.PNG\",\n                                                    alt: \"Modern Kitchen Design 2\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 241,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Contemporary Elegance\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Sophisticated design with premium materials and finishes.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg overflow-hidden h-80 mb-4 shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Kitchen/IMG_6711.JPG\",\n                                                    alt: \"Modern Kitchen Design 3\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Functional Beauty\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Perfect blend of style and practicality for everyday living.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg overflow-hidden h-80 mb-4 shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Kitchen/IMG_6712.JPG\",\n                                                    alt: \"Modern Kitchen Design 4\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Luxury Living\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"High-end appliances and custom cabinetry for the discerning homeowner.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg overflow-hidden h-80 mb-4 shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Kitchen/IMG_6713.JPG\",\n                                                    alt: \"Modern Kitchen Design 5\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 283,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Smart Design\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Innovative storage solutions maximize space and efficiency.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white rounded-lg overflow-hidden h-80 mb-4 shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Kitchen/IMG_6714.JPG\",\n                                                    alt: \"Modern Kitchen Design 6\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Timeless Appeal\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 305,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Classic design elements with modern functionality.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                lineNumber: 211,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl md:text-4xl font-bold text-forest-green-700 mb-6 text-center\",\n                                children: [\n                                    \"Closet \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-gold-500\",\n                                        children: \"Organization\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 22\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-forest-green-600 text-center max-w-4xl mx-auto mb-12\",\n                                children: \"Transform your storage spaces with our custom closet organization solutions. From walk-in wardrobes to compact storage areas, we create systems that maximize space while maintaining style and accessibility.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Closet/IMG_6003.PNG\",\n                                                    alt: \"Closet Organization 1\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Walk-in Wardrobe\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Luxurious walk-in closet with custom shelving and hanging solutions.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 338,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Closet/IMG_6004.PNG\",\n                                                    alt: \"Closet Organization 2\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 342,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Organized Storage\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 351,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Efficient storage system with designated spaces for every item.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Closet/IMG_6005.PNG\",\n                                                    alt: \"Closet Organization 3\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 357,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Custom Solutions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 365,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Tailored organization systems designed for your specific needs.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Closet/IMG_6006.PNG\",\n                                                    alt: \"Closet Organization 4\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 371,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Space Optimization\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Maximizing every inch of available space with smart design.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Closet/IMG_6007.PNG\",\n                                                    alt: \"Closet Organization 5\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Elegant Design\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 393,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Beautiful and functional closet design that complements your home.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"group cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    src: \"/images/Closet/IMG_6008.PNG\",\n                                                    alt: \"Closet Organization 6\",\n                                                    width: 400,\n                                                    height: 320,\n                                                    className: \"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 399,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-forest-green-700 mb-2\",\n                                                children: \"Complete Organization\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 407,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-forest-green-600\",\n                                                children: \"Comprehensive storage solution for all your belongings.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                    lineNumber: 315,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                lineNumber: 314,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-forest-green-600\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold text-white mb-6\",\n                            children: [\n                                \"Ready to Start Your \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-gold-400\",\n                                    children: \"Project?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 33\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                            lineNumber: 418,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-forest-green-100 mb-8 max-w-2xl mx-auto\",\n                            children: \"Let's bring your vision to life. Contact us today to discuss your interior design needs.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                            lineNumber: 421,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/#contact\",\n                                    className: \"bg-gold-500 text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-gold-600 transition duration-300\",\n                                    children: \"Get Started\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 425,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/\",\n                                    className: \"border-2 border-white text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-white hover:text-forest-green-600 transition duration-300\",\n                                    children: \"Back to Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 428,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                            lineNumber: 424,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                    lineNumber: 417,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                lineNumber: 416,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-forest-green-800 text-white py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"col-span-1 md:col-span-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-2xl font-bold text-gold-400 mb-4\",\n                                            children: \"Modern Haven\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 440,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-forest-green-200 mb-4\",\n                                            children: \"Creating beautiful, functional spaces that reflect your unique style and enhance your lifestyle. Your dream home is just a consultation away.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gold-400 hover:text-gold-300\",\n                                                    children: \"\\uD83D\\uDCD8\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 446,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gold-400 hover:text-gold-300\",\n                                                    children: \"\\uD83D\\uDCF7\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gold-400 hover:text-gold-300\",\n                                                    children: \"\\uD83D\\uDC26\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 448,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"#\",\n                                                    className: \"text-gold-400 hover:text-gold-300\",\n                                                    children: \"\\uD83D\\uDCBC\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold text-gold-400 mb-4\",\n                                            children: \"Quick Links\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 453,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-forest-green-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"Home\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                        lineNumber: 455,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/#about\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"About\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                        lineNumber: 456,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 456,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/#services\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"Services\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                        lineNumber: 457,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 457,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/gallery\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"Gallery\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 458,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                        href: \"/#contact\",\n                                                        className: \"hover:text-gold-400\",\n                                                        children: \"Contact\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 454,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 452,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"text-lg font-semibold text-gold-400 mb-4\",\n                                            children: \"Collections\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 463,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-forest-green-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Kitchen Reconstruction\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 465,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Modern Kitchen Designs\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Closet Organization\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Custom Storage Solutions\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Interior Styling\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                                    lineNumber: 469,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 462,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                            lineNumber: 438,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-forest-green-700 mt-8 pt-8 text-center text-forest-green-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"\\xa9 2024 Modern Haven Interior Design. All rights reserved.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 474,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                            lineNumber: 473,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                    lineNumber: 437,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                lineNumber: 436,\n                columnNumber: 7\n            }, this),\n            selectedImage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative max-w-4xl max-h-full bg-white rounded-lg overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: closeModal,\n                            className: \"absolute top-4 right-4 z-10 bg-forest-green-600 text-white rounded-full w-10 h-10 flex items-center justify-center hover:bg-forest-green-700 transition-colors\",\n                            children: \"✕\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                            lineNumber: 484,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                src: selectedImage.src,\n                                alt: selectedImage.alt,\n                                width: 1200,\n                                height: 800,\n                                className: \"w-full h-auto max-h-[80vh] object-contain\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                lineNumber: 493,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                            lineNumber: 492,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 bg-white\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-2xl font-bold text-forest-green-700 mb-2\",\n                                    children: selectedImage.title\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-forest-green-600\",\n                                    children: selectedImage.description\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                            lineNumber: 503,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                    lineNumber: 482,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n                lineNumber: 481,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Flame workspace\\\\morderm-haven\\\\src\\\\app\\\\gallery\\\\page.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n_s(Gallery, \"gNHFCSJ3h3sH8HFrpxN7hUqoxHs=\");\n_c = Gallery;\nvar _c;\n$RefreshReg$(_c, \"Gallery\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/gallery/page.tsx\n"));

/***/ })

});