# PowerShell script to convert HEIC files to JPG
Add-Type -AssemblyName System.Drawing

$heicFiles = Get-ChildItem -Path "public\images\kitchen-reconstruction" -Filter "*.HEIC"

foreach ($file in $heicFiles) {
    try {
        $outputPath = $file.FullName -replace '\.HEIC$', '.jpg'
        
        # Use magick (ImageMagick) if available, otherwise try Windows API
        if (Get-Command magick -ErrorAction SilentlyContinue) {
            & magick $file.FullName $outputPath
            Write-Host "Converted $($file.Name) to JPG using ImageMagick"
        } else {
            # Try using Windows built-in conversion
            $image = [System.Drawing.Image]::FromFile($file.FullName)
            $image.Save($outputPath, [System.Drawing.Imaging.ImageFormat]::Jpeg)
            $image.Dispose()
            Write-Host "Converted $($file.Name) to JPG using System.Drawing"
        }
    }
    catch {
        Write-Host "Failed to convert $($file.Name): $($_.Exception.Message)"
    }
}

Write-Host "Conversion complete!"
