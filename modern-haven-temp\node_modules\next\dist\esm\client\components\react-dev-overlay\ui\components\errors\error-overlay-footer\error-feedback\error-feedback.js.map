{"version": 3, "sources": ["../../../../../../../../../src/client/components/react-dev-overlay/ui/components/errors/error-overlay-footer/error-feedback/error-feedback.tsx"], "sourcesContent": ["import { useState, useCallback } from 'react'\nimport { ThumbsUp } from '../../../../icons/thumbs/thumbs-up'\nimport { ThumbsDown } from '../../../../icons/thumbs/thumbs-down'\nimport { cx } from '../../../../utils/cx'\n\ninterface ErrorFeedbackProps {\n  errorCode: string\n  className?: string\n}\nexport function ErrorFeedback({ errorCode, className }: ErrorFeedbackProps) {\n  const [votedMap, setVotedMap] = useState<Record<string, boolean>>({})\n  const voted = votedMap[errorCode]\n  const hasVoted = voted !== undefined\n  const disabled = process.env.__NEXT_TELEMETRY_DISABLED\n\n  const handleFeedback = useCallback(\n    async (wasHelpful: boolean) => {\n      // Optimistically set feedback state without loading/error states to keep implementation simple\n      setVotedMap((prev) => ({\n        ...prev,\n        [errorCode]: wasHelpful,\n      }))\n\n      try {\n        const response = await fetch(\n          `${process.env.__NEXT_ROUTER_BASEPATH || ''}/__nextjs_error_feedback?${new URLSearchParams(\n            {\n              errorCode,\n              wasHelpful: wasHelpful.toString(),\n            }\n          )}`\n        )\n\n        if (!response.ok) {\n          // Handle non-2xx HTTP responses here if needed\n          console.error('Failed to record feedback on the server.')\n        }\n      } catch (error) {\n        console.error('Failed to record feedback:', error)\n      }\n    },\n    [errorCode]\n  )\n\n  return (\n    <div\n      className={cx('error-feedback', className)}\n      role=\"region\"\n      aria-label=\"Error feedback\"\n    >\n      {hasVoted ? (\n        <p className=\"error-feedback-thanks\" role=\"status\" aria-live=\"polite\">\n          Thanks for your feedback!\n        </p>\n      ) : (\n        <>\n          <p>\n            <a\n              href=\"https://nextjs.org/telemetry#error-feedback\"\n              rel=\"noopener noreferrer\"\n              target=\"_blank\"\n            >\n              Was this helpful?\n            </a>\n          </p>\n          <button\n            aria-disabled={disabled ? 'true' : undefined}\n            aria-label=\"Mark as helpful\"\n            onClick={disabled ? undefined : () => handleFeedback(true)}\n            className={cx('feedback-button', voted === true && 'voted')}\n            title={\n              disabled\n                ? 'Feedback disabled due to setting NEXT_TELEMETRY_DISABLED'\n                : undefined\n            }\n            type=\"button\"\n          >\n            <ThumbsUp aria-hidden=\"true\" />\n          </button>\n          <button\n            aria-disabled={disabled ? 'true' : undefined}\n            aria-label=\"Mark as not helpful\"\n            onClick={disabled ? undefined : () => handleFeedback(false)}\n            className={cx('feedback-button', voted === false && 'voted')}\n            title={\n              disabled\n                ? 'Feedback disabled due to setting NEXT_TELEMETRY_DISABLED'\n                : undefined\n            }\n            type=\"button\"\n          >\n            <ThumbsDown\n              aria-hidden=\"true\"\n              // Optical alignment\n              style={{\n                translate: '1px 1px',\n              }}\n            />\n          </button>\n        </>\n      )}\n    </div>\n  )\n}\n\nexport const styles = `\n  .error-feedback {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    white-space: nowrap;\n    color: var(--color-gray-900);\n  }\n\n  .error-feedback-thanks {\n    height: var(--size-24);\n    display: flex;\n    align-items: center;\n    padding-right: 4px; /* To match the 4px inner padding of the thumbs up and down icons */\n  }\n\n  .feedback-button {\n    background: none;\n    border: none;\n    border-radius: var(--rounded-md);\n    width: var(--size-24);\n    height: var(--size-24);\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    cursor: pointer;\n\n    &:focus {\n      outline: var(--focus-ring);\n    }\n\n    &:hover {\n      background: var(--color-gray-alpha-100);\n    }\n\n    &:active {\n      background: var(--color-gray-alpha-200);\n    }\n  }\n\n  .feedback-button[aria-disabled='true'] {\n    opacity: 0.7;\n    cursor: not-allowed;\n  }\n\n  .feedback-button.voted {\n    background: var(--color-gray-alpha-200);\n  }\n\n  .thumbs-up-icon,\n  .thumbs-down-icon {\n    color: var(--color-gray-900);\n    width: var(--size-16);\n    height: var(--size-16);\n  }\n`\n"], "names": ["useState", "useCallback", "ThumbsUp", "ThumbsDown", "cx", "ErrorFeedback", "errorCode", "className", "votedMap", "setVotedMap", "voted", "hasVoted", "undefined", "disabled", "process", "env", "__NEXT_TELEMETRY_DISABLED", "handleFeedback", "wasHelpful", "prev", "response", "fetch", "__NEXT_ROUTER_BASEPATH", "URLSearchParams", "toString", "ok", "console", "error", "div", "role", "aria-label", "p", "aria-live", "a", "href", "rel", "target", "button", "aria-disabled", "onClick", "title", "type", "aria-hidden", "style", "translate", "styles"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,WAAW,QAAQ,QAAO;AAC7C,SAASC,QAAQ,QAAQ,qCAAoC;AAC7D,SAASC,UAAU,QAAQ,uCAAsC;AACjE,SAASC,EAAE,QAAQ,uBAAsB;AAMzC,OAAO,SAASC,cAAc,KAA4C;IAA5C,IAAA,EAAEC,SAAS,EAAEC,SAAS,EAAsB,GAA5C;IAC5B,MAAM,CAACC,UAAUC,YAAY,GAAGT,SAAkC,CAAC;IACnE,MAAMU,QAAQF,QAAQ,CAACF,UAAU;IACjC,MAAMK,WAAWD,UAAUE;IAC3B,MAAMC,WAAWC,QAAQC,GAAG,CAACC,yBAAyB;IAEtD,MAAMC,iBAAiBhB,YACrB,OAAOiB;QACL,+FAA+F;QAC/FT,YAAY,CAACU,OAAU,CAAA;gBACrB,GAAGA,IAAI;gBACP,CAACb,UAAU,EAAEY;YACf,CAAA;QAEA,IAAI;YACF,MAAME,WAAW,MAAMC,MACrB,AAAGP,CAAAA,QAAQC,GAAG,CAACO,sBAAsB,IAAI,EAAC,IAAE,8BAA2B,IAAIC,gBACzE;gBACEjB;gBACAY,YAAYA,WAAWM,QAAQ;YACjC;YAIJ,IAAI,CAACJ,SAASK,EAAE,EAAE;gBAChB,+CAA+C;gBAC/CC,QAAQC,KAAK,CAAC;YAChB;QACF,EAAE,OAAOA,OAAO;YACdD,QAAQC,KAAK,CAAC,8BAA8BA;QAC9C;IACF,GACA;QAACrB;KAAU;IAGb,qBACE,KAACsB;QACCrB,WAAWH,GAAG,kBAAkBG;QAChCsB,MAAK;QACLC,cAAW;kBAEVnB,yBACC,KAACoB;YAAExB,WAAU;YAAwBsB,MAAK;YAASG,aAAU;sBAAS;2BAItE;;8BACE,KAACD;8BACC,cAAA,KAACE;wBACCC,MAAK;wBACLC,KAAI;wBACJC,QAAO;kCACR;;;8BAIH,KAACC;oBACCC,iBAAezB,WAAW,SAASD;oBACnCkB,cAAW;oBACXS,SAAS1B,WAAWD,YAAY,IAAMK,eAAe;oBACrDV,WAAWH,GAAG,mBAAmBM,UAAU,QAAQ;oBACnD8B,OACE3B,WACI,6DACAD;oBAEN6B,MAAK;8BAEL,cAAA,KAACvC;wBAASwC,eAAY;;;8BAExB,KAACL;oBACCC,iBAAezB,WAAW,SAASD;oBACnCkB,cAAW;oBACXS,SAAS1B,WAAWD,YAAY,IAAMK,eAAe;oBACrDV,WAAWH,GAAG,mBAAmBM,UAAU,SAAS;oBACpD8B,OACE3B,WACI,6DACAD;oBAEN6B,MAAK;8BAEL,cAAA,KAACtC;wBACCuC,eAAY;wBACZ,oBAAoB;wBACpBC,OAAO;4BACLC,WAAW;wBACb;;;;;;AAOd;AAEA,OAAO,MAAMC,SAAU,ynCAuDtB"}