import type { ModelData } from "./model-data.js";
export declare const adapters: (model: ModelData) => string[];
export declare const allennlp: (model: ModelData) => string[];
export declare const araclip: (model: ModelData) => string[];
export declare const asteroid: (model: ModelData) => string[];
export declare const audioseal: (model: ModelData) => string[];
export declare const ben2: (model: ModelData) => string[];
export declare const bertopic: (model: ModelData) => string[];
export declare const bm25s: (model: ModelData) => string[];
export declare const cxr_foundation: () => string[];
export declare const depth_anything_v2: (model: ModelData) => string[];
export declare const depth_pro: (model: ModelData) => string[];
export declare const derm_foundation: () => string[];
export declare const dia: (model: ModelData) => string[];
export declare const describe_anything: (model: ModelData) => string[];
export declare const diffusers: (model: ModelData) => string[];
export declare const diffusionkit: (model: ModelData) => string[];
export declare const cartesia_pytorch: (model: ModelData) => string[];
export declare const cartesia_mlx: (model: ModelData) => string[];
export declare const edsnlp: (model: ModelData) => string[];
export declare const espnetTTS: (model: ModelData) => string[];
export declare const espnetASR: (model: ModelData) => string[];
export declare const espnet: (model: ModelData) => string[];
export declare const fairseq: (model: ModelData) => string[];
export declare const flair: (model: ModelData) => string[];
export declare const gliner: (model: ModelData) => string[];
export declare const indextts: (model: ModelData) => string[];
export declare const htrflow: (model: ModelData) => string[];
export declare const keras: (model: ModelData) => string[];
export declare const keras_hub: (model: ModelData) => string[];
export declare const kimi_audio: (model: ModelData) => string[];
export declare const lightning_ir: (model: ModelData) => string[];
export declare const llama_cpp_python: (model: ModelData) => string[];
export declare const tf_keras: (model: ModelData) => string[];
export declare const mamba_ssm: (model: ModelData) => string[];
export declare const mars5_tts: (model: ModelData) => string[];
export declare const matanyone: (model: ModelData) => string[];
export declare const mesh_anything: () => string[];
export declare const open_clip: (model: ModelData) => string[];
export declare const paddlenlp: (model: ModelData) => string[];
export declare const perception_encoder: (model: ModelData) => string[];
export declare const phantom_wan: (model: ModelData) => string[];
export declare const pyannote_audio_pipeline: (model: ModelData) => string[];
export declare const pyannote_audio: (model: ModelData) => string[];
export declare const relik: (model: ModelData) => string[];
export declare const tensorflowtts: (model: ModelData) => string[];
export declare const timm: (model: ModelData) => string[];
export declare const saelens: () => string[];
export declare const seed_story: () => string[];
export declare const sklearn: (model: ModelData) => string[];
export declare const stable_audio_tools: (model: ModelData) => string[];
export declare const fastai: (model: ModelData) => string[];
export declare const sam2: (model: ModelData) => string[];
export declare const sampleFactory: (model: ModelData) => string[];
export declare const sentenceTransformers: (model: ModelData) => string[];
export declare const setfit: (model: ModelData) => string[];
export declare const spacy: (model: ModelData) => string[];
export declare const span_marker: (model: ModelData) => string[];
export declare const stanza: (model: ModelData) => string[];
export declare const speechbrain: (model: ModelData) => string[];
export declare const terratorch: (model: ModelData) => string[];
export declare const transformers: (model: ModelData) => string[];
export declare const transformersJS: (model: ModelData) => string[];
export declare const peft: (model: ModelData) => string[];
export declare const fasttext: (model: ModelData) => string[];
export declare const stableBaselines3: (model: ModelData) => string[];
export declare const mlAgents: (model: ModelData) => string[];
export declare const sentis: () => string[];
export declare const sana: (model: ModelData) => string[];
export declare const vfimamba: (model: ModelData) => string[];
export declare const voicecraft: (model: ModelData) => string[];
export declare const chattts: () => string[];
export declare const ultralytics: (model: ModelData) => string[];
export declare const birefnet: (model: ModelData) => string[];
export declare const swarmformer: (model: ModelData) => string[];
export declare const mlxim: (model: ModelData) => string[];
export declare const mlx: (model: ModelData) => string[];
export declare const model2vec: (model: ModelData) => string[];
export declare const nemo: (model: ModelData) => string[];
export declare const outetts: (model: ModelData) => string[];
export declare const pxia: (model: ModelData) => string[];
export declare const pythae: (model: ModelData) => string[];
export declare const anemoi: (model: ModelData) => string[];
export declare const audiocraft: (model: ModelData) => string[];
export declare const whisperkit: () => string[];
export declare const threedtopia_xl: (model: ModelData) => string[];
export declare const hezar: (model: ModelData) => string[];
export declare const zonos: (model: ModelData) => string[];
//# sourceMappingURL=model-libraries-snippets.d.ts.map