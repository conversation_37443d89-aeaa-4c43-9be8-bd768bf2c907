"use client";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";

interface GeneratedContent {
  url?: string;
  textDescription?: string;
  prompt: string;
  timestamp: number;
  message?: string;
}

export default function AIDesign() {
  const [prompt, setPrompt] = useState("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedContent, setGeneratedContent] = useState<GeneratedContent[]>([]);
  const [error, setError] = useState("");
  const [suggestion, setSuggestion] = useState("");

  // Interior design keywords to validate prompts
  const interiorKeywords = [
    "kitchen", "bedroom", "living room", "bathroom", "dining room", "office", "closet",
    "interior", "home", "house", "apartment", "room", "space", "design", "furniture",
    "cabinet", "counter", "sofa", "chair", "table", "bed", "shelf", "storage",
    "modern", "contemporary", "traditional", "minimalist", "luxury", "cozy",
    "lighting", "window", "wall", "floor", "ceiling", "decor", "renovation"
  ];

  const validatePrompt = (inputPrompt: string): boolean => {
    const lowerPrompt = inputPrompt.toLowerCase();
    return interiorKeywords.some(keyword => lowerPrompt.includes(keyword));
  };

  const enhancePrompt = (userPrompt: string): string => {
    // Add interior design context to ensure relevant results
    const baseContext = "Interior design, home decor, architectural photography, professional lighting, high quality, detailed, ";

    // Check if prompt already contains interior context
    const lowerPrompt = userPrompt.toLowerCase();
    if (lowerPrompt.includes("interior") || lowerPrompt.includes("room") || lowerPrompt.includes("kitchen") || lowerPrompt.includes("bedroom")) {
      return `${baseContext}${userPrompt}`;
    }

    return `${baseContext}modern ${userPrompt} interior design`;
  };

  const generateImage = async () => {
    if (!prompt.trim()) {
      setError("Please enter a design description");
      return;
    }

    if (!validatePrompt(prompt)) {
      setError("Please describe an interior design or home-related concept (e.g., 'modern kitchen', 'cozy bedroom', 'minimalist living room')");
      return;
    }

    setIsGenerating(true);
    setError("");
    setSuggestion("");

    try {
      const enhancedPrompt = enhancePrompt(prompt);

      const response = await fetch('/api/generate-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ prompt: enhancedPrompt }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate image');
      }

      const data = await response.json();

      const newContent: GeneratedContent = {
        url: data.imageUrl,
        textDescription: data.textDescription,
        prompt: prompt,
        timestamp: Date.now(),
        message: data.message,
      };

      setGeneratedContent(prev => [newContent, ...prev]);
      setPrompt("");
    } catch (err: any) {
      // Parse error response
      let errorMessage = "Failed to generate image. Please try again.";
      let suggestionMessage = "";

      if (err.response?.data) {
        errorMessage = err.response.data.error || errorMessage;
        suggestionMessage = err.response.data.suggestion || "";
      } else if (err.message) {
        errorMessage = err.message;
      }

      setError(errorMessage);
      setSuggestion(suggestionMessage);
      console.error("Error generating image:", err);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !isGenerating) {
      generateImage();
    }
  };

  const samplePrompts = [
    "Modern minimalist kitchen with white cabinets and marble countertops",
    "Cozy bedroom with warm lighting and natural wood furniture",
    "Contemporary living room with large windows and neutral colors",
    "Luxury bathroom with marble tiles and gold fixtures",
    "Scandinavian dining room with wooden table and pendant lights",
    "Walk-in closet with organized storage and elegant lighting"
  ];

  return (
    <main className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b border-forest-green-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/" className="text-2xl font-bold text-forest-green-600">Modern Haven</Link>
            </div>
            <div className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-4">
                <Link href="/" className="text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium">Home</Link>
                <Link href="/#about" className="text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium">About</Link>
                <Link href="/#services" className="text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium">Services</Link>
                <Link href="/gallery" className="text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium">Gallery</Link>
                <Link href="/ai-design" className="bg-gold-500 text-white px-3 py-2 rounded-md text-sm font-medium">AI Design</Link>
                <Link href="/#contact" className="text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium">Contact</Link>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-forest-green-50 to-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-forest-green-700 mb-6">
              AI <span className="text-gold-500">Design Studio</span>
              <span className="text-sm bg-blue-100 text-blue-800 px-2 py-1 rounded-full ml-3">Powered by Gemini 2.0</span>
            </h1>
            <p className="text-xl text-forest-green-600 mb-8 max-w-3xl mx-auto">
              Bring your interior design ideas to life with Google's Gemini 2.0 Flash AI.
              Describe your vision and get detailed design concepts and inspiration.
            </p>
          </div>
        </div>
      </section>

      {/* AI Design Generator - Two Column Layout */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">

            {/* Left Column - Input and Controls */}
            <div className="space-y-8">
              {/* Input Section */}
              <div className="bg-forest-green-50 rounded-lg p-8">
                <h2 className="text-2xl font-bold text-forest-green-700 mb-6">
                  Create Your Design
                </h2>

                <div className="space-y-4">
                  <div>
                    <label htmlFor="prompt" className="block text-sm font-medium text-forest-green-700 mb-2">
                      Describe your interior design vision
                    </label>
                    <textarea
                      id="prompt"
                      value={prompt}
                      onChange={(e) => setPrompt(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder="e.g., Modern kitchen with white cabinets, marble countertops, and stainless steel appliances..."
                      className="w-full px-4 py-3 border border-forest-green-200 rounded-lg focus:ring-2 focus:ring-gold-500 focus:border-transparent resize-none"
                      rows={6}
                      disabled={isGenerating}
                    />
                  </div>

                  {error && (
                    <div className="text-red-600 text-sm bg-red-50 p-3 rounded-lg">
                      <p className="font-medium">{error}</p>
                      {suggestion && (
                        <p className="mt-2 text-blue-600 bg-blue-50 p-2 rounded border-l-4 border-blue-400">
                          💡 <strong>Suggestion:</strong> {suggestion}
                        </p>
                      )}
                    </div>
                  )}

                  <button
                    onClick={generateImage}
                    disabled={isGenerating || !prompt.trim()}
                    className="w-full bg-gold-500 text-white py-3 px-6 rounded-lg font-semibold hover:bg-gold-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition duration-300"
                  >
                    {isGenerating ? (
                      <div className="flex items-center justify-center">
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                        Generating Design...
                      </div>
                    ) : (
                      "Generate Design"
                    )}
                  </button>
                </div>
              </div>

              {/* Sample Prompts */}
              <div className="bg-white border border-forest-green-200 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-forest-green-700 mb-4">
                  💡 Try these sample prompts:
                </h3>
                <div className="space-y-2">
                  {samplePrompts.map((samplePrompt, index) => (
                    <button
                      key={index}
                      onClick={() => setPrompt(samplePrompt)}
                      className="w-full text-left p-3 bg-forest-green-50 border border-forest-green-200 rounded-lg hover:border-gold-500 hover:bg-gold-50 transition duration-200"
                      disabled={isGenerating}
                    >
                      <span className="text-sm text-forest-green-600">{samplePrompt}</span>
                    </button>
                  ))}
                </div>
              </div>

              {/* Tips Section */}
              <div className="bg-forest-green-600 rounded-lg p-6 text-white">
                <h3 className="text-lg font-bold mb-4">🎯 Tips for Better Results with Gemini AI</h3>
                <ul className="space-y-2 text-forest-green-100 text-sm">
                  <li>• Be specific about room types (kitchen, bedroom, living room, etc.)</li>
                  <li>• Include style preferences (modern, traditional, minimalist, luxury)</li>
                  <li>• Mention colors, materials, and lighting preferences</li>
                  <li>• Describe the mood you want to create (cozy, elegant, bright, etc.)</li>
                  <li>• Include furniture and decor elements you envision</li>
                  <li>• Gemini will provide detailed descriptions you can use for inspiration</li>
                </ul>
              </div>
            </div>

            {/* Right Column - Generated Images */}
            <div className="space-y-6">
              <div className="bg-forest-green-50 rounded-lg p-6">
                <h3 className="text-2xl font-bold text-forest-green-700 mb-6">
                  {generatedContent.length > 0 ? 'Your Generated Designs' : 'Generated Designs Will Appear Here'}
                </h3>

                {generatedContent.length === 0 ? (
                  <div className="text-center py-12">
                    <div className="text-6xl mb-4">🎨</div>
                    <p className="text-forest-green-600 mb-4">
                      Start by describing your interior design vision in the left panel.
                    </p>
                    <p className="text-sm text-forest-green-500">
                      Your AI-generated designs will appear here once you click "Generate Design".
                    </p>
                  </div>
                ) : (
                  <div className="space-y-6 max-h-[800px] overflow-y-auto">
                    {generatedContent.map((content, index) => (
                      <div key={index} className="bg-white rounded-lg shadow-lg overflow-hidden">
                        {content.url ? (
                          // Display image if available
                          <div className="relative h-64">
                            <Image
                              src={content.url}
                              alt={`Generated design: ${content.prompt}`}
                              fill
                              className="object-cover"
                            />
                          </div>
                        ) : (
                          // Display text description from Gemini
                          <div className="p-6 bg-gradient-to-br from-gold-50 to-forest-green-50">
                            <div className="text-4xl mb-4 text-center">🏠✨</div>
                            <h4 className="text-lg font-semibold text-forest-green-700 mb-3">
                              AI Design Concept
                            </h4>
                            <div className="text-forest-green-600 text-sm leading-relaxed">
                              {content.textDescription}
                            </div>
                            {content.message && (
                              <div className="mt-3 p-3 bg-blue-50 border-l-4 border-blue-400 rounded">
                                <p className="text-blue-700 text-sm">
                                  💡 {content.message}
                                </p>
                              </div>
                            )}
                          </div>
                        )}
                        <div className="p-4">
                          <p className="text-forest-green-600 text-sm mb-2">
                            <strong>Prompt:</strong> {content.prompt}
                          </p>
                          <p className="text-gray-500 text-xs mb-3">
                            Generated on {new Date(content.timestamp).toLocaleString()}
                          </p>
                          <div className="flex space-x-2">
                            {content.url && (
                              <button
                                onClick={() => {
                                  const link = document.createElement('a');
                                  link.href = content.url!;
                                  link.download = `modern-haven-design-${content.timestamp}.png`;
                                  link.click();
                                }}
                                className="text-xs bg-gold-500 text-white px-3 py-1 rounded hover:bg-gold-600 transition duration-200"
                              >
                                Download Image
                              </button>
                            )}
                            {content.textDescription && (
                              <button
                                onClick={() => {
                                  navigator.clipboard.writeText(content.textDescription!);
                                  alert('Description copied to clipboard!');
                                }}
                                className="text-xs bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600 transition duration-200"
                              >
                                Copy Description
                              </button>
                            )}
                            <button
                              onClick={() => setPrompt(content.prompt)}
                              className="text-xs bg-forest-green-600 text-white px-3 py-1 rounded hover:bg-forest-green-700 transition duration-200"
                            >
                              Use Prompt
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-forest-green-800 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <h3 className="text-2xl font-bold text-gold-400 mb-4">Modern Haven</h3>
              <p className="text-forest-green-200 mb-4">
                Creating beautiful, functional spaces that reflect your unique style and enhance your lifestyle.
                Your dream home is just a consultation away.
              </p>
              <div className="flex space-x-4">
                <a href="#" className="text-gold-400 hover:text-gold-300">📘</a>
                <a href="#" className="text-gold-400 hover:text-gold-300">📷</a>
                <a href="#" className="text-gold-400 hover:text-gold-300">🐦</a>
                <a href="#" className="text-gold-400 hover:text-gold-300">💼</a>
              </div>
            </div>
            <div>
              <h4 className="text-lg font-semibold text-gold-400 mb-4">Quick Links</h4>
              <ul className="space-y-2 text-forest-green-200">
                <li><Link href="/" className="hover:text-gold-400">Home</Link></li>
                <li><Link href="/#about" className="hover:text-gold-400">About</Link></li>
                <li><Link href="/#services" className="hover:text-gold-400">Services</Link></li>
                <li><Link href="/gallery" className="hover:text-gold-400">Gallery</Link></li>
                <li><Link href="/ai-design" className="hover:text-gold-400">AI Design</Link></li>
                <li><Link href="/#contact" className="hover:text-gold-400">Contact</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold text-gold-400 mb-4">AI Features</h4>
              <ul className="space-y-2 text-forest-green-200">
                <li>Interior Design Generation</li>
                <li>Room Visualization</li>
                <li>Style Exploration</li>
                <li>Design Inspiration</li>
                <li>Custom Concepts</li>
              </ul>
            </div>
          </div>
          <div className="border-t border-forest-green-700 mt-8 pt-8 text-center text-forest-green-200">
            <p>&copy; 2024 Modern Haven Interior Design. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </main>
  );
}
