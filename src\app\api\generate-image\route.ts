import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

export async function POST(request: NextRequest) {
  try {
    const { prompt, uploadedImage } = await request.json();

    if (!prompt) {
      return NextResponse.json(
        { error: 'Prompt is required' },
        { status: 400 }
      );
    }

    // Get the Google AI API key from environment variables
    const apiKey = process.env.GOOGLE_AI_API_KEY;

    if (!apiKey) {
      console.error('GOOGLE_AI_API_KEY not found in environment variables');
      return NextResponse.json(
        { error: 'API configuration error' },
        { status: 500 }
      );
    }

    // Interior design validation - ensure prompt is related to interior design
    const interiorKeywords = [
      'kitchen', 'bedroom', 'living room', 'bathroom', 'dining room', 'office', 'closet',
      'interior', 'home', 'house', 'apartment', 'room', 'space', 'design', 'furniture',
      'cabinet', 'counter', 'sofa', 'chair', 'table', 'bed', 'shelf', 'storage',
      'modern', 'contemporary', 'traditional', 'minimalist', 'luxury', 'cozy',
      'lighting', 'window', 'wall', 'floor', 'ceiling', 'decor', 'renovation'
    ];

    const lowerPrompt = prompt.toLowerCase();
    const isInteriorRelated = interiorKeywords.some(keyword => lowerPrompt.includes(keyword));

    if (!isInteriorRelated) {
      return NextResponse.json(
        { error: 'Please provide an interior design or home-related prompt' },
        { status: 400 }
      );
    }

    // Initialize Google AI client
    const genAI = new GoogleGenerativeAI(apiKey);

    try {
      // Use Gemini 2.0 Flash Preview Image Generation
      const model = genAI.getGenerativeModel({ model: 'gemini-2.0-flash-preview-image-generation' });

      // Enhanced prompt for better interior design results
      const enhancedPrompt = `Create a high-quality, professional interior design image: ${prompt}. Style: photorealistic, architectural photography, professional lighting, high resolution, detailed textures, modern design aesthetic.`;

      console.log('Generating image with Gemini 2.0 Flash Preview Image Generation...');

      // Prepare the content array
      const contentParts = [{ text: enhancedPrompt }];

      // Add uploaded image if provided
      if (uploadedImage) {
        contentParts.push({
          inlineData: {
            mimeType: 'image/jpeg', // or detect from uploadedImage
            data: uploadedImage.split(',')[1] // Remove data:image/jpeg;base64, prefix
          }
        });
      }

      const result = await model.generateContent(contentParts);
      const response = await result.response;

      // Check if the response contains an image
      if (response.candidates && response.candidates[0]) {
        const candidate = response.candidates[0];

        // Look for image data in the response
        if (candidate.content && candidate.content.parts) {
          for (const part of candidate.content.parts) {
            if (part.inlineData && part.inlineData.data) {
              // Found generated image
              const imageData = part.inlineData.data;
              const mimeType = part.inlineData.mimeType || 'image/png';
              const imageUrl = `data:${mimeType};base64,${imageData}`;

              return NextResponse.json({
                imageUrl,
                prompt,
                timestamp: Date.now(),
                message: 'Successfully generated interior design image'
              });
            }
          }
        }
      }

      // If no image was generated, return text description
      const text = response.text();

      // Try to use Gemini's image generation capabilities
      // Note: Gemini 2.0 Flash Preview may have limited image generation
      // Let's implement a proper image generation request

      try {
        // For Gemini image generation, we need to use a different approach
        // Since Gemini 2.0 Flash Preview might not support direct image generation yet,
        // let's create a comprehensive text description and use that for now

        const imageDescription = text;

        // Create a placeholder image with the description
        // In a real implementation, you would use Gemini's actual image generation API
        // For now, we'll return the description and suggest using it with other tools

        return NextResponse.json({
          textDescription: imageDescription,
          prompt: enhancedPrompt,
          timestamp: Date.now(),
          message: 'Gemini 2.0 Flash has generated a detailed description of your interior design concept.',
          suggestion: 'Use this description with image generation tools or as inspiration for your design project.'
        });

      } catch (imageError: any) {
        console.log('Image generation not available, returning text description');

        return NextResponse.json({
          textDescription: text,
          prompt: enhancedPrompt,
          timestamp: Date.now(),
          message: 'Generated detailed interior design description using Gemini 2.0 Flash',
        });
      }

    } catch (geminiError: any) {
      console.error('Google AI API error:', geminiError);

      if (geminiError.message?.includes('quota') || geminiError.message?.includes('exceeded')) {
        return NextResponse.json(
          {
            error: 'Google AI API quota exceeded. Please check your API usage or upgrade your plan.',
            suggestion: 'Visit Google AI Studio to check your API quota and usage limits.'
          },
          { status: 402 }
        );
      }

      if (geminiError.message?.includes('invalid') || geminiError.message?.includes('key')) {
        return NextResponse.json(
          { error: 'Invalid Google AI API key. Please check your configuration.' },
          { status: 401 }
        );
      }

      if (geminiError.message?.includes('rate limit')) {
        return NextResponse.json(
          { error: 'Rate limit exceeded. Please wait a moment before trying again.' },
          { status: 429 }
        );
      }

      return NextResponse.json(
        { error: `Failed to generate content: ${geminiError.message || 'Unknown error'}` },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in generate-image API:', error);
    return NextResponse.json(
      { error: 'Internal server error. Please try again.' },
      { status: 500 }
    );
  }
}
