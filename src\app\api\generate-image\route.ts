import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { prompt } = await request.json();

    if (!prompt) {
      return NextResponse.json(
        { error: 'Prompt is required' },
        { status: 400 }
      );
    }

    // Get the API key from environment variables
    const apiKey = process.env.HUGGINGFACE_API_KEY;

    if (!apiKey) {
      console.error('HUGGINGFACE_API_KEY not found in environment variables');
      return NextResponse.json(
        { error: 'API configuration error' },
        { status: 500 }
      );
    }

    // Interior design validation - ensure prompt is related to interior design
    const interiorKeywords = [
      'kitchen', 'bedroom', 'living room', 'bathroom', 'dining room', 'office', 'closet',
      'interior', 'home', 'house', 'apartment', 'room', 'space', 'design', 'furniture',
      'cabinet', 'counter', 'sofa', 'chair', 'table', 'bed', 'shelf', 'storage',
      'modern', 'contemporary', 'traditional', 'minimalist', 'luxury', 'cozy',
      'lighting', 'window', 'wall', 'floor', 'ceiling', 'decor', 'renovation'
    ];

    const lowerPrompt = prompt.toLowerCase();
    const isInteriorRelated = interiorKeywords.some(keyword => lowerPrompt.includes(keyword));

    if (!isInteriorRelated) {
      return NextResponse.json(
        { error: 'Please provide an interior design or home-related prompt' },
        { status: 400 }
      );
    }

    // Use Hugging Face Inference API with FLUX.1-dev model
    const response = await fetch(
      'https://api-inference.huggingface.co/models/black-forest-labs/FLUX.1-dev',
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          inputs: prompt,
          parameters: {
            guidance_scale: 7.5,
            num_inference_steps: 28,
            width: 1024,
            height: 1024,
            seed: Math.floor(Math.random() * 1000000),
          },
        }),
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Hugging Face API error:', response.status, errorText);

      if (response.status === 503) {
        return NextResponse.json(
          { error: 'Model is currently loading. Please try again in a few moments.' },
          { status: 503 }
        );
      }

      if (response.status === 429) {
        return NextResponse.json(
          { error: 'Rate limit exceeded. Please wait a moment before trying again.' },
          { status: 429 }
        );
      }

      return NextResponse.json(
        { error: 'Failed to generate image. Please try again.' },
        { status: response.status }
      );
    }

    // Get the image as a blob
    const imageBlob = await response.blob();

    // Convert blob to base64 for sending to client
    const arrayBuffer = await imageBlob.arrayBuffer();
    const base64 = Buffer.from(arrayBuffer).toString('base64');
    const imageUrl = `data:image/png;base64,${base64}`;

    return NextResponse.json({
      imageUrl,
      prompt,
      timestamp: Date.now(),
    });

  } catch (error) {
    console.error('Error in generate-image API:', error);
    return NextResponse.json(
      { error: 'Internal server error. Please try again.' },
      { status: 500 }
    );
  }
}
