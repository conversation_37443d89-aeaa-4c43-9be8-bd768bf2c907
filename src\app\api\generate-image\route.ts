import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

export async function POST(request: NextRequest) {
  try {
    const { prompt, uploadedImage } = await request.json();

    if (!prompt) {
      return NextResponse.json(
        { error: 'Prompt is required' },
        { status: 400 }
      );
    }

    // Get the Google AI API key from environment variables
    const apiKey = process.env.GOOGLE_AI_API_KEY;

    if (!apiKey) {
      console.error('GOOGLE_AI_API_KEY not found in environment variables');
      return NextResponse.json(
        { error: 'API configuration error' },
        { status: 500 }
      );
    }

    // Interior design validation - ensure prompt is related to interior design
    const interiorKeywords = [
      'kitchen', 'bedroom', 'living room', 'bathroom', 'dining room', 'office', 'closet',
      'interior', 'home', 'house', 'apartment', 'room', 'space', 'design', 'furniture',
      'cabinet', 'counter', 'sofa', 'chair', 'table', 'bed', 'shelf', 'storage',
      'modern', 'contemporary', 'traditional', 'minimalist', 'luxury', 'cozy',
      'lighting', 'window', 'wall', 'floor', 'ceiling', 'decor', 'renovation'
    ];

    const lowerPrompt = prompt.toLowerCase();
    const isInteriorRelated = interiorKeywords.some(keyword => lowerPrompt.includes(keyword));

    if (!isInteriorRelated) {
      return NextResponse.json(
        { error: 'Please provide an interior design or home-related prompt' },
        { status: 400 }
      );
    }

    // Initialize Google AI client
    const genAI = new GoogleGenerativeAI(apiKey);

    try {
      // Use Gemini 2.0 Flash Preview Image Generation
      const model = genAI.getGenerativeModel({
        model: 'gemini-2.0-flash-preview-image-generation'
      });

      // Enhanced prompt for better interior design results
      const enhancedPrompt = `Create a high-quality, professional interior design image: ${prompt}. Style: photorealistic, architectural photography, professional lighting, high resolution, detailed textures, modern design aesthetic.`;

      console.log('Generating image with Gemini 2.0 Flash Preview Image Generation...');

      // Prepare the content array
      const contentParts = [{ text: enhancedPrompt }];

      // Add uploaded image if provided
      if (uploadedImage) {
        contentParts.push({
          inlineData: {
            mimeType: 'image/jpeg', // or detect from uploadedImage
            data: uploadedImage.split(',')[1] // Remove data:image/jpeg;base64, prefix
          }
        });
      }

      // For image generation models, we need to specify that we want both image and text
      const result = await model.generateContent({
        contents: [{
          role: 'user',
          parts: contentParts
        }]
      });
      const response = await result.response;

      // Check if response has candidates with parts
      if (response.candidates && response.candidates[0] && response.candidates[0].content) {
        const parts = response.candidates[0].content.parts;
        let imageData = null;
        let textDescription = '';

        // Look through all parts for image and text
        for (const part of parts) {
          if (part.inlineData && part.inlineData.mimeType?.startsWith('image/')) {
            // Found image data
            imageData = part.inlineData.data;
          } else if (part.text) {
            // Found text description
            textDescription += part.text;
          }
        }

        if (imageData) {
          // Successfully generated image
          const imageUrl = `data:image/png;base64,${imageData}`;

          return NextResponse.json({
            imageUrl,
            textDescription: textDescription || 'Generated interior design image',
            prompt,
            timestamp: Date.now(),
            message: 'Successfully generated interior design image'
          });
        } else if (textDescription) {
          // Got text description only
          return NextResponse.json({
            textDescription,
            prompt,
            timestamp: Date.now(),
            message: 'Generated detailed interior design description'
          });
        }
      }

      // Fallback: try to get text response
      const text = response.text();
      return NextResponse.json({
        textDescription: text,
        prompt,
        timestamp: Date.now(),
        message: 'Generated interior design concept'
      });

    } catch (geminiError: any) {
      console.error('Google AI API error:', geminiError);

      if (geminiError.message?.includes('quota') || geminiError.message?.includes('exceeded')) {
        return NextResponse.json(
          {
            error: 'Google AI API quota exceeded. Please check your API usage or upgrade your plan.',
            suggestion: 'Visit Google AI Studio to check your API quota and usage limits.'
          },
          { status: 402 }
        );
      }

      if (geminiError.message?.includes('invalid') || geminiError.message?.includes('key')) {
        return NextResponse.json(
          { error: 'Invalid Google AI API key. Please check your configuration.' },
          { status: 401 }
        );
      }

      if (geminiError.message?.includes('rate limit')) {
        return NextResponse.json(
          { error: 'Rate limit exceeded. Please wait a moment before trying again.' },
          { status: 429 }
        );
      }

      return NextResponse.json(
        { error: `Failed to generate content: ${geminiError.message || 'Unknown error'}` },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in generate-image API:', error);
    return NextResponse.json(
      { error: 'Internal server error. Please try again.' },
      { status: 500 }
    );
  }
}
