import { NextRequest, NextResponse } from 'next/server';
import { HfInference } from '@huggingface/inference';

export async function POST(request: NextRequest) {
  try {
    const { prompt } = await request.json();

    if (!prompt) {
      return NextResponse.json(
        { error: 'Prompt is required' },
        { status: 400 }
      );
    }

    // Get the API key from environment variables
    const apiKey = process.env.HUGGINGFACE_API_KEY;

    if (!apiKey) {
      console.error('HUGGINGFACE_API_KEY not found in environment variables');
      return NextResponse.json(
        { error: 'API configuration error' },
        { status: 500 }
      );
    }

    // Interior design validation - ensure prompt is related to interior design
    const interiorKeywords = [
      'kitchen', 'bedroom', 'living room', 'bathroom', 'dining room', 'office', 'closet',
      'interior', 'home', 'house', 'apartment', 'room', 'space', 'design', 'furniture',
      'cabinet', 'counter', 'sofa', 'chair', 'table', 'bed', 'shelf', 'storage',
      'modern', 'contemporary', 'traditional', 'minimalist', 'luxury', 'cozy',
      'lighting', 'window', 'wall', 'floor', 'ceiling', 'decor', 'renovation'
    ];

    const lowerPrompt = prompt.toLowerCase();
    const isInteriorRelated = interiorKeywords.some(keyword => lowerPrompt.includes(keyword));

    if (!isInteriorRelated) {
      return NextResponse.json(
        { error: 'Please provide an interior design or home-related prompt' },
        { status: 400 }
      );
    }

    // Initialize Hugging Face Inference Client
    const hf = new HfInference(apiKey);

    try {
      // Try FLUX.1-dev first, fallback to other models if credits exceeded
      let imageBlob;

      try {
        // Primary model: FLUX.1-dev (best quality)
        imageBlob = await hf.textToImage({
          inputs: prompt,
          model: 'black-forest-labs/FLUX.1-dev',
          parameters: {
            guidance_scale: 7.5,
            num_inference_steps: 28,
            width: 1024,
            height: 1024,
          },
        });
      } catch (primaryError: any) {
        if (primaryError.message?.includes('exceeded')) {
          // Try fallback model with potentially higher free limits
          console.log('FLUX.1-dev credits exceeded, trying fallback model...');

          try {
            imageBlob = await hf.textToImage({
              inputs: prompt,
              model: 'stabilityai/stable-diffusion-xl-base-1.0',
              parameters: {
                guidance_scale: 7.5,
                num_inference_steps: 20,
                width: 1024,
                height: 1024,
              },
            });
          } catch (fallbackError: any) {
            if (fallbackError.message?.includes('exceeded')) {
              // Try another fallback
              console.log('SDXL credits exceeded, trying final fallback...');

              imageBlob = await hf.textToImage({
                inputs: prompt,
                model: 'runwayml/stable-diffusion-v1-5',
                parameters: {
                  guidance_scale: 7.5,
                  num_inference_steps: 20,
                  width: 512,
                  height: 512,
                },
              });
            } else {
              throw fallbackError;
            }
          }
        } else {
          throw primaryError;
        }
      }

      // Convert blob to base64 for sending to client
      const arrayBuffer = await imageBlob.arrayBuffer();
      const base64 = Buffer.from(arrayBuffer).toString('base64');
      const imageUrl = `data:image/png;base64,${base64}`;

      return NextResponse.json({
        imageUrl,
        prompt,
        timestamp: Date.now(),
      });

    } catch (hfError: any) {
      console.error('Hugging Face API error:', hfError);

      if (hfError.message?.includes('exceeded')) {
        return NextResponse.json(
          {
            error: 'All available AI models have exceeded their monthly free credits. Please try again next month or upgrade to a paid plan for unlimited access.',
            suggestion: 'Consider upgrading your Hugging Face account to PRO for 20x more monthly credits and faster generation.'
          },
          { status: 402 }
        );
      }

      if (hfError.message?.includes('loading')) {
        return NextResponse.json(
          { error: 'AI model is currently loading. Please try again in a few moments.' },
          { status: 503 }
        );
      }

      if (hfError.message?.includes('rate limit')) {
        return NextResponse.json(
          { error: 'Rate limit exceeded. Please wait a moment before trying again.' },
          { status: 429 }
        );
      }

      return NextResponse.json(
        { error: `Failed to generate image: ${hfError.message || 'Unknown error'}` },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in generate-image API:', error);
    return NextResponse.json(
      { error: 'Internal server error. Please try again.' },
      { status: 500 }
    );
  }
}
