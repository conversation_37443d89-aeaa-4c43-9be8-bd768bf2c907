"use client";
import Image from "next/image";
import Link from "next/link";
import { useState, useEffect } from "react";

interface ModalImage {
  src: string;
  alt: string;
  title: string;
  description: string;
}

export default function Gallery() {
  const [selectedImage, setSelectedImage] = useState<ModalImage | null>(null);

  const openModal = (image: ModalImage) => {
    setSelectedImage(image);
  };

  const closeModal = () => {
    setSelectedImage(null);
  };

  // Handle keyboard events
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && selectedImage) {
        closeModal();
      }
    };

    if (selectedImage) {
      document.addEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'hidden'; // Prevent background scrolling
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [selectedImage]);
  return (
    <main className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b border-forest-green-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <Link href="/">
                <Image
                  src="/images/modern-haven-logo.png"
                  alt="Modern Haven Logo"
                  width={120}
                  height={40}
                  className="h-10 w-auto"
                />
              </Link>
              <Link href="/" className="text-2xl font-bold text-forest-green-600">Modern Haven</Link>
            </div>
            <div className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-4">
                <Link href="/" className="text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium">Home</Link>
                <Link href="#about" className="text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium">About</Link>
                <Link href="#services" className="text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium">Services</Link>
                <Link href="/gallery" className="bg-gold-500 text-white px-3 py-2 rounded-md text-sm font-medium">Gallery</Link>
                <Link href="/ai-design" className="text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium">AI Design</Link>
                <Link href="#contact" className="text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium">Contact</Link>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-forest-green-50 to-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-forest-green-700 mb-6">
              Our <span className="text-gold-500">Gallery</span>
            </h1>
            <p className="text-xl text-forest-green-600 mb-8 max-w-3xl mx-auto">
              Explore our diverse collections of interior design projects, from modern kitchens to organized storage solutions
            </p>
          </div>
        </div>
      </section>

      {/* Kitchen Reconstruction Collection */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-forest-green-700 mb-6 text-center">
              Kitchen <span className="text-gold-500">Reconstruction</span>
            </h2>
            <p className="text-lg text-forest-green-600 text-center max-w-4xl mx-auto mb-12">
              Witness the complete transformation of a kitchen space from start to finish. This collection showcases
              our expertise in full-scale kitchen renovations, featuring modern design principles, functional layouts,
              and premium finishes that create the heart of the home.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {/* Kitchen Reconstruction Images */}
              <div
                className="group cursor-pointer"
                onClick={() => openModal({
                  src: "/images/kitchen-reconstruction/IMG_7906.jpg",
                  alt: "Kitchen Reconstruction - Initial Phase",
                  title: "Initial Phase",
                  description: "The beginning of our comprehensive kitchen renovation project. This phase involves planning, measurements, and preparing the space for transformation."
                })}
              >
                <div className="bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4">
                  <Image
                    src="/images/kitchen-reconstruction/IMG_7906.jpg"
                    alt="Kitchen Reconstruction - Initial Phase"
                    width={400}
                    height={320}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <h3 className="text-lg font-semibold text-forest-green-700 mb-2">Initial Phase</h3>
                <p className="text-forest-green-600">The beginning of our comprehensive kitchen renovation project.</p>
              </div>

              <div
                className="group cursor-pointer"
                onClick={() => openModal({
                  src: "/images/kitchen-reconstruction/IMG_7908.jpg",
                  alt: "Kitchen Reconstruction - Demolition",
                  title: "Demolition Phase",
                  description: "Careful removal of existing structures to prepare for the new design. This phase requires precision to preserve structural integrity while clearing space for improvements."
                })}
              >
                <div className="bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4">
                  <Image
                    src="/images/kitchen-reconstruction/IMG_7908.jpg"
                    alt="Kitchen Reconstruction - Demolition"
                    width={400}
                    height={320}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <h3 className="text-lg font-semibold text-forest-green-700 mb-2">Demolition Phase</h3>
                <p className="text-forest-green-600">Careful removal of existing structures to prepare for the new design.</p>
              </div>

              <div
                className="group cursor-pointer"
                onClick={() => openModal({
                  src: "/images/kitchen-reconstruction/IMG_7910.jpg",
                  alt: "Kitchen Reconstruction - Structural Work",
                  title: "Structural Work",
                  description: "Foundation and structural improvements for the new kitchen layout. This phase includes electrical, plumbing, and framework modifications to support the new design."
                })}
              >
                <div className="bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4">
                  <Image
                    src="/images/kitchen-reconstruction/IMG_7910.jpg"
                    alt="Kitchen Reconstruction - Structural Work"
                    width={400}
                    height={320}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <h3 className="text-lg font-semibold text-forest-green-700 mb-2">Structural Work</h3>
                <p className="text-forest-green-600">Foundation and structural improvements for the new kitchen layout.</p>
              </div>

              <div
                className="group cursor-pointer"
                onClick={() => openModal({
                  src: "/images/kitchen-reconstruction/IMG_7911.jpg",
                  alt: "Kitchen Reconstruction - Installation",
                  title: "Installation Phase",
                  description: "Installing cabinets, countertops, and essential kitchen elements. This phase brings the design to life with custom cabinetry and premium materials."
                })}
              >
                <div className="bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4">
                  <Image
                    src="/images/kitchen-reconstruction/IMG_7911.jpg"
                    alt="Kitchen Reconstruction - Installation"
                    width={400}
                    height={320}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <h3 className="text-lg font-semibold text-forest-green-700 mb-2">Installation Phase</h3>
                <p className="text-forest-green-600">Installing cabinets, countertops, and essential kitchen elements.</p>
              </div>

              <div
                className="group cursor-pointer"
                onClick={() => openModal({
                  src: "/images/kitchen-reconstruction/IMG_7913.jpg",
                  alt: "Kitchen Reconstruction - Finishing Touches",
                  title: "Finishing Touches",
                  description: "Adding the final details that bring the design vision to life. This includes hardware installation, lighting fixtures, and decorative elements."
                })}
              >
                <div className="bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4">
                  <Image
                    src="/images/kitchen-reconstruction/IMG_7913.jpg"
                    alt="Kitchen Reconstruction - Finishing Touches"
                    width={400}
                    height={320}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <h3 className="text-lg font-semibold text-forest-green-700 mb-2">Finishing Touches</h3>
                <p className="text-forest-green-600">Adding the final details that bring the design vision to life.</p>
              </div>

              <div
                className="group cursor-pointer"
                onClick={() => openModal({
                  src: "/images/kitchen-reconstruction/IMG_7915.jpg",
                  alt: "Kitchen Reconstruction - Completed",
                  title: "Final Result",
                  description: "The stunning completed kitchen transformation. A perfect blend of functionality and style, showcasing modern design principles and premium craftsmanship."
                })}
              >
                <div className="bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4">
                  <Image
                    src="/images/kitchen-reconstruction/IMG_7915.jpg"
                    alt="Kitchen Reconstruction - Completed"
                    width={400}
                    height={320}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <h3 className="text-lg font-semibold text-forest-green-700 mb-2">Final Result</h3>
                <p className="text-forest-green-600">The stunning completed kitchen transformation.</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Modern Kitchen Collection */}
      <section className="py-20 bg-forest-green-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-forest-green-700 mb-6 text-center">
              Modern <span className="text-gold-500">Kitchen Designs</span>
            </h2>
            <p className="text-lg text-forest-green-600 text-center max-w-4xl mx-auto mb-12">
              Our collection of contemporary kitchen designs featuring sleek lines, innovative storage solutions,
              and cutting-edge appliances. Each design balances functionality with aesthetic appeal to create
              the perfect culinary workspace.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {/* Modern Kitchen Images */}
              <div
                className="group cursor-pointer"
                onClick={() => openModal({
                  src: "/images/Kitchen/IMG_6009.PNG",
                  alt: "Modern Kitchen Design 1",
                  title: "Minimalist Excellence",
                  description: "Clean lines and neutral tones create a serene cooking environment. This design emphasizes simplicity and functionality with hidden storage solutions and streamlined appliances."
                })}
              >
                <div className="bg-white rounded-lg overflow-hidden h-80 mb-4 shadow-lg">
                  <Image
                    src="/images/Kitchen/IMG_6009.PNG"
                    alt="Modern Kitchen Design 1"
                    width={400}
                    height={320}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <h3 className="text-lg font-semibold text-forest-green-700 mb-2">Minimalist Excellence</h3>
                <p className="text-forest-green-600">Clean lines and neutral tones create a serene cooking environment.</p>
              </div>

              <div
                className="group cursor-pointer"
                onClick={() => openModal({
                  src: "/images/Kitchen/IMG_6010.PNG",
                  alt: "Modern Kitchen Design 2",
                  title: "Contemporary Elegance",
                  description: "Sophisticated design with premium materials and finishes. Features high-end appliances, custom cabinetry, and elegant lighting that creates a luxurious cooking experience."
                })}
              >
                <div className="bg-white rounded-lg overflow-hidden h-80 mb-4 shadow-lg">
                  <Image
                    src="/images/Kitchen/IMG_6010.PNG"
                    alt="Modern Kitchen Design 2"
                    width={400}
                    height={320}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <h3 className="text-lg font-semibold text-forest-green-700 mb-2">Contemporary Elegance</h3>
                <p className="text-forest-green-600">Sophisticated design with premium materials and finishes.</p>
              </div>

              <div
                className="group cursor-pointer"
                onClick={() => openModal({
                  src: "/images/Kitchen/IMG_6711.JPG",
                  alt: "Modern Kitchen Design 3",
                  title: "Functional Beauty",
                  description: "Perfect blend of style and practicality for everyday living. Smart storage solutions and ergonomic design make cooking and entertaining effortless and enjoyable."
                })}
              >
                <div className="bg-white rounded-lg overflow-hidden h-80 mb-4 shadow-lg">
                  <Image
                    src="/images/Kitchen/IMG_6711.JPG"
                    alt="Modern Kitchen Design 3"
                    width={400}
                    height={320}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <h3 className="text-lg font-semibold text-forest-green-700 mb-2">Functional Beauty</h3>
                <p className="text-forest-green-600">Perfect blend of style and practicality for everyday living.</p>
              </div>

              <div
                className="group cursor-pointer"
                onClick={() => openModal({
                  src: "/images/Kitchen/IMG_6712.JPG",
                  alt: "Modern Kitchen Design 4",
                  title: "Luxury Living",
                  description: "High-end appliances and custom cabinetry for the discerning homeowner. Premium materials and sophisticated finishes create an upscale culinary environment."
                })}
              >
                <div className="bg-white rounded-lg overflow-hidden h-80 mb-4 shadow-lg">
                  <Image
                    src="/images/Kitchen/IMG_6712.JPG"
                    alt="Modern Kitchen Design 4"
                    width={400}
                    height={320}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <h3 className="text-lg font-semibold text-forest-green-700 mb-2">Luxury Living</h3>
                <p className="text-forest-green-600">High-end appliances and custom cabinetry for the discerning homeowner.</p>
              </div>

              <div
                className="group cursor-pointer"
                onClick={() => openModal({
                  src: "/images/Kitchen/IMG_6713.JPG",
                  alt: "Modern Kitchen Design 5",
                  title: "Smart Design",
                  description: "Innovative storage solutions maximize space and efficiency. Clever organization systems and multi-functional elements make the most of every square inch."
                })}
              >
                <div className="bg-white rounded-lg overflow-hidden h-80 mb-4 shadow-lg">
                  <Image
                    src="/images/Kitchen/IMG_6713.JPG"
                    alt="Modern Kitchen Design 5"
                    width={400}
                    height={320}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <h3 className="text-lg font-semibold text-forest-green-700 mb-2">Smart Design</h3>
                <p className="text-forest-green-600">Innovative storage solutions maximize space and efficiency.</p>
              </div>

              <div
                className="group cursor-pointer"
                onClick={() => openModal({
                  src: "/images/Kitchen/IMG_6714.JPG",
                  alt: "Modern Kitchen Design 6",
                  title: "Timeless Appeal",
                  description: "Classic design elements with modern functionality. This kitchen combines traditional aesthetics with contemporary conveniences for enduring style and performance."
                })}
              >
                <div className="bg-white rounded-lg overflow-hidden h-80 mb-4 shadow-lg">
                  <Image
                    src="/images/Kitchen/IMG_6714.JPG"
                    alt="Modern Kitchen Design 6"
                    width={400}
                    height={320}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <h3 className="text-lg font-semibold text-forest-green-700 mb-2">Timeless Appeal</h3>
                <p className="text-forest-green-600">Classic design elements with modern functionality.</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Closet Organization Collection */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-forest-green-700 mb-6 text-center">
              Closet <span className="text-gold-500">Organization</span>
            </h2>
            <p className="text-lg text-forest-green-600 text-center max-w-4xl mx-auto mb-12">
              Transform your storage spaces with our custom closet organization solutions. From walk-in wardrobes
              to compact storage areas, we create systems that maximize space while maintaining style and accessibility.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {/* Closet Organization Images */}
              <div
                className="group cursor-pointer"
                onClick={() => openModal({
                  src: "/images/Closet/IMG_6003.PNG",
                  alt: "Closet Organization 1",
                  title: "Walk-in Wardrobe",
                  description: "Luxurious walk-in closet with custom shelving and hanging solutions. Features dedicated spaces for different clothing types, shoes, and accessories with elegant lighting."
                })}
              >
                <div className="bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4">
                  <Image
                    src="/images/Closet/IMG_6003.PNG"
                    alt="Closet Organization 1"
                    width={400}
                    height={320}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <h3 className="text-lg font-semibold text-forest-green-700 mb-2">Walk-in Wardrobe</h3>
                <p className="text-forest-green-600">Luxurious walk-in closet with custom shelving and hanging solutions.</p>
              </div>

              <div
                className="group cursor-pointer"
                onClick={() => openModal({
                  src: "/images/Closet/IMG_6004.PNG",
                  alt: "Closet Organization 2",
                  title: "Organized Storage",
                  description: "Efficient storage system with designated spaces for every item. Smart organization solutions that make finding and storing belongings effortless and intuitive."
                })}
              >
                <div className="bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4">
                  <Image
                    src="/images/Closet/IMG_6004.PNG"
                    alt="Closet Organization 2"
                    width={400}
                    height={320}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <h3 className="text-lg font-semibold text-forest-green-700 mb-2">Organized Storage</h3>
                <p className="text-forest-green-600">Efficient storage system with designated spaces for every item.</p>
              </div>

              <div
                className="group cursor-pointer"
                onClick={() => openModal({
                  src: "/images/Closet/IMG_6005.PNG",
                  alt: "Closet Organization 3",
                  title: "Custom Solutions",
                  description: "Tailored organization systems designed for your specific needs. Every element is customized to fit your lifestyle, space constraints, and storage requirements."
                })}
              >
                <div className="bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4">
                  <Image
                    src="/images/Closet/IMG_6005.PNG"
                    alt="Closet Organization 3"
                    width={400}
                    height={320}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <h3 className="text-lg font-semibold text-forest-green-700 mb-2">Custom Solutions</h3>
                <p className="text-forest-green-600">Tailored organization systems designed for your specific needs.</p>
              </div>

              <div
                className="group cursor-pointer"
                onClick={() => openModal({
                  src: "/images/Closet/IMG_6006.PNG",
                  alt: "Closet Organization 4",
                  title: "Space Optimization",
                  description: "Maximizing every inch of available space with smart design. Vertical storage solutions and multi-level organization create maximum capacity in minimal footprint."
                })}
              >
                <div className="bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4">
                  <Image
                    src="/images/Closet/IMG_6006.PNG"
                    alt="Closet Organization 4"
                    width={400}
                    height={320}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <h3 className="text-lg font-semibold text-forest-green-700 mb-2">Space Optimization</h3>
                <p className="text-forest-green-600">Maximizing every inch of available space with smart design.</p>
              </div>

              <div
                className="group cursor-pointer"
                onClick={() => openModal({
                  src: "/images/Closet/IMG_6007.PNG",
                  alt: "Closet Organization 5",
                  title: "Elegant Design",
                  description: "Beautiful and functional closet design that complements your home. Sophisticated finishes and thoughtful details create a luxurious storage experience."
                })}
              >
                <div className="bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4">
                  <Image
                    src="/images/Closet/IMG_6007.PNG"
                    alt="Closet Organization 5"
                    width={400}
                    height={320}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <h3 className="text-lg font-semibold text-forest-green-700 mb-2">Elegant Design</h3>
                <p className="text-forest-green-600">Beautiful and functional closet design that complements your home.</p>
              </div>

              <div
                className="group cursor-pointer"
                onClick={() => openModal({
                  src: "/images/Closet/IMG_6008.PNG",
                  alt: "Closet Organization 6",
                  title: "Complete Organization",
                  description: "Comprehensive storage solution for all your belongings. Every item has its place in this thoughtfully designed organization system that maintains order and accessibility."
                })}
              >
                <div className="bg-forest-green-100 rounded-lg overflow-hidden h-80 mb-4">
                  <Image
                    src="/images/Closet/IMG_6008.PNG"
                    alt="Closet Organization 6"
                    width={400}
                    height={320}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <h3 className="text-lg font-semibold text-forest-green-700 mb-2">Complete Organization</h3>
                <p className="text-forest-green-600">Comprehensive storage solution for all your belongings.</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 bg-forest-green-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to Start Your <span className="text-gold-400">Project?</span>
          </h2>
          <p className="text-lg text-forest-green-100 mb-8 max-w-2xl mx-auto">
            Let's bring your vision to life. Contact us today to discuss your interior design needs.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/#contact" className="bg-gold-500 text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-gold-600 transition duration-300">
              Get Started
            </Link>
            <Link href="/" className="border-2 border-white text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-white hover:text-forest-green-600 transition duration-300">
              Back to Home
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-forest-green-800 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <h3 className="text-2xl font-bold text-gold-400 mb-4">Modern Haven</h3>
              <p className="text-forest-green-200 mb-4">
                Creating beautiful, functional spaces that reflect your unique style and enhance your lifestyle.
                Your dream home is just a consultation away.
              </p>
              <div className="flex space-x-4">
                <a href="#" className="text-gold-400 hover:text-gold-300">📘</a>
                <a href="#" className="text-gold-400 hover:text-gold-300">📷</a>
                <a href="#" className="text-gold-400 hover:text-gold-300">🐦</a>
                <a href="#" className="text-gold-400 hover:text-gold-300">💼</a>
              </div>
            </div>
            <div>
              <h4 className="text-lg font-semibold text-gold-400 mb-4">Quick Links</h4>
              <ul className="space-y-2 text-forest-green-200">
                <li><Link href="/" className="hover:text-gold-400">Home</Link></li>
                <li><Link href="/#about" className="hover:text-gold-400">About</Link></li>
                <li><Link href="/#services" className="hover:text-gold-400">Services</Link></li>
                <li><Link href="/gallery" className="hover:text-gold-400">Gallery</Link></li>
                <li><Link href="/#contact" className="hover:text-gold-400">Contact</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold text-gold-400 mb-4">Collections</h4>
              <ul className="space-y-2 text-forest-green-200">
                <li>Kitchen Reconstruction</li>
                <li>Modern Kitchen Designs</li>
                <li>Closet Organization</li>
                <li>Custom Storage Solutions</li>
                <li>Interior Styling</li>
              </ul>
            </div>
          </div>
          <div className="border-t border-forest-green-700 mt-8 pt-8 text-center text-forest-green-200">
            <p>&copy; 2024 Modern Haven Interior Design. All rights reserved.</p>
          </div>
        </div>
      </footer>

      {/* Image Modal */}
      {selectedImage && (
        <div
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4"
          onClick={closeModal}
        >
          <div
            className="relative max-w-4xl max-h-full bg-white rounded-lg overflow-hidden"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Close button */}
            <button
              onClick={closeModal}
              className="absolute top-4 right-4 z-10 bg-forest-green-600 text-white rounded-full w-10 h-10 flex items-center justify-center hover:bg-forest-green-700 transition-colors"
            >
              ✕
            </button>

            {/* Image */}
            <div className="relative">
              <Image
                src={selectedImage.src}
                alt={selectedImage.alt}
                width={1200}
                height={800}
                className="w-full h-auto max-h-[80vh] object-contain"
              />
            </div>

            {/* Image info */}
            <div className="p-6 bg-white">
              <h3 className="text-2xl font-bold text-forest-green-700 mb-2">{selectedImage.title}</h3>
              <p className="text-forest-green-600">{selectedImage.description}</p>
            </div>
          </div>
        </div>
      )}
    </main>
  );
}
