import Image from "next/image";

export default function Home() {
  return (
    <main className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b border-forest-green-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Image
                src="/images/modern-haven-logo.png"
                alt="Modern Haven Logo"
                width={120}
                height={40}
                className="h-10 w-auto"
              />
            </div>
            <div className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-4">
                <a href="#home" className="text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium">Home</a>
                <a href="#about" className="text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium">About</a>
                <a href="#services" className="text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium">Services</a>
                <a href="#portfolio" className="text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium">Portfolio</a>
                <a href="/gallery" className="text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium">Gallery</a>
                <a href="/ai-design" className="text-forest-green-600 hover:text-gold-500 px-3 py-2 rounded-md text-sm font-medium">AI Design</a>
                <a href="#contact" className="bg-gold-500 text-white hover:bg-gold-600 px-4 py-2 rounded-md text-sm font-medium">Contact</a>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section id="home" className="relative bg-gradient-to-br from-forest-green-50 to-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-forest-green-700 mb-6">
              Welcome to <span className="text-gold-500">Modern Haven</span>
            </h1>
            <p className="text-xl md:text-2xl text-forest-green-600 mb-8 max-w-3xl mx-auto">
              Transform your space into a sanctuary of style and comfort with our expert interior design services
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-gold-500 text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-gold-600 transition duration-300">
                Start Your Project
              </button>
              <button className="border-2 border-forest-green-600 text-forest-green-600 px-8 py-3 rounded-lg text-lg font-semibold hover:bg-forest-green-600 hover:text-white transition duration-300">
                View Portfolio
              </button>
            </div>
          </div>
        </div>
        <div className="absolute bottom-0 left-0 right-0 h-20 bg-gradient-to-t from-white to-transparent"></div>
      </section>

      {/* About Section */}
      <section id="about" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-forest-green-700 mb-6">
                Creating Spaces That <span className="text-gold-500">Inspire</span>
              </h2>
              <p className="text-lg text-forest-green-600 mb-6">
                At Modern Haven, we believe that your home should be a reflection of your personality and lifestyle.
                Our team of experienced designers specializes in creating beautiful, functional spaces that blend
                contemporary elegance with timeless comfort.
              </p>
              <p className="text-lg text-forest-green-600 mb-8">
                With over a decade of experience in residential and commercial design, we bring creativity,
                expertise, and attention to detail to every project, no matter the size or scope.
              </p>
              <div className="grid grid-cols-2 gap-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-gold-500 mb-2">150+</div>
                  <div className="text-forest-green-600">Projects Completed</div>
                </div>
                <div className="text-center">
                  <div className="text-3xl font-bold text-gold-500 mb-2">10+</div>
                  <div className="text-forest-green-600">Years Experience</div>
                </div>
              </div>
            </div>
            <div className="bg-forest-green-100 rounded-lg overflow-hidden h-96">
              <Image
                src="/images/kitchen-reconstruction/IMG_7906.jpg"
                alt="Modern Kitchen Design - Completed Project"
                width={600}
                height={400}
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="py-20 bg-forest-green-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-forest-green-700 mb-4">
              Our <span className="text-gold-500">Services</span>
            </h2>
            <p className="text-lg text-forest-green-600 max-w-2xl mx-auto">
              From concept to completion, we offer comprehensive interior design services
              tailored to your unique needs and vision.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Service 1 */}
            <div className="bg-white rounded-lg p-8 shadow-lg hover:shadow-xl transition duration-300">
              <div className="text-4xl text-gold-500 mb-4">🛋️</div>
              <h3 className="text-xl font-bold text-forest-green-700 mb-4">Residential Design</h3>
              <p className="text-forest-green-600">
                Complete home makeovers, room redesigns, and space planning to create your perfect living environment.
              </p>
            </div>

            {/* Service 2 */}
            <div className="bg-white rounded-lg p-8 shadow-lg hover:shadow-xl transition duration-300">
              <div className="text-4xl text-gold-500 mb-4">🏢</div>
              <h3 className="text-xl font-bold text-forest-green-700 mb-4">Commercial Spaces</h3>
              <p className="text-forest-green-600">
                Professional office design, retail spaces, and hospitality interiors that enhance productivity and customer experience.
              </p>
            </div>

            {/* Service 3 */}
            <div className="bg-white rounded-lg p-8 shadow-lg hover:shadow-xl transition duration-300">
              <div className="text-4xl text-gold-500 mb-4">🎨</div>
              <h3 className="text-xl font-bold text-forest-green-700 mb-4">Color Consultation</h3>
              <p className="text-forest-green-600">
                Expert color selection and palette development to create the perfect mood and atmosphere for your space.
              </p>
            </div>

            {/* Service 4 */}
            <div className="bg-white rounded-lg p-8 shadow-lg hover:shadow-xl transition duration-300">
              <div className="text-4xl text-gold-500 mb-4">🪑</div>
              <h3 className="text-xl font-bold text-forest-green-700 mb-4">Furniture Selection</h3>
              <p className="text-forest-green-600">
                Curated furniture and decor selection that perfectly complements your style and functional needs.
              </p>
            </div>

            {/* Service 5 */}
            <div className="bg-white rounded-lg p-8 shadow-lg hover:shadow-xl transition duration-300">
              <div className="text-4xl text-gold-500 mb-4">📐</div>
              <h3 className="text-xl font-bold text-forest-green-700 mb-4">Space Planning</h3>
              <p className="text-forest-green-600">
                Optimize your space layout for maximum functionality, flow, and aesthetic appeal.
              </p>
            </div>

            {/* Service 6 */}
            <div className="bg-white rounded-lg p-8 shadow-lg hover:shadow-xl transition duration-300">
              <div className="text-4xl text-gold-500 mb-4">🔧</div>
              <h3 className="text-xl font-bold text-forest-green-700 mb-4">Project Management</h3>
              <p className="text-forest-green-600">
                Full project coordination from design to installation, ensuring seamless execution and timely completion.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Portfolio Section */}
      <section id="portfolio" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-forest-green-700 mb-4">
              Our <span className="text-gold-500">Portfolio</span>
            </h2>
            <p className="text-lg text-forest-green-600 max-w-2xl mx-auto">
              Explore some of our recent projects and see how we transform spaces into beautiful, functional environments.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Portfolio Item 1 */}
            <div className="group cursor-pointer">
              <div className="bg-forest-green-100 rounded-lg overflow-hidden h-64 mb-4">
                <Image
                  src="/images/kitchen-reconstruction/IMG_7906.jpg"
                  alt="Kitchen Reconstruction - Phase 1"
                  width={400}
                  height={300}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
              </div>
              <h3 className="text-lg font-semibold text-forest-green-700 mb-2">Kitchen Reconstruction - Phase 1</h3>
              <p className="text-forest-green-600">Complete kitchen renovation showcasing the transformation process.</p>
            </div>

            {/* Portfolio Item 2 */}
            <div className="group cursor-pointer">
              <div className="bg-forest-green-100 rounded-lg overflow-hidden h-64 mb-4">
                <Image
                  src="/images/kitchen-reconstruction/IMG_7908.jpg"
                  alt="Kitchen Reconstruction - Phase 2"
                  width={400}
                  height={300}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
              </div>
              <h3 className="text-lg font-semibold text-forest-green-700 mb-2">Kitchen Reconstruction - Phase 2</h3>
              <p className="text-forest-green-600">Mid-renovation progress showing structural improvements.</p>
            </div>

            {/* Portfolio Item 3 */}
            <div className="group cursor-pointer">
              <div className="bg-forest-green-100 rounded-lg overflow-hidden h-64 mb-4">
                <Image
                  src="/images/kitchen-reconstruction/IMG_7910.jpg"
                  alt="Kitchen Reconstruction - Phase 3"
                  width={400}
                  height={300}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
              </div>
              <h3 className="text-lg font-semibold text-forest-green-700 mb-2">Kitchen Reconstruction - Phase 3</h3>
              <p className="text-forest-green-600">Advanced renovation stage with cabinet installation.</p>
            </div>

            {/* Portfolio Item 4 */}
            <div className="group cursor-pointer">
              <div className="bg-forest-green-100 rounded-lg overflow-hidden h-64 mb-4">
                <Image
                  src="/images/kitchen-reconstruction/IMG_7911.jpg"
                  alt="Kitchen Reconstruction - Phase 4"
                  width={400}
                  height={300}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
              </div>
              <h3 className="text-lg font-semibold text-forest-green-700 mb-2">Kitchen Reconstruction - Phase 4</h3>
              <p className="text-forest-green-600">Near completion with appliances and finishing touches.</p>
            </div>

            {/* Portfolio Item 5 */}
            <div className="group cursor-pointer">
              <div className="bg-forest-green-100 rounded-lg overflow-hidden h-64 mb-4">
                <Image
                  src="/images/kitchen-reconstruction/IMG_7913.jpg"
                  alt="Kitchen Reconstruction - Phase 5"
                  width={400}
                  height={300}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
              </div>
              <h3 className="text-lg font-semibold text-forest-green-700 mb-2">Kitchen Reconstruction - Final Stage</h3>
              <p className="text-forest-green-600">Final touches and styling of the completed kitchen renovation.</p>
            </div>

            {/* Portfolio Item 6 */}
            <div className="group cursor-pointer">
              <div className="bg-forest-green-100 rounded-lg overflow-hidden h-64 mb-4">
                <Image
                  src="/images/kitchen-reconstruction/IMG_7915.jpg"
                  alt="Kitchen Reconstruction - Completed"
                  width={400}
                  height={300}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
              </div>
              <h3 className="text-lg font-semibold text-forest-green-700 mb-2">Complete Kitchen Transformation</h3>
              <p className="text-forest-green-600">The finished kitchen reconstruction showcasing modern design excellence.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section id="contact" className="py-20 bg-forest-green-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Ready to Transform Your <span className="text-gold-400">Space?</span>
            </h2>
            <p className="text-lg text-forest-green-100 max-w-2xl mx-auto">
              Let's discuss your vision and create something beautiful together.
              Contact us today for a consultation.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div className="bg-white rounded-lg p-8">
              <h3 className="text-2xl font-bold text-forest-green-700 mb-6">Get In Touch</h3>
              <form className="space-y-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-forest-green-700 mb-2">
                    Full Name
                  </label>
                  <input
                    type="text"
                    id="name"
                    className="w-full px-4 py-2 border border-forest-green-200 rounded-lg focus:ring-2 focus:ring-gold-500 focus:border-transparent"
                    placeholder="Your Name"
                  />
                </div>
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-forest-green-700 mb-2">
                    Email Address
                  </label>
                  <input
                    type="email"
                    id="email"
                    className="w-full px-4 py-2 border border-forest-green-200 rounded-lg focus:ring-2 focus:ring-gold-500 focus:border-transparent"
                    placeholder="<EMAIL>"
                  />
                </div>
                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-forest-green-700 mb-2">
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    className="w-full px-4 py-2 border border-forest-green-200 rounded-lg focus:ring-2 focus:ring-gold-500 focus:border-transparent"
                    placeholder="(*************"
                  />
                </div>
                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-forest-green-700 mb-2">
                    Project Details
                  </label>
                  <textarea
                    id="message"
                    rows={4}
                    className="w-full px-4 py-2 border border-forest-green-200 rounded-lg focus:ring-2 focus:ring-gold-500 focus:border-transparent"
                    placeholder="Tell us about your project..."
                  ></textarea>
                </div>
                <button
                  type="submit"
                  className="w-full bg-gold-500 text-white py-3 rounded-lg font-semibold hover:bg-gold-600 transition duration-300"
                >
                  Send Message
                </button>
              </form>
            </div>

            {/* Contact Information */}
            <div className="text-white">
              <h3 className="text-2xl font-bold mb-6">Contact Information</h3>
              <div className="space-y-6">
                <div className="flex items-start">
                  <div className="text-gold-400 text-xl mr-4 mt-1">📍</div>
                  <div>
                    <h4 className="font-semibold mb-1">Address</h4>
                    <p className="text-forest-green-100">
                      123 Design Street<br />
                      Creative District<br />
                      City, State 12345
                    </p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="text-gold-400 text-xl mr-4 mt-1">📞</div>
                  <div>
                    <h4 className="font-semibold mb-1">Phone</h4>
                    <p className="text-forest-green-100">(*************</p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="text-gold-400 text-xl mr-4 mt-1">✉️</div>
                  <div>
                    <h4 className="font-semibold mb-1">Email</h4>
                    <p className="text-forest-green-100"><EMAIL></p>
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="text-gold-400 text-xl mr-4 mt-1">🕒</div>
                  <div>
                    <h4 className="font-semibold mb-1">Business Hours</h4>
                    <p className="text-forest-green-100">
                      Monday - Friday: 9:00 AM - 6:00 PM<br />
                      Saturday: 10:00 AM - 4:00 PM<br />
                      Sunday: By Appointment
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-forest-green-800 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <h3 className="text-2xl font-bold text-gold-400 mb-4">Modern Haven</h3>
              <p className="text-forest-green-200 mb-4">
                Creating beautiful, functional spaces that reflect your unique style and enhance your lifestyle.
                Your dream home is just a consultation away.
              </p>
              <div className="flex space-x-4">
                <a href="#" className="text-gold-400 hover:text-gold-300">📘</a>
                <a href="#" className="text-gold-400 hover:text-gold-300">📷</a>
                <a href="#" className="text-gold-400 hover:text-gold-300">🐦</a>
                <a href="#" className="text-gold-400 hover:text-gold-300">💼</a>
              </div>
            </div>
            <div>
              <h4 className="text-lg font-semibold text-gold-400 mb-4">Quick Links</h4>
              <ul className="space-y-2 text-forest-green-200">
                <li><a href="#home" className="hover:text-gold-400">Home</a></li>
                <li><a href="#about" className="hover:text-gold-400">About</a></li>
                <li><a href="#services" className="hover:text-gold-400">Services</a></li>
                <li><a href="#portfolio" className="hover:text-gold-400">Portfolio</a></li>
                <li><a href="#contact" className="hover:text-gold-400">Contact</a></li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold text-gold-400 mb-4">Services</h4>
              <ul className="space-y-2 text-forest-green-200">
                <li>Residential Design</li>
                <li>Commercial Spaces</li>
                <li>Color Consultation</li>
                <li>Space Planning</li>
                <li>Project Management</li>
              </ul>
            </div>
          </div>
          <div className="border-t border-forest-green-700 mt-8 pt-8 text-center text-forest-green-200">
            <p>&copy; 2024 Modern Haven Interior Design. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </main>
  );
}
